# OA订单导入处理示例

## 输入Excel文件示例

| 序号 | 订单编号-区分送货##bh | 客户名称##KUNAG | SAP客户编码##KUNWE | 电话##TEL_NUMBER | 联系人##NAME_CO | 综合地址信息##zhdzxx | 省代码##REGION | 省##s | 市##CITY | 区##DISTRICT | 地址信息##STREET | 物料号##MATNR | 物料名称##TXZ01 | 数量##KWMENG | 成本价##KBETR | 合计金额##hjje | 销售单位##VRKME | 备注##TEXT |
|------|-------------------|----------------|------------------|-----------------|----------------|-------------------|---------------|-------|----------|-------------|----------------|-------------|-------------|-------------|-------------|-------------|---------------|-----------|
| 1    | ORD001           | 客户A          | CUST001          | 13800138000     | 张三           | 北京市朝阳区      | 110000        | 北京   | 北京市    | 朝阳区       | 建国路1号       | Z001        | 组合商品A       | 2           | 100.00      | 200.00      | PCS           | 测试订单   |
| 2    | ORD002           | 客户B          | CUST002          | 13900139000     | 李四           | 上海市浦东新区    | 310000        | 上海   | 上海市    | 浦东新区     | 陆家嘴路2号     | P001        | 普通商品B       | 1           | 50.00       | 50.00       | PCS           | 普通商品   |

## 组合商品表数据示例

假设组合商品Z001包含以下子商品：

| ps_c_sku_ecode | title    | num | sellprice |
|----------------|----------|-----|-----------|
| SKU001         | 子商品1   | 1   | 60.00     |
| SKU002         | 子商品2   | 2   | 20.00     |

## 处理结果

### 情况1：所有组合商品都存在

**输出Excel文件：**

| 序号 | 订单编号-区分送货##bh | 客户名称##KUNAG | SAP客户编码##KUNWE | 电话##TEL_NUMBER | 联系人##NAME_CO | 综合地址信息##zhdzxx | 省代码##REGION | 省##s | 市##CITY | 区##DISTRICT | 地址信息##STREET | 物料号##MATNR | 物料名称##TXZ01 | 数量##KWMENG | 成本价##KBETR | 合计金额##hjje | 销售单位##VRKME | 备注##TEXT |
|------|-------------------|----------------|------------------|-----------------|----------------|-------------------|---------------|-------|----------|-------------|----------------|-------------|-------------|-------------|-------------|-------------|---------------|-----------|
| 1    | ORD001           | 客户A          | CUST001          | 13800138000     | 张三           | 北京市朝阳区      | 110000        | 北京   | 北京市    | 朝阳区       | 建国路1号       | SKU001      | 子商品1        | 2           | 60.00       | 120.00      | PCS           | 测试订单   |
| 1    | ORD001           | 客户A          | CUST001          | 13800138000     | 张三           | 北京市朝阳区      | 110000        | 北京   | 北京市    | 朝阳区       | 建国路1号       | SKU002      | 子商品2        | 4           | 20.00       | 80.00       | PCS           | 测试订单   |
| 2    | ORD002           | 客户B          | CUST002          | 13900139000     | 李四           | 上海市浦东新区    | 310000        | 上海   | 上海市    | 浦东新区     | 陆家嘴路2号     | P001        | 普通商品B       | 1           | 50.00       | 50.00       | PCS           | 普通商品   |

**说明：**
- 第1行（Z001组合商品）被展开为2行：
  - SKU001：数量 = 2 × 1 = 2，金额 = 2 × 60.00 = 120.00
  - SKU002：数量 = 2 × 2 = 4，金额 = 4 × 20.00 = 80.00
- 第2行（普通商品P001）保持不变
- 所有其他字段（订单编号、客户信息、地址等）完全保留

### 情况2：存在不存在的组合商品

如果Z001组合商品不存在，则生成错误文件：

| 序号 | 订单编号-区分送货##bh | 客户名称##KUNAG | SAP客户编码##KUNWE | 电话##TEL_NUMBER | 联系人##NAME_CO | 综合地址信息##zhdzxx | 省代码##REGION | 省##s | 市##CITY | 区##DISTRICT | 地址信息##STREET | 物料号##MATNR | 物料名称##TXZ01 | 数量##KWMENG | 成本价##KBETR | 合计金额##hjje | 销售单位##VRKME | 备注##TEXT | 错误信息 |
|------|-------------------|----------------|------------------|-----------------|----------------|-------------------|---------------|-------|----------|-------------|----------------|-------------|-------------|-------------|-------------|-------------|---------------|-----------|---------|
| 1    | ORD001           | 客户A          | CUST001          | 13800138000     | 张三           | 北京市朝阳区      | 110000        | 北京   | 北京市    | 朝阳区       | 建国路1号       | Z001        | 组合商品A       | 2           | 100.00      | 200.00      | PCS           | 测试订单   | 第2行组合商品不存在！ |

## 关键改进点

1. **完整字段保留**：所有原始Excel字段都被完整保留到输出文件中
2. **精确展开**：组合商品按照子商品数量正确展开为多行
3. **数量计算**：实际数量 = 原始数量 × 组合中子商品数量
4. **价格处理**：优先使用组合商品中的价格，否则使用原始价格重新计算
5. **错误处理**：错误文件包含完整的原始数据和错误信息

## API响应示例

### 成功响应
```json
{
  "success": true,
  "message": "处理成功",
  "exportFileUrl": "https://oss.example.com/order-import/result_20241201_103000.xlsx",
  "originalCount": 2,
  "expandedCount": 3
}
```

### 错误响应
```json
{
  "success": false,
  "message": "存在不存在的组合商品",
  "errorFileUrl": "https://oss.example.com/order-import/error_20241201_103000.xlsx",
  "missingProducts": ["Z001"]
}
```
