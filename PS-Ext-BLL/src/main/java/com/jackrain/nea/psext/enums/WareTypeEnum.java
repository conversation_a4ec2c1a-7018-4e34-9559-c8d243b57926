package com.jackrain.nea.psext.enums;

/**
 * sku 商品类型 枚举类  0 正常商品 1 福袋商品 2 组合商品 3 预售商品
 *
 * @author: 郑立轩
 * @since: 2019/4/8
 * create at : 2019/4/8 13:20
 */
public enum WareTypeEnum {
    normal(0), luck(1), group(2), presell(3), ERROR(-1);
    private Integer value;

    WareTypeEnum(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public static WareTypeEnum valueOf(Integer value) {
        for (WareTypeEnum type : WareTypeEnum.values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return ERROR;
    }


}
