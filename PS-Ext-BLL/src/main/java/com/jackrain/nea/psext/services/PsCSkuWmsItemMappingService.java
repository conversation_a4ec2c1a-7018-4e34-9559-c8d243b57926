package com.jackrain.nea.psext.services;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.psext.common.PsExtConstantsIF;
import com.jackrain.nea.psext.mapper.PsCSkuWmsItemMappingMapper;
import com.jackrain.nea.psext.model.table.PsCSkuWmsItemMapping;
import com.jackrain.nea.psext.request.PsCSkuWmsItemMappingSaveRequest;
import com.jackrain.nea.psext.utils.PsUtils;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * wms商品映射
 *
 * <AUTHOR>
 * @since 2023-04-03 19:52
 */
@Slf4j
@Service
public class PsCSkuWmsItemMappingService {
    @Resource
    private PsCSkuWmsItemMappingMapper psCSkuWmsItemMappingMapper;

    @Resource
    private PsCSkuQueryService psCSkuQueryService;


    public PsCSkuWmsItemMapping queryByECode(String customerId, String ecode) {
        if (StringUtils.isEmpty(customerId) || StringUtils.isEmpty(ecode)) {
            log.error(LogUtil.format("平台ID和商品编码必填，平台ID：{},WMS商品ID:{}", "PsCSkuWmsItemMappingService.queryByECode"),
                    customerId, ecode);
            throw new NDSException("平台ID和商品编码必填");
        }

        List<PsCSkuWmsItemMapping> mappingList = psCSkuWmsItemMappingMapper.selectList(new QueryWrapper<PsCSkuWmsItemMapping>().lambda()
                .eq(BaseModel::getIsactive, PsExtConstantsIF.IS_ACTIVE_Y)
                .eq(PsCSkuWmsItemMapping::getCustomerId, customerId)
                .eq(PsCSkuWmsItemMapping::getPsCSkuEcode, ecode));
        if (CollectionUtils.isEmpty(mappingList)) {
            return null;
        }
        if (mappingList.size() > 1) {
            log.error(LogUtil.format("WMS商品映射数据错误，平台ID：{},WMS商品ID:{}", "PsCSkuWmsItemMappingService.queryByECode"),
                    customerId, ecode);
            throw new NDSException("WMS商品映射数据错误");
        }
        return mappingList.get(0);
    }


    public PsCSkuWmsItemMapping queryByWmsItem(String customerId, String itemId) {
        if (StringUtils.isEmpty(customerId) || StringUtils.isEmpty(itemId)) {
            log.error(LogUtil.format("平台ID和WMS商品编码必填，平台ID：{},WMS商品ID:{}", "PsCSkuWmsItemMappingService.queryByWmsItem"),
                    customerId, itemId);
            throw new NDSException("平台ID和WMS商品编码必填");
        }

        List<PsCSkuWmsItemMapping> mappingList = psCSkuWmsItemMappingMapper.selectList(new QueryWrapper<PsCSkuWmsItemMapping>().lambda()
                .eq(BaseModel::getIsactive, PsExtConstantsIF.IS_ACTIVE_Y)
                .eq(PsCSkuWmsItemMapping::getCustomerId, customerId)
                .eq(PsCSkuWmsItemMapping::getWmsItemId, itemId));
        if (CollectionUtils.isEmpty(mappingList)) {
            return null;
        }
        if (mappingList.size() > 1) {
            log.error(LogUtil.format("WMS商品映射数据错误，平台ID：{},WMS商品ID:{}", "PsCSkuWmsItemMappingService.queryByWmsItem"),
                    customerId, itemId);
            throw new NDSException("WMS商品映射数据错误");
        }
        return mappingList.get(0);
    }

    public PsCSkuWmsItemMapping save(PsCSkuWmsItemMappingSaveRequest saveRequest) {
        checkSaveParam(saveRequest);
        PsCSkuWmsItemMapping exists = queryByECode(saveRequest.getCustomerId(), saveRequest.getSkuEcode());

        if (Objects.isNull(exists)) {
            return create(saveRequest);
        }

        if (StringUtils.equals(exists.getWmsItemId(), saveRequest.getWmsItemId())) {
            return exists;
        }

//        RedisOpsUtil.getObjRedisTemplate().delete(PsExtConstantsIF.ECODE_REDIS_KEY_PREFIX + saveRequest.getCustomerId() + ":" + saveRequest.getSkuEcode());
//        RedisOpsUtil.getObjRedisTemplate().delete(PsExtConstantsIF.ITEM_REDIS_KEY_PREFIX + saveRequest.getCustomerId() + ":" + saveRequest.getWmsItemId());

        exists.setWmsItemId(saveRequest.getWmsItemId());
        exists.setWmsType(saveRequest.getWmsType());
        exists.setRemark(saveRequest.getRemark());
        PsUtils.setBModelDefalutData(exists, SystemUserResource.getRootUser());
        psCSkuWmsItemMappingMapper.updateById(exists);
        return exists;
    }

    private PsCSkuWmsItemMapping create(PsCSkuWmsItemMappingSaveRequest saveRequest) {
        PsCSkuWmsItemMapping entity = new PsCSkuWmsItemMapping();
        entity.setId(ModelUtil.getSequence("PS_C_SKU_WMS_ITEM_MAPPING"));

        entity.setPsCSkuId(saveRequest.getPsCSkuId());
        entity.setPsCSkuEcode(saveRequest.getSkuEcode());
        entity.setPsCProEname(saveRequest.getPsCSkuEname());

        entity.setWmsType(saveRequest.getWmsType());
        entity.setCustomerId(saveRequest.getCustomerId());
        entity.setWmsItemId(saveRequest.getWmsItemId());
        entity.setRemark(saveRequest.getRemark());

        PsUtils.setBModelDefalutData(entity, SystemUserResource.getRootUser());
        psCSkuWmsItemMappingMapper.insert(entity);
        return entity;
    }

    private void checkSaveParam(PsCSkuWmsItemMappingSaveRequest saveRequest) {
        if (Objects.isNull(saveRequest)) {
            log.error(LogUtil.format("保存WMS商品映射出错，入参不能为空",
                    "PsCSkuWmsItemMappingService.checkSaveParam"));
            throw new NDSException("入参不能为空");
        }
        if (StringUtils.isEmpty(saveRequest.getSkuEcode())
                || StringUtils.isEmpty(saveRequest.getCustomerId())
                || Objects.isNull(saveRequest.getPsCSkuId())
                || StringUtils.isEmpty(saveRequest.getPsCSkuEname())
                || StringUtils.isEmpty(saveRequest.getWmsItemId())
                || StringUtils.isEmpty(saveRequest.getWmsType())) {
            log.error(LogUtil.format("保存WMS商品映射出错，必填参数不能为空，入参:{}",
                    "PsCSkuWmsItemMappingService.checkSaveParam"), JSON.toJSONString(saveRequest));
            throw new NDSException("必填参数不能为空");
        }
    }
}
