package com.jackrain.nea.psext.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.util.StringUtil;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.psext.common.PsExtConstantsIF;
import com.jackrain.nea.psext.enums.PlatformSyncStatusEnum;
import com.jackrain.nea.psext.mapper.PsCSamecityDetrimentRefMapper;
import com.jackrain.nea.psext.mapper.PsCSamecityDetrimentRefTmalltaskMapper;
import com.jackrain.nea.psext.model.table.PsCSamecityDetrimentRef;
import com.jackrain.nea.psext.model.table.PsCSamecityDetrimentRefTmalltask;
import com.jackrain.nea.psext.request.PsCSameCityDetrimentRefTmallTaskRequest;
import com.jackrain.nea.psext.result.PsCSameCityDetrimentRefTmallTaskResponse;
import com.jackrain.nea.psext.utils.HttpClientUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.utils.AssertUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class PushSameCityDetrimentRefToTmallService {
    @Resource
    private PsCSamecityDetrimentRefTmalltaskMapper psCSamecityDetrimentRefTmalltaskMapper;
    @Resource
    private PsCSamecityDetrimentRefMapper psCSamecityDetrimentRefMapper;
    @Autowired
    private PropertiesConf pconf;

    private final String urlProperty = "ps.push.samecitypurchaseref_to_tmall.url";
    private final String methodProperty = "ps.push.samecitypurchaseref_to_tmall.method";
    private final String tagProperty = "ps.push.samecitypurchaseref_to_tmall.tag";
    private final String limitProperty = "ps.push.samecitypurchaseref_to_tmall.limit";


    public ValueHolderV14 pushSameCityPurchaseRefToTmall() {
        ValueHolderV14 valueHolderV14 = new ValueHolderV14();

        if (StringUtil.isEmpty(pconf.getProperty(urlProperty))) {
            log.error(LogUtil.format("同步同城购商品请求地址为空"));
            valueHolderV14.setCode(-1);
            valueHolderV14.setMessage("请求地址为空，无法同步");
            return valueHolderV14;
        }
        Map map = getConfig();
        // 查询需要同步的数据对象
        List<PsCSamecityDetrimentRefTmalltask> psCSameCityPurchaseRefTmallTaskList = psCSamecityDetrimentRefTmalltaskMapper.selectPsCSameCityPurchaseRefTmallTaskList(map);

        if (CollectionUtils.isNotEmpty(psCSameCityPurchaseRefTmallTaskList)) {
            //更新同步状态为同步中
            updatePlatformSyncStatus(psCSameCityPurchaseRefTmallTaskList);

            String params = getRequestParams(psCSameCityPurchaseRefTmallTaskList);
            log.info(LogUtil.format("同步同城购商品请求报文：{}"), params);
            String repData = HttpClientUtil.sendPost(pconf.getProperty(urlProperty), params);
            log.info(LogUtil.format("同步同城购商品请求返回报文：{}"), repData);
            //分析报文具体结构解析
            if (StringUtil.isEmpty(repData)) {
                log.error(LogUtil.format("同步同城购商品请求返回报文为空无法解析"));
                valueHolderV14.setCode(-1);
                valueHolderV14.setMessage("返回报文为空,无法解析");
                return valueHolderV14;
            }
            List<PsCSameCityDetrimentRefTmallTaskResponse> psCSameCityDetrimentRefTmallTaskResponse
                    = JSONArray.parseArray(repData, PsCSameCityDetrimentRefTmallTaskResponse.class);
            if (CollectionUtils.isNotEmpty(psCSameCityDetrimentRefTmallTaskResponse)) {
                updateTaskInfo(psCSameCityDetrimentRefTmallTaskResponse, psCSameCityPurchaseRefTmallTaskList);
            }
            log.info(LogUtil.format("同步结束"));
        }
        valueHolderV14.setCode(0);
        valueHolderV14.setMessage("同步成功");
        return valueHolderV14;
    }

    /**
     * 更新记录
     *
     * @param result
     */
    private void updateTaskInfo(List<PsCSameCityDetrimentRefTmallTaskResponse> result
            , List<PsCSamecityDetrimentRefTmalltask> psCSameCityPurchaseRefTmallTaskList) {
        for (PsCSameCityDetrimentRefTmallTaskResponse item : result) {
            String cpCSalesroomId = item.getTbStoreId();
            String numIid = item.getNumIid();
            String cpCShopNickName = item.getSellerNick();
            String operationType = item.getActionType();
            Boolean isSuccess = item.getIsSuccess();

            PsCSamecityDetrimentRefTmalltask beforeInfo
                    = psCSameCityPurchaseRefTmallTaskList.stream().filter(
                    x -> String.valueOf(x.getCpCSalesroomId()).equals(cpCSalesroomId)
                            && String.valueOf(x.getNumiid()).equals(numIid)
                            && String.valueOf(x.getCpCShopNickName()).equals(cpCShopNickName)
                            && x.getOperationType().equalsIgnoreCase(operationType)
            ).collect(Collectors.toList()).get(0);
            if (beforeInfo != null) {
                beforeInfo.setIsNeedToDeal(getIsNeedToDeal(item.getIsSuccess()));
                beforeInfo.setDealTimes(beforeInfo.getDealTimes() + 1);
                beforeInfo.setModifierename(SystemUserResource.ROOT_ENAME);
                beforeInfo.setModifiername(SystemUserResource.ROOT_USER_NAME);
                beforeInfo.setModifierid(SystemUserResource.ROOT_USER_ID);
                beforeInfo.setModifieddate(new Date());
                psCSamecityDetrimentRefTmalltaskMapper.updateById(beforeInfo);

                Long psCSamecityPurchaseRefId = beforeInfo.getPsCSamecityPurchaseRefId();
                //删除动作且同步成功删除ref表记录
                if (beforeInfo.getOperationType().equals(PsExtConstantsIF.SYNCSAMECITYPURCHASEREF_ACTIONTYPE_DELETE) && isSuccess) {
                    psCSamecityDetrimentRefMapper.deleteById(psCSamecityPurchaseRefId);
                } else {
                    PsCSamecityDetrimentRef psCSamecityDetrimentRef = getPsCSamecityDetrimentRef(item,psCSamecityPurchaseRefId);
                    if(psCSamecityDetrimentRef!=null) {
                        psCSamecityDetrimentRefMapper.update(psCSamecityDetrimentRef,
                                new UpdateWrapper<PsCSamecityDetrimentRef>().lambda()
                                        .eq(PsCSamecityDetrimentRef::getId, psCSamecityPurchaseRefId));
                    }
                }
            }
        }
    }

    /**
     * Create PsCSamecityDetrimentRef
     *
     * @param item
     * @return
     */
    private PsCSamecityDetrimentRef getPsCSamecityDetrimentRef(PsCSameCityDetrimentRefTmallTaskResponse item,Long id) {
        PsCSamecityDetrimentRef oldpsCSamecityDetrimentRef=psCSamecityDetrimentRefMapper.selectById(id);
        if(oldpsCSamecityDetrimentRef!=null) {
            PsCSamecityDetrimentRef psCSamecityDetrimentRef = new PsCSamecityDetrimentRef();
            psCSamecityDetrimentRef.setPlatformSyncStatus(getPlatformSynvStatus(item.getIsSuccess(), item.getActionType()));
            if (!item.getIsSuccess()) {
                Long count=oldpsCSamecityDetrimentRef.getSyncFailedCount()==null?0L:oldpsCSamecityDetrimentRef.getSyncFailedCount();

                psCSamecityDetrimentRef.setSyncFailedCount(count+1);
            }
            psCSamecityDetrimentRef.setFailReason(item.getMsg());
            psCSamecityDetrimentRef.setModifierename(SystemUserResource.ROOT_ENAME);
            psCSamecityDetrimentRef.setModifiername(SystemUserResource.ROOT_USER_NAME);
            psCSamecityDetrimentRef.setModifierid(SystemUserResource.ROOT_USER_ID);
            psCSamecityDetrimentRef.setModifieddate(new Date());

            return psCSamecityDetrimentRef;
        }
        return  null;
    }

    /**
     * 获取同步状态
     *
     * @return
     */
    private String getPlatformSynvStatus(Boolean isSuccess, String operationType) {
        if (isSuccess) {
            return PlatformSyncStatusEnum.SUCCESS.getCode();
        } else {
            //删除动作
            if (operationType.equalsIgnoreCase(PsExtConstantsIF.SYNCSAMECITYPURCHASEREF_ACTIONTYPE_DELETE)) {
                return PlatformSyncStatusEnum.DELFAILED.getCode();
            }
            return PlatformSyncStatusEnum.ADDFAILED.getCode();
        }
    }

    /**
     * 状态
     *
     * @param isSuccess
     * @return
     */
    private Long getIsNeedToDeal(Boolean isSuccess) {
        if (isSuccess) {
            return 0L;
        } else {
            return 1L;
        }
    }

    /**
     * send 参数
     *
     * @param psCSameCityPurchaseRefTmallTaskList
     * @return
     */
    private String getRequestParams(List<PsCSamecityDetrimentRefTmalltask> psCSameCityPurchaseRefTmallTaskList) {
        List<PsCSameCityDetrimentRefTmallTaskRequest> syncInfoList = new ArrayList<>();
        for (PsCSamecityDetrimentRefTmalltask item : psCSameCityPurchaseRefTmallTaskList) {
            PsCSameCityDetrimentRefTmallTaskRequest syncInfo = new PsCSameCityDetrimentRefTmallTaskRequest();
            syncInfo.setTbStoreId(item.getCpCSalesroomId().toString());
            syncInfo.setNumIid(item.getNumiid());
            syncInfo.setSellerNick(item.getCpCShopNickName());
            syncInfo.setActionType(item.getOperationType());
            syncInfoList.add(syncInfo);
        }

        HashMap<String, Object> params = new HashMap<>();
        params.put("tag", pconf.getProperty(tagProperty));
        params.put("data", JSONObject.parseArray(JSON.toJSONString(syncInfoList)));
        params.put("method", pconf.getProperty(methodProperty));

        String queryString = null;
        try {
            queryString = (null == params) ? "" : HttpClientUtil.delimit(params.entrySet(), true);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return queryString;
    }

    /**
     * 更新同城门店商品关系同步状态为同步中(45)
     *
     * @param psCSameCityPurchaseRefTmallTaskList
     */
    private void updatePlatformSyncStatus(List<PsCSamecityDetrimentRefTmalltask> psCSameCityPurchaseRefTmallTaskList) {
        List<Long> refIds = psCSameCityPurchaseRefTmallTaskList.stream().map(x -> x.getPsCSamecityPurchaseRefId()).collect(Collectors.toList());
        PsCSamecityDetrimentRef psCSamecityDetrimentRef = new PsCSamecityDetrimentRef();
        psCSamecityDetrimentRef.setModifierename(SystemUserResource.ROOT_ENAME);
        psCSamecityDetrimentRef.setModifiername(SystemUserResource.ROOT_USER_NAME);
        psCSamecityDetrimentRef.setModifierid(SystemUserResource.ROOT_USER_ID);
        psCSamecityDetrimentRef.setModifieddate(new Date());
        psCSamecityDetrimentRef.setPlatformSyncStatus(PlatformSyncStatusEnum.PROCESSING.getCode());
        psCSamecityDetrimentRefMapper.update(psCSamecityDetrimentRef,
                new UpdateWrapper<PsCSamecityDetrimentRef>().lambda().in(PsCSamecityDetrimentRef::getId, refIds));
    }

    /**
     * 获取需要数据同步配置
     *
     * @return
     */
    private Map getConfig() {
        Map map = new HashMap<>();
        map.put("deal_times", 5);
        map.put("limit", StringUtil.isEmpty(pconf.getProperty(limitProperty)) ? 30 : pconf.getProperty(limitProperty));
        map.put("isactive", PsExtConstantsIF.EFFECTIVE);
        return map;
    }

    /**
     * 手工推送
     *
     * @param psCSamecityPurchaseRefIds
     */
    public void pushSameCityPurchaseRefToTmall(List<Long> psCSamecityPurchaseRefIds) {
        List<PsCSamecityDetrimentRefTmalltask> taskInfos = psCSamecityDetrimentRefTmalltaskMapper.selectList(
                new QueryWrapper<PsCSamecityDetrimentRefTmalltask>().lambda()
                        .in(PsCSamecityDetrimentRefTmalltask::getPsCSamecityPurchaseRefId, psCSamecityPurchaseRefIds)
                        .eq(PsCSamecityDetrimentRefTmalltask::getIsNeedToDeal, 1)
                        .eq(PsCSamecityDetrimentRefTmalltask::getIsactive, PsExtConstantsIF.EFFECTIVE));

        if (CollectionUtils.isNotEmpty(taskInfos)) {
            //更新同步状态为同步中
            updatePlatformSyncStatus(taskInfos);

            String params = getRequestParams(taskInfos);
            log.info(LogUtil.format("手工同步同城购商品请求报文：{}"), params);
            String repData = HttpClientUtil.sendPost(pconf.getProperty(urlProperty), params);
            log.info(LogUtil.format("手工同步同城购商品请求返回报文："), repData);
            //分析报文具体结构解析
            if (StringUtil.isEmpty(repData)) {
                log.error(LogUtil.format("同步同城购商品请求返回报文为空无法解析"));
                AssertUtils.logAndThrow("返回报文为空,无法解析");
            }
            List<PsCSameCityDetrimentRefTmallTaskResponse> psCSameCityDetrimentRefTmallTaskResponse
                    = JSONArray.parseArray(repData, PsCSameCityDetrimentRefTmallTaskResponse.class);
            if (CollectionUtils.isNotEmpty(psCSameCityDetrimentRefTmallTaskResponse)) {
                updateTaskInfo(psCSameCityDetrimentRefTmallTaskResponse, taskInfos);
            }
        }
    }
}
