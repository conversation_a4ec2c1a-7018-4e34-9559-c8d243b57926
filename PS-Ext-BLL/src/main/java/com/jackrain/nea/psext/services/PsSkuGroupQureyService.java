package com.jackrain.nea.psext.services;

import com.jackrain.nea.psext.mapper.PsCSkugroupMapper;
import com.jackrain.nea.psext.model.table.PsCSkugroup;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: 汪聿森
 * @Date: Created in 2019-10-11 15:21
 * @Description : 通过逻辑仓ID和商品编码查询组合商品实际条码信息
 */
@Component
@Slf4j
public class PsSkuGroupQureyService {
    @Autowired
    private PsCSkugroupMapper psCSkugroupMapper;

    public List<PsCSkugroup> psSkuGroupQurey(Long cpCStoreId, String psCProEcode, String psCSkuEcode) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("接收参数：cpCStoreId/psCProEcode/psCSkuEcode=", cpCStoreId, psCProEcode, psCSkuEcode));
        }
        return psCSkugroupMapper.psSkuGroupQurey(cpCStoreId, psCProEcode, psCSkuEcode);
    }
}
