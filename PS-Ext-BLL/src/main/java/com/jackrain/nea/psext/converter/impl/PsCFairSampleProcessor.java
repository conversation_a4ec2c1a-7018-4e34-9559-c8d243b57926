//package com.jackrain.nea.psext.converter.impl;
//
//import com.alibaba.fastjson.JSONObject;
//import com.aliyun.dts.subscribe.clients.record.RowImage;
//import com.aliyun.dts.subscribe.clients.record.value.Value;
//import com.baomidou.mybatisplus.core.mapper.BaseMapper;
//import com.jackrain.nea.psext.converter.R3Processor;
//import com.jackrain.nea.psext.enums.DtsBeanEnum;
//import com.jackrain.nea.psext.mapper.PsCFairSampleMapper;
//import com.jackrain.nea.psext.model.table.PsCFairSample;
//import com.jackrain.nea.psext.utils.PsUtils;
//import com.jackrain.nea.resource.SystemUserResource;
//import com.jackrain.nea.web.face.User;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.math.BigDecimal;
//
///**
// * @ClassName : PsCFairSampleProcessor
// * @Description :
// * <AUTHOR> CD
// * @Date: 2021-07-29 15:48
// */
//@Component
//@Slf4j
//public class PsCFairSampleProcessor implements R3Processor<RowImage, PsCFairSample> {
//    @Autowired
//    private PsCFairSampleMapper psCFairSampleMapper;
//
//    /**
//     * 获取转换表名称
//     *
//     * @return 表名
//     */
//    @Override
//    public String getTableName() {
//        return DtsBeanEnum.B_FAIR_SAMPLE.name();
//    }
//
//    /**
//     * 获取数据库执行
//     *
//     * @return
//     */
//    @Override
//    public BaseMapper<PsCFairSample> getMapper() {
//        return psCFairSampleMapper;
//    }
//
//    @Override
//    public PsCFairSample convert(RowImage rowImage) {
//        User user = SystemUserResource.getRootUser();
//
//        PsCFairSample sample = new PsCFairSample();
//        Value<BigDecimal> id = rowImage.getValue("ID");
//        if (null == id) return sample;
//        sample.setId(id.getData().longValue());
//
//        Value<BigDecimal> bFairId = rowImage.getValue("B_FAIR_ID");
//        if (null != bFairId) sample.setBFairId(bFairId.getData().longValue());
//        Value<BigDecimal> frEndMa = rowImage.getValue("FR_END_MA");
//        if (null != frEndMa) sample.setFrEndMa(frEndMa.getData().longValue());
//        Value<String> channel = rowImage.getValue("CHANNEL");
//        if (null != channel) sample.setChannel(channel.toString());
//        Value<String> status = rowImage.getValue("STATUS");
//        if (null != status) sample.setStatus(status.toString());
//        Value<BigDecimal> mSampleId = rowImage.getValue("M_SAMPLE_ID");
//        if (null != mSampleId) sample.setMSampleId(mSampleId.getData().longValue());
//        Value<String> division2 = rowImage.getValue("DIVISION2");
//        if (null != division2) sample.setDivision2(division2.toString());
//        Value<String> isKeyDivision = rowImage.getValue("IS_KEY_DIVISION");
//        if (null != isKeyDivision) sample.setIsKeyDivision(isKeyDivision.toString());
//        Value<BigDecimal> quarter = rowImage.getValue("QUARTER");
//        if (null != quarter) sample.setQuarter(quarter.getData().intValue());
//        Value<BigDecimal> shoeChannel = rowImage.getValue("SHOE_CHANNEL");
//        if (null != shoeChannel) sample.setShoeChannel(shoeChannel.getData().intValue());
//        Value<BigDecimal> marketdate = rowImage.getValue("MARKETDATE");
//        if (null != marketdate) sample.setMarketdate(marketdate.getData().intValue());
//        Value<BigDecimal> comedatee = rowImage.getValue("COMEDATEE");
//        if (null != comedatee) {
//            sample.setComedatee(comedatee.getData().intValue());
//            if (null != sample.getComedatee()) {
//                String comedateeMonthStr = StringUtils.substring(sample.getComedatee().toString(), 0, 6);
//                if (StringUtils.isNotBlank(comedateeMonthStr)) {
//                    sample.setComedateeMonth(Integer.valueOf(comedateeMonthStr));
//                }
//            }
//        }
//        Value<BigDecimal> comedates = rowImage.getValue("COMEDATES");
//        if (null != comedates) {
//            sample.setComedates(comedates.getData().intValue());
//            if (null != sample.getComedates()) {
//                String comedatesMonthStr = StringUtils.substring(sample.getComedates().toString(), 0, 6);
//                if (StringUtils.isNotBlank(comedatesMonthStr)) {
//                    sample.setComedatesMonth(Integer.valueOf(comedatesMonthStr));
//                }
//            }
//        }
//        Value<BigDecimal> comedatew = rowImage.getValue("COMEDATEW");
//        if (null != comedatew) {
//            sample.setComedatew(comedatew.getData().intValue());
//            if (null != sample.getComedatew()) {
//                String comedatewMonthStr = StringUtils.substring(sample.getComedatew().toString(), 0, 6);
//                if (StringUtils.isNotBlank(comedatewMonthStr)) {
//                    sample.setComedatewMonth(Integer.valueOf(comedatewMonthStr));
//                }
//            }
//        }
//        Value<BigDecimal> comedateen = rowImage.getValue("COMEDATEEN");
//        if (null != comedateen) {
//            sample.setComedateen(comedateen.getData().intValue());
//            if (null != sample.getComedateen()) {
//                String comedateenMonthStr = StringUtils.substring(sample.getComedateen().toString(), 0, 6);
//                if (StringUtils.isNotBlank(comedateenMonthStr)) {
//                    sample.setComedateenMonth(Integer.valueOf(comedateenMonthStr));
//                }
//            }
//        }
//        Value<BigDecimal> comedaten = rowImage.getValue("COMEDATEN");
//        if (null != comedaten) {
//            sample.setComedaten(comedaten.getData().intValue());
//            if (null != sample.getComedaten()) {
//                String comedatenMonthStr = StringUtils.substring(sample.getComedaten().toString(), 0, 6);
//                if (StringUtils.isNotBlank(comedatenMonthStr)) {
//                    sample.setComedatenMonth(Integer.valueOf(comedatenMonthStr));
//                }
//            }
//        }
//        Value<BigDecimal> comedateen2 = rowImage.getValue("COMEDATEEN2");
//        if (null != comedateen2) sample.setComedateen2(comedateen2.getData().intValue());
//        Value<String> flowno = rowImage.getValue("FLOWNO");
//        if (null != flowno) sample.setFlowno(flowno.toString());
//        Value<String> productInformation = rowImage.getValue("PRODUCT_INFORMATION");
//        if (null != productInformation) sample.setProductInformation(productInformation.toString());
//        Value<BigDecimal> pricelist = rowImage.getValue("PRICELIST");
//        if (null != pricelist) sample.setPricelist(BigDecimal.valueOf(pricelist.getData().doubleValue()));
//        Value<BigDecimal> certainly = rowImage.getValue("CERTAINLY");
//        if (null != certainly) sample.setCertainly(certainly.getData().intValue());
//        Value<BigDecimal> poster = rowImage.getValue("POSTER");
//        if (null != poster) sample.setPoster(poster.getData().intValue());
//        Value<BigDecimal> selfSelected = rowImage.getValue("SELF_SELECTED");
//        if (null != selfSelected) sample.setSelfSelected(selfSelected.getData().intValue());
//        Value<BigDecimal> ncTop = rowImage.getValue("NC_TOP");
//        if (null != ncTop) sample.setNcTop(ncTop.getData().intValue());
//        Value<BigDecimal> ecTop = rowImage.getValue("EC_TOP");
//        if (null != ecTop) sample.setEcTop(ecTop.getData().intValue());
//        Value<BigDecimal> scTop = rowImage.getValue("SC_TOP");
//        if (null != scTop) sample.setScTop(scTop.getData().intValue());
//        Value<BigDecimal> testProduct = rowImage.getValue("TEST_PRODUCT");
//        if (null != testProduct) sample.setTestProduct(testProduct.getData().intValue());
//        Value<BigDecimal> flagshipProduct = rowImage.getValue("FLAGSHIP_PRODUCT");
//        if (null != flagshipProduct) sample.setFlagshipProduct(flagshipProduct.getData().intValue());
//        Value<BigDecimal> cityProduct = rowImage.getValue("CITY_PRODUCT");
//        if (null != cityProduct) sample.setCityProduct(cityProduct.getData().intValue());
//        Value<BigDecimal> oto = rowImage.getValue("OTO");
//        if (null != oto) sample.setOto(oto.getData().intValue());
//        Value<String> analysis2 = rowImage.getValue("ANALYSIS2");
//        if (null != analysis2) sample.setAnalysis2(analysis2.toString());
//        Value<String> analysis3 = rowImage.getValue("ANALYSIS3");
//        if (null != analysis3) sample.setAnalysis3(analysis3.toString());
//        Value<BigDecimal> is6Multiple = rowImage.getValue("IS_6_MULTIPLE");
//        if (null != is6Multiple) sample.setIs6Multiple(is6Multiple.getData().intValue());
//        Value<BigDecimal> isBas = rowImage.getValue("IS_BAS");
//        if (null != isBas) sample.setIsBas(isBas.getData().intValue());
//        Value<String> analysis4 = rowImage.getValue("ANALYSIS4");
//        if (null != analysis4) sample.setAnalysis4(analysis4.toString());
//        Value<String> modelPicture = rowImage.getValue("MODEL_PICTURE");
//        if (null != modelPicture) sample.setModelPicture(modelPicture.toString());
//        Value<String> matchCode = rowImage.getValue("MATCH_CODE");
//        if (null != matchCode) sample.setMatchCode(matchCode.toString());
//        Value<String> isDelete = rowImage.getValue("IS_DELETE");
//        if (null != isDelete) sample.setIsDelete(isDelete.toString());
//        Value<BigDecimal> mSizeName = rowImage.getValue("M_SIZE_NAME");
//        if (null != mSizeName) sample.setMSizeId(mSizeName.getData().intValue());
//
//        sample.setOwnerename(user.getEname());
//        sample.setModifierename(user.getEname());
//        PsUtils.setModelDtsSyncData(sample, rowImage, user);
//
//        log.info("PsCFairSampleProcessor.convert data sample:{}", JSONObject.toJSON(sample));
//
//        return sample;
//    }
//}
