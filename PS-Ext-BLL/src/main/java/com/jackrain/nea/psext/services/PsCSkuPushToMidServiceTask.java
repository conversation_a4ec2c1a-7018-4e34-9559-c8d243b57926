package com.jackrain.nea.psext.services;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.Enum.ThirdWmsTypeEnum;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.psext.common.PSConstants;
import com.jackrain.nea.psext.common.PsExtConstantsIF;
import com.jackrain.nea.psext.common.PsSkuPushSystemConstants;
import com.jackrain.nea.psext.mapper.CpCBusinessSystemParametersMapper;
import com.jackrain.nea.psext.mapper.CpCPhyWarehouseMapper;
import com.jackrain.nea.psext.mapper.PsCSkuPushToWmsMidMapper;
import com.jackrain.nea.psext.mapper.PsCSkuWmsMapper;
import com.jackrain.nea.psext.model.table.CpCBusinessSystemParameters;
import com.jackrain.nea.psext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.psext.model.table.PsCSkuPushWmsMidModel;
import com.jackrain.nea.psext.model.table.PsCSkuWms;
import com.jackrain.nea.psext.utils.PsUtils;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: 商品推送中间表
 *
 * @Author: guo.kw
 * @Since: 2022/7/26
 * create at: 2022/7/26 09:46
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PsCSkuPushToMidServiceTask extends ServiceImpl<PsCSkuWmsMapper, PsCSkuWms> {

    private final PsCSkuWmsMapper psCSkuWmsMapper;

    private final PsCSkuPushToWmsMidMapper psCSkuPushToWmsMidMapper;

    private final CpCPhyWarehouseMapper cpCPhyWarehouseMapper;

    private final CpCBusinessSystemParametersMapper cpCBusinessSystemParametersMapper;

    private final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    private static final String PARAM_CODE = "ps_c_sku_push_wms_mid_query_date";
    private static final String PARAM_NAME = "商品查询起始时间";
    private static final String PARAM_REMARK = "商品查询起始时间";

    @Value("${lts.SyncSkuToMidTask.interval:10}")
    private Integer interval;

    public ValueHolderV14<String> pushItemToMidTask() {
        log.info(LogUtil.format("PsCSkuPushToMidServiceTask beginning"));
        ValueHolderV14<String> result = new ValueHolderV14<>(ResultCode.SUCCESS, "success");

        try {
            CpCBusinessSystemParameters cpCBusinessSystemParameters = cpCBusinessSystemParametersMapper.selectOne(new LambdaQueryWrapper<CpCBusinessSystemParameters>()
                    .eq(CpCBusinessSystemParameters::getParamCode, PARAM_CODE));
            //根据时间间隔查询
            Date beginParse = simpleDateFormat.parse("1999-01-01 00:00:00");
            Date endParse = new Date();
            User loginUser = SystemUserResource.getRootUser();
            if (Objects.isNull(cpCBusinessSystemParameters)) {
                insertCpCBusinessSystemParam(loginUser, beginParse);
            } else {
                beginParse = simpleDateFormat.parse(cpCBusinessSystemParameters.getParamValue());
            }
            List<PsCSkuWms> psCSkuList = psCSkuWmsMapper.selectList(new LambdaQueryWrapper<PsCSkuWms>()
                    .ge(PsCSkuWms::getModifieddate, beginParse)
                    .lt(PsCSkuWms::getModifieddate, endParse));
            if (CollectionUtils.isEmpty(psCSkuList)) {
                log.info(LogUtil.format("PsCSkuPushToMidService psCSkuList is null"));
                result.setMessage("未查询到数据");
                return result;
            }
            //查询仓库
            List<CpCPhyWarehouse> cpCPhyWarehouseList = cpCPhyWarehouseMapper.selectList(new LambdaQueryWrapper<CpCPhyWarehouse>()
                    .eq(CpCPhyWarehouse::getWmsControlWarehouse, 1));
            if (CollectionUtils.isEmpty(cpCPhyWarehouseList)) {
                result.setMessage("未查询到仓库数据");
                return result;
            }

            //查询仓库
            Map<String, List<CpCPhyWarehouse>> wmsTypeMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(cpCPhyWarehouseList)) {
                wmsTypeMap = cpCPhyWarehouseList.stream().filter(s -> StringUtils.isNotBlank(s.getWmsType()))
                        .collect(Collectors.groupingBy(CpCPhyWarehouse::getWmsType));
            }

            List<CpCPhyWarehouse> jwWarehouseList = new ArrayList<>(); //巨沃管控仓库
            List<CpCPhyWarehouse> jdWarehouseList = new ArrayList<>(); //京东管控仓库
            List<CpCPhyWarehouse> dbWarehouseList = new ArrayList<>(); //大宝仓
            List<CpCPhyWarehouse> flWarehouseList = new ArrayList<>(); //富勒仓
            if (wmsTypeMap != null && CollectionUtils.isNotEmpty(wmsTypeMap.keySet())) {
                List<CpCPhyWarehouse> jwList = wmsTypeMap.get(ThirdWmsTypeEnum.QMWMS.getCode());
                if (CollectionUtils.isNotEmpty(jwList)) {
                    jwWarehouseList = jwList.stream().filter(s -> StringUtils.isNotBlank(s.getWmsAccount())).collect(Collectors.toList());
                }
                List<CpCPhyWarehouse> jdList = wmsTypeMap.get(ThirdWmsTypeEnum.JDWMS.getCode());
                if (CollectionUtils.isNotEmpty(jdList)) {
                    jdWarehouseList = jdList.stream().filter(s -> StringUtils.isNotBlank(s.getWmsAccount())).collect(Collectors.toList());
                }
                List<CpCPhyWarehouse> dbList = wmsTypeMap.get(ThirdWmsTypeEnum.DBWMS.getCode());
                if (CollectionUtils.isNotEmpty(dbList)) {
                    dbWarehouseList = dbList.stream().filter(s -> StringUtils.isNotBlank(s.getWmsAccount())).collect(Collectors.toList());
                }

                // 增加富勒wms
                List<CpCPhyWarehouse> flList = wmsTypeMap.get(ThirdWmsTypeEnum.FLWMS.getCode());
                if (CollectionUtils.isNotEmpty(flList)) {
                    flWarehouseList = flList.stream().filter(s -> StringUtils.isNotBlank(s.getWmsAccount())).collect(Collectors.toList());
                }

            }

            // 看情况而定是否还需要此判断

            List<PsCSkuPushWmsMidModel> insertWmsTaskList = Lists.newArrayList();
            buildRequest(psCSkuList, jdWarehouseList, jwWarehouseList, dbWarehouseList, flWarehouseList, insertWmsTaskList);
            insertObject(insertWmsTaskList);
            updateCpCBusinessSystemParam(cpCBusinessSystemParameters, endParse);
        } catch (Exception e) {
            log.error(LogUtil.format("PsCSkuPushToMidService.error pushToMid message:{}",
                    "PsSkuPushToMidService.error pushToMid message"), Throwables.getStackTraceAsString(e));
            throw new NDSException(Throwables.getStackTraceAsString(e));
        }
        return result;
    }

    /**
     * 封装新增数据
     *
     * @param psCSkuList        获取商品列表
     * @param jdWarehouseList   京东管控仓库
     * @param jwWarehouseList   巨沃管控仓库
     * @param dbWarehouseList   大宝仓
     * @param insertWmsTaskList 新增中间表数据
     */
    public void buildRequest(List<PsCSkuWms> psCSkuList,
                             List<CpCPhyWarehouse> jdWarehouseList,
                             List<CpCPhyWarehouse> jwWarehouseList,
                             List<CpCPhyWarehouse> dbWarehouseList,
                             List<CpCPhyWarehouse> flWarehouseList,
                             List<PsCSkuPushWmsMidModel> insertWmsTaskList) {
        log.info(LogUtil.format("sku存入WMS商品表参数构建,sku：{},京东管控仓：{},巨沃管控仓：{},大宝仓：{}", "PsCSkuPushToMidServiceTask.buildRequest"),
                JSON.toJSON(insertWmsTaskList), JSON.toJSON(jdWarehouseList), JSON.toJSON(jwWarehouseList), JSON.toJSONString(dbWarehouseList));
        //查询是否中间表存在，用于判断新增还是编辑
        Map<String, PsCSkuPushWmsMidModel> jdMidModelMap = null;
        if (CollectionUtils.isNotEmpty(psCSkuList)) {
            List<String> skuCodeList = psCSkuList.stream().filter(s -> PSConstants.Y.equals(s.getIsPushJdyc())).map(PsCSkuWms::getEcode).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(skuCodeList)) {
                List<PsCSkuPushWmsMidModel> wmsMidModels = psCSkuPushToWmsMidMapper.selectList(new LambdaQueryWrapper<PsCSkuPushWmsMidModel>()
                        .in(PsCSkuPushWmsMidModel::getPsCSkuEcode, skuCodeList)
                        .eq(PsCSkuPushWmsMidModel::getThirdPartySystemName, ThirdWmsTypeEnum.JDWMS.getCode()));
                if (CollectionUtils.isNotEmpty(wmsMidModels)) {
                    jdMidModelMap = wmsMidModels.stream().collect(Collectors.toMap(PsCSkuPushWmsMidModel::getPsCSkuEcode, Function.identity(), (x, y) -> y));
                }
            }
        }



        for (PsCSkuWms psCSku : psCSkuList) {
            // 传奇门
            if (PSConstants.Y.equals(psCSku.getIsPushWms()) && CollectionUtils.isNotEmpty(jwWarehouseList)) {
                insertWmsTaskList.add(toMidWms(psCSku, jwWarehouseList));
            }
            // 传京东云仓
            if (PSConstants.Y.equals(psCSku.getIsPushJdyc()) && CollectionUtils.isNotEmpty(jdWarehouseList)) {
                PsCSkuPushWmsMidModel oldSkuMid = null;
                if (jdMidModelMap != null) {
                    oldSkuMid = jdMidModelMap.get(psCSku.getEcode());
                }
                insertWmsTaskList.add(toJdWms(psCSku, oldSkuMid, jdWarehouseList));
            }
            // 传大宝仓
            if (PSConstants.Y.equals(psCSku.getIsPushDbWms()) && CollectionUtils.isNotEmpty(dbWarehouseList)) {
                insertWmsTaskList.add(toDbWms(psCSku, dbWarehouseList));
            }
            // 传奶卡
            if (PSConstants.Y.equals(psCSku.getIsPushNk())) {
                insertWmsTaskList.add(toNaiKa(psCSku));
            }
            // 传富勒 组合品不传
            if (PSConstants.Y.equals(psCSku.getIsPushFule()) && 2 != psCSku.getWareType()) {
                insertWmsTaskList.add(toFLWms(psCSku, flWarehouseList));
            }
        }
    }

    /**
     * 传奶卡参数
     * @param psCSku
     * @return
     */
    private PsCSkuPushWmsMidModel toNaiKa(PsCSkuWms psCSku) {
        PsCSkuPushWmsMidModel psCSkuPushWmsMid = new PsCSkuPushWmsMidModel();
        psCSkuPushWmsMid.setId(ModelUtil.getSequence("PS_C_SKU_PUSH_WMS_MID"));
        psCSkuPushWmsMid.setPsCSkuId(psCSku.getId());
        psCSkuPushWmsMid.setPsCSkuEname(psCSku.getPsCProEname());
        psCSkuPushWmsMid.setPsCSkuEcode(psCSku.getEcode());
        psCSkuPushWmsMid.setBarcode(psCSku.getGbcode());
        psCSkuPushWmsMid.setThirdPartySystemName(PsSkuPushSystemConstants.SYSTEM_TYPE_NK);
        psCSkuPushWmsMid.setWmsStatus(PsExtConstantsIF.TO_WMS_STATUS_WAIT);
        psCSkuPushWmsMid.setFailureNum(0);
        PsUtils.setBModelDefalutData(psCSkuPushWmsMid, SystemUserResource.getRootUser());
        psCSkuPushWmsMid.setIsactive(psCSku.getIsactive());
//        if (psCSku.getPsCProId() != null && !org.springframework.util.CollectionUtils.isEmpty(proMap)) {
//            PsCPro psCPro = proMap.get(psCSku.getPsCProId());
//            if (psCPro != null) {
//                //保存规格型号
//                psCSkuPushWmsMid.setSpecModel(psCPro.getSpecModel());
//                if (psCPro.getMDim2Id() != null && !org.springframework.util.CollectionUtils.isEmpty(midMap) &&
//                    StringUtils.isNotBlank(midMap.get(new Long(psCPro.getMDim2Id())))) {
//                    //保存物料组
//                    psCSkuPushWmsMid.setProdimName(midMap.get(new Long(psCPro.getMDim2Id())));
//                }
//            }
//        }
        return psCSkuPushWmsMid;
    }

    /**
     * 传京东参数
     * @param psCSku
     * @param oldSkuMid
     * @param jdWarehouseList
     * @return
     */
    private PsCSkuPushWmsMidModel toJdWms(PsCSkuWms psCSku, PsCSkuPushWmsMidModel oldSkuMid, List<CpCPhyWarehouse> jdWarehouseList) {
        PsCSkuPushWmsMidModel psCSkuPushWmsMid = new PsCSkuPushWmsMidModel();
        CpCPhyWarehouse warehouse = jdWarehouseList.get(0);
        psCSkuPushWmsMid.setId(ModelUtil.getSequence("PS_C_SKU_PUSH_WMS_MID"));
        psCSkuPushWmsMid.setPsCSkuId(psCSku.getId());
        psCSkuPushWmsMid.setPsCSkuEname(psCSku.getPsCProEname());
        psCSkuPushWmsMid.setPsCSkuEcode(psCSku.getEcode());
        psCSkuPushWmsMid.setBarcode(psCSku.getGbcode());
        /*psCSkuPushWmsMid.setThirdPartySystemName(PsSkuPushSystemConstants.SYSTEM_TYPE_JDYC);*/
        psCSkuPushWmsMid.setThirdPartySystemName(ThirdWmsTypeEnum.JDWMS.getCode());
        if (oldSkuMid == null) {
            psCSkuPushWmsMid.setOperationType(PsSkuPushSystemConstants.OPERATION_TYPE_ADD);
        } else {
            psCSkuPushWmsMid.setOperationType(PsSkuPushSystemConstants.OPERATION_TYPE_UPDATE);
        }
        psCSkuPushWmsMid.setOwnerCode(warehouse.getOwnerCode());
        psCSkuPushWmsMid.setWmsAccount(warehouse.getWmsAccount());
        psCSkuPushWmsMid.setWmsStatus(PsExtConstantsIF.TO_WMS_STATUS_WAIT);
        psCSkuPushWmsMid.setFailureNum(0);
        PsUtils.setBModelDefalutData(psCSkuPushWmsMid, SystemUserResource.getRootUser());
        psCSkuPushWmsMid.setIsactive(psCSku.getIsactive());
        return psCSkuPushWmsMid;
    }


    /**
     * 传大宝参数
     *
     * @param psCSku
     * @param dbWarehouseList
     * @return
     */
    private PsCSkuPushWmsMidModel toDbWms(PsCSkuWms psCSku, List<CpCPhyWarehouse> dbWarehouseList) {
        PsCSkuPushWmsMidModel psCSkuPushWmsMid = new PsCSkuPushWmsMidModel();
        CpCPhyWarehouse warehouse = dbWarehouseList.get(0);
        psCSkuPushWmsMid.setId(ModelUtil.getSequence("PS_C_SKU_PUSH_WMS_MID"));
        psCSkuPushWmsMid.setPsCSkuId(psCSku.getId());
        psCSkuPushWmsMid.setPsCSkuEname(psCSku.getPsCProEname());
        psCSkuPushWmsMid.setPsCSkuEcode(psCSku.getEcode());
        psCSkuPushWmsMid.setBarcode(psCSku.getGbcode());
        /*psCSkuPushWmsMid.setThirdPartySystemName(PsSkuPushSystemConstants.SYSTEM_TYPE_DBWMS);*/
        psCSkuPushWmsMid.setThirdPartySystemName(ThirdWmsTypeEnum.DBWMS.getCode());
        /*每次都新增*/
        psCSkuPushWmsMid.setOperationType(PsSkuPushSystemConstants.OPERATION_TYPE_ADD);

        psCSkuPushWmsMid.setWmsWarehouseCode(warehouse.getWmsWarehouseCode());
        psCSkuPushWmsMid.setOwnerCode(warehouse.getOwnerCode());
        psCSkuPushWmsMid.setWmsAccount(warehouse.getWmsAccount());
        psCSkuPushWmsMid.setWmsStatus(PsExtConstantsIF.TO_WMS_STATUS_WAIT);
        psCSkuPushWmsMid.setFailureNum(0);
        PsUtils.setBModelDefalutData(psCSkuPushWmsMid, SystemUserResource.getRootUser());
        psCSkuPushWmsMid.setIsactive(psCSku.getIsactive());
        return psCSkuPushWmsMid;
    }

    /**
     * 传送wms参数
     *
     * @param psCSku
     * @return
     */
    public PsCSkuPushWmsMidModel toMidWms(PsCSkuWms psCSku, List<CpCPhyWarehouse> JwWarehouseList) {
        PsCSkuPushWmsMidModel psCSkuPushWmsMid = new PsCSkuPushWmsMidModel();
        CpCPhyWarehouse cpCPhyWarehouse = JwWarehouseList.get(0);
        psCSkuPushWmsMid.setId(ModelUtil.getSequence("PS_C_SKU_PUSH_WMS_MID"));
        psCSkuPushWmsMid.setPsCSkuId(psCSku.getId());
        psCSkuPushWmsMid.setPsCSkuEname(psCSku.getPsCProEname());
        psCSkuPushWmsMid.setPsCSkuEcode(psCSku.getEcode());
        psCSkuPushWmsMid.setBarcode(psCSku.getGbcode());
        psCSkuPushWmsMid.setOwnerCode(PsSkuPushSystemConstants.OWNER_CODE);

        //psCSkuPushWmsMid.setWmsAccount(PsSkuPushSystemConstants.WMS_ACCOUNT);
        psCSkuPushWmsMid.setWmsAccount(cpCPhyWarehouse.getWmsAccount());

        psCSkuPushWmsMid.setWmsWarehouseCode(PsSkuPushSystemConstants.WMS_WAREHOUSE_CODE);
        /*psCSkuPushWmsMid.setThirdPartySystemName(PsSkuPushSystemConstants.SYSTEM_TYPE_QMWMS);*/
        psCSkuPushWmsMid.setThirdPartySystemName(ThirdWmsTypeEnum.QMWMS.getCode());
        psCSkuPushWmsMid.setOperationType(PsSkuPushSystemConstants.OPERATION_TYPE_ADD);
        psCSkuPushWmsMid.setWmsStatus(PsExtConstantsIF.TO_WMS_STATUS_WAIT);
        psCSkuPushWmsMid.setFailureNum(0);
        PsUtils.setBModelDefalutData(psCSkuPushWmsMid, SystemUserResource.getRootUser());
        psCSkuPushWmsMid.setIsactive(psCSku.getIsactive());
        return psCSkuPushWmsMid;
    }

    /**
     * 传送富勒参数
     *
     * @param psCSku
     * @return
     */
    public PsCSkuPushWmsMidModel toFLWms(PsCSkuWms psCSku, List<CpCPhyWarehouse> FLWarehouseList) {
        PsCSkuPushWmsMidModel psCSkuPushWmsMid = new PsCSkuPushWmsMidModel();
        CpCPhyWarehouse cpCPhyWarehouse = FLWarehouseList.get(0);
        psCSkuPushWmsMid.setId(ModelUtil.getSequence("PS_C_SKU_PUSH_WMS_MID"));
        psCSkuPushWmsMid.setPsCSkuId(psCSku.getId());
        psCSkuPushWmsMid.setPsCSkuEname(psCSku.getPsCProEname());
        psCSkuPushWmsMid.setPsCSkuEcode(psCSku.getEcode());
        psCSkuPushWmsMid.setBarcode(psCSku.getGbcode());
        psCSkuPushWmsMid.setOwnerCode(PsSkuPushSystemConstants.OWNER_CODE);

        //psCSkuPushWmsMid.setWmsAccount(PsSkuPushSystemConstants.WMS_ACCOUNT);
        psCSkuPushWmsMid.setWmsAccount(cpCPhyWarehouse.getWmsAccount());

        psCSkuPushWmsMid.setWmsWarehouseCode(PsSkuPushSystemConstants.WMS_WAREHOUSE_CODE);
        /*psCSkuPushWmsMid.setThirdPartySystemName(PsSkuPushSystemConstants.SYSTEM_TYPE_QMWMS);*/
        psCSkuPushWmsMid.setThirdPartySystemName(ThirdWmsTypeEnum.FLWMS.getCode());
        psCSkuPushWmsMid.setOperationType(PsSkuPushSystemConstants.OPERATION_TYPE_ADD);
        psCSkuPushWmsMid.setWmsStatus(PsExtConstantsIF.TO_WMS_STATUS_WAIT);
        psCSkuPushWmsMid.setFailureNum(0);
        PsUtils.setBModelDefalutData(psCSkuPushWmsMid, SystemUserResource.getRootUser());
        psCSkuPushWmsMid.setIsactive(psCSku.getIsactive());
        return psCSkuPushWmsMid;
    }

    /**
     * 获取时间间隔
     *
     * @param interval
     * @return
     */
    public Date getIntervalDate(Integer interval) {
        Calendar instance = Calendar.getInstance();
        instance.add(Calendar.MINUTE, -interval);
        return instance.getTime();
    }

    /**
     * 新增或者修改
     *
     * @param subIdList
     * @param insertWmsTaskList
     */
    @Transactional
    public void insertAndUpdate(List<Long> subIdList,
                                List<PsCSkuPushWmsMidModel> insertWmsTaskList) {
        // 插入中间表
        if (CollectionUtils.isNotEmpty(insertWmsTaskList)) {
            List<List<PsCSkuPushWmsMidModel>> partition = Lists.partition(insertWmsTaskList, 200);

            for (List<PsCSkuPushWmsMidModel> subList : partition) {
                List<PsCSkuPushWmsMidModel> updateList = Lists.newArrayList();
                List<PsCSkuPushWmsMidModel> insertList = Lists.newArrayList();
                for (PsCSkuPushWmsMidModel psCSku : subList) {
                    psCSku.setFailureReason("");
                    psCSku.setFailureNum(0);
                    psCSku.setWmsStatus(0);
                    Map<String, Object> map = new HashMap<>();
                    map.put("WMS_ACCOUNT", psCSku.getWmsAccount());
                    map.put("PS_C_SKU_ID", psCSku.getPsCSkuId());
                    List<PsCSkuPushWmsMidModel> psCSkuPushWmsMids = psCSkuPushToWmsMidMapper.selectByMap(map);
                    if (CollectionUtils.isNotEmpty(psCSkuPushWmsMids)) {
                        PsUtils.setBModelDefalutDataByUpdate(psCSku, SystemUserResource.getRootUser());
                        PsCSkuPushWmsMidModel psCSkuPushWmsMid = psCSkuPushWmsMids.get(0);
                        if (psCSkuPushWmsMid.getWmsStatus() != 2
                                && "add".equals(psCSkuPushWmsMid.getOperationType())) {
                            psCSku.setOperationType("add");
                        } else {
                            psCSku.setOperationType("update");
                        }
                        psCSku.setId(psCSkuPushWmsMid.getId());
                        updateList.add(psCSku);
                    } else {
                        psCSku.setOperationType("add");
                        psCSku.setId(ModelUtil.getSequence("ps_c_sku_push_wms_mid"));
                        PsUtils.setBModelDefalutData(psCSku, SystemUserResource.getRootUser());
                        insertList.add(psCSku);
                    }
                }
                if (CollectionUtils.isNotEmpty(updateList)) {
                    PsCSkuPushToMidService bean = ApplicationContextHandle.getBean(PsCSkuPushToMidService.class);
                    bean.updateBatchById(updateList, PsExtConstantsIF.SELECT_PAGE_SIZE_FIVE_HUNDRED);
                }
                if (CollectionUtils.isNotEmpty(insertList)) {
                    psCSkuPushToWmsMidMapper.batchInsert(insertList);
                }
            }
        }
        // 将【条码档案】‘是否传入WMS’更新为‘是’
        PsCSkuWms psCSku = new PsCSkuWms();
        psCSku.setIsToMidTable(1);
        psCSku.setModifieddate(new Date());
        LambdaQueryWrapper<PsCSkuWms> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PsCSkuWms::getId, subIdList);
        psCSkuWmsMapper.update(psCSku, queryWrapper);
    }

    /**
     * 只进行新增中间表
     *
     * @param insertWmsTaskList
     */
    @Transactional
    public void insertObject(List<PsCSkuPushWmsMidModel> insertWmsTaskList) {
        log.info(LogUtil.format("sku存入WMS商品表，入参：{}"), JSON.toJSON(insertWmsTaskList));
        if (CollectionUtils.isNotEmpty(insertWmsTaskList)) {
            List<List<PsCSkuPushWmsMidModel>> partition = Lists.partition(insertWmsTaskList, 200);
            for (List<PsCSkuPushWmsMidModel> psCSkuList : partition) {
                psCSkuPushToWmsMidMapper.batchInsert(psCSkuList);
            }
        }
    }

    /**
     * 批量新增商品sku  三个是否传送字段为N
     *
     * @param psCSkuWms
     */
    @Transactional
    public void updatePcSkuList(List<PsCSkuWms> psCSkuWms) {
        if (CollectionUtils.isNotEmpty(psCSkuWms)) {
            List<List<PsCSkuWms>> partition = Lists.partition(psCSkuWms, 200);
            for (List<PsCSkuWms> psCSku : partition) {
                PsCSkuPushToMidServiceTask bean = ApplicationContextHandle.getBean(PsCSkuPushToMidServiceTask.class);
                bean.updateBatchById(psCSku, 200);
            }
        }
    }

    /**
     * 新增业务系统参数数
     *
     * @param loginUser 系统信息
     * @param endParse  当前时间
     */
    public void insertCpCBusinessSystemParam(User loginUser, Date endParse) {
        CpCBusinessSystemParameters cpCBusinessSystemParameters1 = new CpCBusinessSystemParameters();
        Long loginUserId = loginUser.getId() == null ? null : loginUser.getId().longValue();

        cpCBusinessSystemParameters1.setId(ModelUtil.getSequence("cp_c_business_system_parameters"));
        cpCBusinessSystemParameters1.setParamCode(PARAM_CODE);
        cpCBusinessSystemParameters1.setParamName(PARAM_NAME);
        cpCBusinessSystemParameters1.setParamRemark(PARAM_REMARK);
        cpCBusinessSystemParameters1.setParamValue(simpleDateFormat.format(endParse));
        cpCBusinessSystemParameters1.setAdClientId(Long.valueOf(loginUser.getClientId()));
        cpCBusinessSystemParameters1.setAdOrgId(Long.valueOf(loginUser.getOrgId()));
        cpCBusinessSystemParameters1.setIsactive("Y");
        cpCBusinessSystemParameters1.setOwnerid(loginUserId);
        cpCBusinessSystemParameters1.setOwnername(loginUser.getName());
        cpCBusinessSystemParameters1.setCreationdate(endParse);
        cpCBusinessSystemParameters1.setModifierid(loginUserId);
        cpCBusinessSystemParameters1.setModifiername(loginUser.getName());
        cpCBusinessSystemParameters1.setModifieddate(endParse);
        cpCBusinessSystemParametersMapper.insert(cpCBusinessSystemParameters1);
    }

    /**
     * 更新业务系统参数
     *
     * @param cpCBusinessSystemParameters 系统参数实体
     * @param endParse                    当前时间
     */
    public void updateCpCBusinessSystemParam(CpCBusinessSystemParameters cpCBusinessSystemParameters, Date endParse) {
        CpCBusinessSystemParameters cpCBusinessSystemParameters1 = new CpCBusinessSystemParameters();
        cpCBusinessSystemParameters1.setId(cpCBusinessSystemParameters.getId());
        cpCBusinessSystemParameters1.setParamValue(simpleDateFormat.format(endParse));
        cpCBusinessSystemParameters1.setModifieddate(endParse);
        cpCBusinessSystemParametersMapper.updateById(cpCBusinessSystemParameters1);
    }
}
