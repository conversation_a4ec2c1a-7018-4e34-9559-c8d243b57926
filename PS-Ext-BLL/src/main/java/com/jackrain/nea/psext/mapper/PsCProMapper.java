package com.jackrain.nea.psext.mapper;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.ps.api.table.PsCProdimItem;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.psext.request.SkuQueryRequest;
import com.jackrain.nea.psext.result.Dim8Result;
import com.jackrain.nea.psext.result.PdaQueryProResult;
import com.jackrain.nea.psext.result.ProExtResult;
import com.jackrain.nea.psext.result.goodsResult;
import com.jackrain.nea.psext.vo.DefendProSaleAttrImpVo;
import com.jackrain.nea.web.utils.ArrayToSUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.jdbc.SQL;

import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface PsCProMapper extends ExtentionMapper<PsCPro> {

    @Select("select * from ps_c_pro where id = #{id} and ${columnName} = #{val}")
    PsCPro queryByProdimItemCodeAndId(@Param("id") long id, @Param("columnName") String columnName, @Param("val") Long val);

    @Select("select ID,ECODE,ENAME,MIXNAME from PS_C_PRODIM_ITEM where id = #{id}")
    PsCProdimItem queryProdimItem(@Param("id") long id);

    @SelectProvider(type = QuerySkuSqlProvider.class, method = "select")
    public List<HashMap> querySku(SkuQueryRequest skuQueryRequest);

    @Select("SELECT * FROM PS_C_PRO WHERE ISACTIVE=#{isActive} AND GROUP_TYPE=#{type} AND STATUS=#{status} AND MODIFIEDDATE>#{timestamp}  LIMIT #{startIndex},#{pageCount}")
    List<PsCPro> selectProVoid(@Param("isActive") String isActive, @Param("startIndex") Integer startIndex, @Param("pageCount") Integer pageCount, @Param("timestamp") Timestamp timestamp, @Param("type") Integer type, @Param("status") Integer status);

    // start merge code ref ys
    @Select("<script> "
            + "select * from \n"
            + "ps_c_pro  \n"
            + "where    <foreach item='item' index='index' collection='cloumns' open='(' separator='or' close=')'> ${item} like  '${val}%'</foreach> "
            + " and ISACTIVE = 'Y'  ORDER BY ID DESC"
            + "</script>")
    List<PsCPro> queryLikeProByProEcode(@Param("cloumns") List<String> cloumns, @Param("val") String val);

    @Update("update ps_c_pro set product_alias=#{productAlias},product_classify=#{productClassify}," +
            "product_small_category=#{productSmallCategory},product_coefficient=#{productCoefficient},modifierid=#{userId}," +
            "modifierename=#{userEname},modifiername=#{userName},modifieddate=now()" +
            " where ecode=#{proCode}")
    int updateSaleAttr(DefendProSaleAttrImpVo impVo);

    class QuerySkuSqlProvider {

        public String select(SkuQueryRequest skuQueryRequest) {
            StringBuilder sql = new StringBuilder("select a.PS_C_PRO_ID,a.ID as skuId,a.WEIGHT,a.BULK, c.Id as colorId,d.ID as sizeId,b.ENAME as PS_C_PRO_ENAME,b.ECODE as PS_C_PRO_ECODE,  \n" +
                    "a.ECODE as ECODE,d.ENAME as sizeName,d.ECODE as sizeCode,c.ECODE as colorCode,c.ENAME as colorName,    \n" +
                    "a.GBCODE as GBCODE,\n" +
                    "a.WARE_TYPE as WareType, b.is_virtual as isVirtual,\n" +
                    "(case a.WARE_TYPE\n" +
                    "when  2 then 'Y'\n" +
                    "when 1 then 'Y'\n" +
                    "else 'N' end) as IS_GROUP," +
                    "CONCAT(d.ENAME,',',c.ENAME) as SPEC," +
                    //   "b.IS_GIFT as IS_GIFT," +
                    "(case b.IS_GIFT\n" +
                    "when  'Y' then '1'\n" +
                    "else '0'\n" +
                    "end \n" +
                    ") as IS_GIFT," +
                    "b.PRICE_COST_LIST as PRICELIST,\n" +
                    "b.PRICE_SALE_LIST as PRICESALELIST,\n" +
//                    "b.SEX as sex,\n" +
                    "b.PRICE_LIST as tagPrice, \n" +
                    "b.ISACTIVE as ISACTIVE \n" +
                    "            from PS_C_SKU a \n" +
                    "            left join ps_c_pro b on a.PS_C_PRO_ID = b.ID \n" +
                    "            left join ps_c_specobj c on c.ID=a.PS_C_SPEC1OBJ_ID \n" +
                    "            left join PS_C_SPECOBJ d on d.ID = a.PS_C_SPEC2OBJ_ID  \n" +
                    "             where a.ISACTIVE = 'Y' ");

            if (skuQueryRequest.getIsBlur().equals("N")) {
                if (StringUtils.isNotEmpty(skuQueryRequest.getPsCSku().getEcode())) {
                    sql.append(" and a.ECODE  = '" + skuQueryRequest.getPsCSku().getEcode() + "'");
                }
                if (StringUtils.isNotEmpty(skuQueryRequest.getPsCSku().getPsCProEcode())) {
                    sql.append(" and b.ECODE = '" + skuQueryRequest.getPsCSku().getPsCProEcode() + "'");
                }
                if (StringUtils.isNotEmpty(skuQueryRequest.getPsCSku().getPsCProEname())) {
                    sql.append(" and b.ENAME = '" + skuQueryRequest.getPsCSku().getPsCProEname() + "'");
                }
                if (StringUtils.isNotEmpty(skuQueryRequest.getPsCSku().getGbcode())) {
                    sql.append(" and a.GBCODE  = '" + skuQueryRequest.getPsCSku().getGbcode() + "'");
                }
                if (null != skuQueryRequest.getPsCSku().getPsCProId()) {
                    sql.append(" and a.PS_C_PRO_ID  = '" + skuQueryRequest.getPsCSku().getPsCProId() + "'");
                }
            } else {
                if (StringUtils.isNotEmpty(skuQueryRequest.getPsCSku().getEcode())) {
                    sql.append(" and a.ECODE  like '" + skuQueryRequest.getPsCSku().getEcode() + "%'");
                }
                if (StringUtils.isNotEmpty(skuQueryRequest.getPsCSku().getPsCProEcode())) {
                    sql.append(" and b.ECODE like '" + skuQueryRequest.getPsCSku().getPsCProEcode() + "%'");
                }
                if (StringUtils.isNotEmpty(skuQueryRequest.getPsCSku().getPsCProEname())) {
                    sql.append(" and b.ENAME like '" + skuQueryRequest.getPsCSku().getPsCProEname() + "%'");
                }
                if (StringUtils.isNotEmpty(skuQueryRequest.getPsCSku().getGbcode())) {
                    sql.append(" and a.GBCODE  like '" + skuQueryRequest.getPsCSku().getGbcode() + "%'");
                }
            }

            if (null != skuQueryRequest.getPsCSku().getId()) {
                sql.append(" and a.id = " + skuQueryRequest.getPsCSku().getId());
            }
            if (StringUtils.isNotEmpty(skuQueryRequest.getIsGift())) {
//                if (skuQueryRequest.getIsGift().equals("N")) {
//                    skuQueryRequest.setIsGift("0");
//                } else {
//                    skuQueryRequest.setIsGift("1");
//                }
                sql.append(" and b.IS_GIFT = '" + skuQueryRequest.getIsGift() + "'");
            }
            String excSql = sql.toString();
            return excSql;
        }

        public String selectPdaQueryProsql(List<String> proEcodeList, List<String> proEnameList, String isactive) {
            return new SQL() {
                {
                    SELECT(" p.id as psCProId, p.ecode as psCProEcode, p.ename as psCProEname, " +
                            "r.ename as psCProruleEname,s.ename as cpCSupplierEname, b.ename as psCBrandEname, " +
                            "p.gbcode,p.factorycode,p.proyear,p.image, " +
                            "ppi.ename as classificationSeason,p.CLASSIFICATION_YEAR as classificationYear, " +
                            "p.sex,p.modifieddate ");
                    FROM(" ps_c_pro p ");
                    LEFT_OUTER_JOIN(" ps_c_prorule r ON r.id = p.ps_c_prorule_id ");
                    LEFT_OUTER_JOIN(" cp_c_supplier s on s.id = p.CP_C_SUPPLIER_ID ");
                    LEFT_OUTER_JOIN(" ps_c_prodim_item ppi on p.CLASSIFICATION_SEASON = ppi.id ");
                    LEFT_OUTER_JOIN(" ps_c_brand b on b.id = p.ps_c_brand_id ");
                    WHERE(" p.isactive = '" + isactive + "' ");
                    String codeSql = null;
                    if (CollectionUtils.isNotEmpty(proEcodeList)) {
                        StringBuffer condtion = new StringBuffer();
                        for (String ecode : proEcodeList) {
                            condtion.append("'").append(ecode).append("',");
                        }
                        condtion = condtion.deleteCharAt(condtion.length() - 1);
                        codeSql = " p.ecode IN (" + condtion.toString() + ")";
                    }
                    String nameSql = null;
                    if (CollectionUtils.isNotEmpty(proEnameList)) {
                        StringBuffer condtion = new StringBuffer();
                        for (String ecode : proEnameList) {
                            condtion.append("'").append(ecode).append("',");
                        }
                        condtion = condtion.deleteCharAt(condtion.length() - 1);
                        nameSql = " p.ename IN (" + condtion.toString() + ")";
                    }
                    if (CollectionUtils.isNotEmpty(proEcodeList) && CollectionUtils.isNotEmpty(proEnameList)) {
                        WHERE(" (" + codeSql + " OR " + nameSql + ") ");
                    } else {
                        if (CollectionUtils.isNotEmpty(proEcodeList)) {
                            WHERE(codeSql);
                        }
                        if (CollectionUtils.isNotEmpty(proEnameList)) {
                            WHERE(nameSql);
                        }
                    }
                }
            }.toString();
        }

    }

    String field =
            "\tp.id ,\n" +
                    "\tp.PS_C_PRORULE_ID as psCProruleId,\n" +
                    "\tp.PS_C_SKURULE_ID as psCSkuruleId,\n" +
                    "\tp.ENAME,\n" +
                    "\tp.datemarket,\n" +
                    "\tp.CP_C_SUPPLIER_ID as cpCSupplierId,\n" +
                    "\tp.PS_C_SHAPEGROUP_ID as psCShapegroupId,\n" +
                    "\tp.factorycode,\n" +
                    "\tp.fabdesc,\n" +
                    "\tp.tagspec,\n" +
                    "\tp.CP_C_WAREHOUSE_ID as cpCWarehouseId,\n" +
                    "\tp.proyear,\n" +
                    "\tp.largeclass,\n" +
                    "\tp.sex,\n" +
                    "\tp.supbrand,\n" +
                    "\tp.prosea,\n" +
                    "\tp.proband,\n" +
                    "\tp.promonth,\n" +
                    "\tp.prosource,\n" +
                    "\tp.promotiontype,\n" +
                    "\tp.proline,\n" +
                    "\tp.popular,\n" +
                    "\tp.composition,\n" +
                    "\tp.style,\n" +
                    "\tp.details,\n" +
                    "\tp.priceband,\n" +
                    "\tp.series,\n" +
                    "\tp.purnature,\n" +
                    "\tp.buyer,\n" +
                    "\tp.follower,\n" +
                    "\tp.newproer,\n" +
                    "\tp.mdlargeclass,\n" +
                    "\tp.mdmiddleclass,\n" +
                    "\tp.fabric,\n" +
                    "\tp.safetechclass,\n" +
                    "\tp.prostandard,\n" +
                    "\tp.prostandard1,\n" +
                    "\tp.prostandard2,\n" +
                    "\tp.buypatterner,\n" +
                    "\tp.designer,\n" +
                    "\tp.themestory,\n" +
                    "\tp.discenter,\n" +
                    "\tp.shelfcode,\n" +
                    "\tp.pricecostlist,\n" +
                    "\tp.pricelist,\n" +
                    "\tp.pricesettle,\n" +
                    "\tp.pricelower,\n" +
                    "\tp.trialstorenum,\n" +
                    "\tp.disstorenum,\n" +
                    "\tp.redisstorenum,\n" +
                    "\tp.trialdays,\n" +
                    "\tp.recycle,\n" +
                    "\tp.dateoffshelf,\n" +
                    "\tp.saleperiod,\n" +
                    "\tp.reliability,\n" +
                    "\tp.capacity,\n" +
                    "\tp.minlotsize,\n" +
                    "\tp.unit,\n" +
                    "\tp.dateendrecycle,\n" +
                    "\tp.dateendreturn,\n" +
                    "\tp.supremark,\n" +
                    "\tp.numdim1,\n" +
                    "\tp.numdim2,\n" +
                    "\tp.numdim3,\n" +
                    "\tp.numdim4,\n" +
                    "\tp.numdim5,\n" +
                    "\tp.numdim6,\n" +
                    "\tp.numdim7,\n" +
                    "\tp.numdim8,\n" +
                    "\tp.numdim9,\n" +
                    "\tp.numdim10,\n" +
                    "\tp.numdim11,\n" +
                    "\tp.numdim12,\n" +
                    "\tp.numdim13,\n" +
                    "\tp.numdim14,\n" +
                    "\tp.numdim15,\n" +
                    "\tp.numdim16,\n" +
                    "\tp.numdim17,\n" +
                    "\tp.numdim18,\n" +
                    "\tp.numdim19,\n" +
                    "\tp.numdim20,\n" +
                    "\tp.decdim1,\n" +
                    "\tp.decdim2,\n" +
                    "\tp.decdim3,\n" +
                    "\tp.decdim4,\n" +
                    "\tp.decdim5,\n" +
                    "\tp.txtdim1,\n" +
                    "\tp.txtdim2,\n" +
                    "\tp.txtdim3,\n" +
                    "\tp.txtdim4,\n" +
                    "\tp.txtdim5,\n" +
                    "\tp.txtdim6,\n" +

                    "\tp.txtdim7,\n" +
                    "\tp.txtdim8,\n" +
                    "\tp.txtdim9,\n" +
                    "\tp.txtdim10,\n" +
                    "\tp.datedim1,\n" +
                    "\tp.datedim2,\n" +
                    "\tp.datedim3,\n" +
                    "\tp.datedim4,\n" +
                    "\tp.datedim5,\n" +
                    "\tp.datetimedim1,\n" +
                    "\tp.datetimedim2,\n" +
                    "\tp.datetimedim3,\n" +
                    "\tp.datetimedim4,\n" +
                    "\tp.datetimedim5,\n" +
                    "\tp.status,\n" +
                    "\tp.statuserid,\n" +
                    "\tp.statustime,\n" +
                    "\tp.promangroup,\n" +
                    "\tp.PS_C_SPEC1GROUP_ID as psCSpec1groupId,\n" +
                    "\tp.PS_C_SPEC2GROUP_ID as psCSpec2groupId,\n" +
                    "\tp.PS_C_SPEC3GROUP_ID as psCSpec3groupId,\n" +
                    "\tp.PS_C_SPEC4GROUP_ID as psCSpec4groupId,\n" +
                    "\tp.statusername,\n" +
                    "\tp.statuserename,\n" +
                    "\tp.SYNC_SUPPRICE as syncSupprice,\n" +
                    "\tp.SYNC_STOREPRICE as syncStoreprice,\n" +
                    "\tp.clrs,\n" +
                    "\tp.sizes,\n" +
                    "\tp.promangroup,\n" +
                    "\tp.numdim21,\n" +
                    "\tp.numdim22,\n" +
                    "\tp.numdim23,\n" +
                    "\tp.numdim24,\n" +
                    "\tp.numdim25,\n" +
                    "\tp.numdim26,\n" +
                    "\tp.numdim27,\n" +
                    "\tp.numdim28,\n" +
                    "\tp.numdim29,\n" +
                    "\tp.numdim30,\n" +
                    "\tp.numdim31,\n" +
                    "\tp.numdim32,\n" +
                    "\tp.numdim33,\n" +
                    "\tp.numdim34,\n" +
                    "\tp.numdim35,\n" +
                    "\tp.numdim36,\n" +
                    "\tp.numdim37,\n" +
                    "\tp.numdim38,\n" +
                    "\tp.numdim39,\n" +
                    "\tp.numdim40,\n" +
                    "\tp.attention,\n" +
                    "\tp.browse,\n" +
                    "\tp.evaluate,\n" +
                    "\tp.salesvolume,\n" +
                    "\tp.isuploadimg,\n" +
                    "\tp.iswridesc,\n" +
                    "\tp.PROMOTION_PRICE as promotionPrice,\n" +
                    "\tp.collocation,\n" +
                    "\tp.isup,\n" +
                    "\tp.isselection,\n" +
                    "\tp.isprocured,\n" +
                    "\tp.dateonshelf,\n" +
                    "\tp.GROUP_TYPE as groupType,\n" +
                    "\tp.businessrange,\n" +
                    "\tt. ecode as materieltype,\n" +
                    "\tp.WARE_TYPE as wareType,\n" +
                    "\tp.YEAR_SEASON as yearSeason,\n" +
                    "\tp.basicunit,\n" +
                    "\tp.largeseries,\n" +
                    "\tp.smallseries,\n" +
                    "\tp.subseries,\n" +
                    "\tp.bigcate,\n" +
                    "\tp.smallcate,\n" +
                    "\tp.southtime,\n" +
                    "\tp.northtime,\n" +
                    "\tp.ordernum,\n" +
                    "\tp.editiontype,\n" +
                    "\tp.needlewoven,\n" +
                    "\tp.clothlining,\n" +
                    "\tp.clothfabric,\n" +
                    "\tp.clothcollocation,\n" +
                    "\tp.cottonfeather,\n" +
                    "\tp.clothingstyle,\n" +
                    "\tp.thickness,\n" +
                    "\tp.component,\n" +
                    "\tp.shoeupper,\n" +
                    "\tp.leathermesh,\n" +
                    "\tp.sole,\n" +
                    "\tp.shoelabel,\n" +
                    "\tp.shoeextend,\n" +
                    "\tp.prodfunction,\n" +
                    "\tp.propert,\n" +
                    "\tp.concept,\n" +
                    "\tp.extension,\n" +
                    "\tp.packagetypes,\n" +
                    "\tp.IS_PARENTAGE as isParentage,\n" +
                    "\tp.IS_LOVERS as isLovers,\n" +
                    "\tp.IS_SUIT as isSuit,\n" +
                    "\tp.IS_SPONSOR as isSponsor,\n" +
                    "\tp.IS_CERTAINLY as isCertainly,\n" +
                    "\tp.IS_PRINTING as isPrinting,\n" +
                    "\tp.IS_PACKAGE as isPackage,\n" +
                    "\tp.IS_POP as isPop,\n" +
                    "\tp.IS_MAINPUSH as isMainpush,\n" +
                    "\tp.IS_PROMOTION as isPromotion,\n" +
                    "\tp.IS_GIFT as isGift,\n" +
                    "\tp.DELIVERY_METHOD as deliveryMethod,\n" +
                    "\tp.IS_CIRCULARS as isCirculars,\n" +
                    "\tp.IS_AIRFORBIDDEN as isAirforbidden,\n" +
                    "\tp.image,\n" +
                    "\tp.PS_C_SPEC_IDS as psCSpecIds,\n" +
                    "\tp.PS_C_SPECOBJ_IDS as psCSpecobjIds,\n" +
                    "\tp.detaildesc,\n" +
                    "\tp.IMAGE_SKU as imageSku,\n" +
                    "\tp.video";

    @Select("select id from PS_C_PRO where status=#{code}")
    List<String> findSubmitProId(@Param("code") Integer code);

    @Select("SELECT * FROM PS_C_PRO WHERE  ISACTIVE='Y' AND STATUS=#{status} and GROUP_TYPE=#{type} LIMIT #{startIndex},#{pageCount}")
    List<PsCPro> selectProByStatus(@Param("status") Integer status, @Param("startIndex") Integer startIndex, @Param("pageCount") Integer pageCount, @Param("type") Integer type);

    class ProQuery {

        public String selectByIds(Map<String, Object> para) {
            // 查询字段
            // 条码ID集合
            JSONArray ids = (JSONArray) para.get("proIds");
            StringBuilder sql = new StringBuilder(" SELECT ");
            sql.append(field);
            sql.append(" FROM PS_C_PRO p LEFT JOIN PS_C_PRODIM_ITEM as t ON t.id=p.materieltype");
            sql.append(" WHERE p.ID IN (").append(ArrayToSUtil.join((ids).toArray(), ",")).append(")");
            sql.append(" AND p.ISACTIVE = 'Y' ");
            if (!sql.toString().toLowerCase().contains("where")) {
                return "";
            }
            return sql.toString();
        }

        public String selectByEcodes(Map<String, Object> para) {
            // 查询字段
            // 条码ID集合
            JSONArray proEcodes = (JSONArray) para.get("proEcodes");
            StringBuilder sql = new StringBuilder(" SELECT ");
            sql.append(field);
            sql.append(" FROM PS_C_PRO p LEFT JOIN PS_C_PRODIM_ITEM as t ON t.id=p.materieltype");
            sql.append(" WHERE p.ECODE IN (").append(ArrayToSUtil.join((proEcodes).toArray(), ",")).append(")");
            sql.append(" AND p.ISACTIVE = 'Y' ");
            if (!sql.toString().toLowerCase().contains("where")) {
                return "";
            }
            return sql.toString();
        }
    }

    @SelectProvider(type = PsCProMapper.ProQuery.class,
            method = "selectByIds")
    List<PsCPro> proQuery(@Param("proIds") JSONArray proIds);

    @SelectProvider(type = PsCProMapper.ProQuery.class,
            method = "selectByEcodes")
    List<PsCPro> proQueryEcode(@Param("proEcodes") JSONArray proEcodes);

    @Select("<script> "
            + "select * from \n"
            + "ps_c_pro  b\n"
            + "where  b.ecode in <foreach item='item' index='index' collection='proList' open='(' separator=',' close=')'> #{item} </foreach> "
            + " and b.ISACTIVE = 'Y'"
            + "</script>")
    List<PsCPro> proQueryByEcode(@Param("proList") List<String> proList);

    @Select(
            "<script>" +

                    "select a.ID as skuId,b.ID as proId,c.ID as colorId,d.ID as sizeId, b.PRICELIST as priceList,a.WEIGHT as weight, "+
                    "a.ECODE as skuEcode,b.ECODE as proEcode,c.ECODE as colorCode, a.WARE_TYPE as skuType,"+
                    "d.ECODE as sizeCode,b.ENAME as proName ,c.ENAME as colorName, "+
                    "d.ENAME as sizeName,a.GBCODE as gbCode,CONCAT(d.ENAME,',',c.ENAME) as spec "+
                    "from ps_c_sku a "+
                    "left join ps_c_pro b on a.PS_C_PRO_ID = b.ID "+
                    "left join ps_c_specobj c on c.ID=a.PS_C_SPEC1OBJ_ID "+
                    "left join PS_C_SPECOBJ d on d.ID = a.PS_C_SPEC2OBJ_ID "+
                    "where " +
                    "<foreach item='item' index='index' collection='skuEcodeList' separator='or'>" +
                    "a.ecode = #{item} " +
                    "</foreach>" +
                    "</script>")
    List<goodsResult> queryListByskuEcode(@Param("skuEcodeList") List<String> skuEcodeList);


    @Select("SELECT id FROM ps_c_pro WHERE ecode=#{ecode}")
    Long selectMainIdByEcode(@Param("ecode") String ecode);

    //根据商品编码查询商品档案中颜色组与尺寸组
    @Select("select pro.ecode  pcode,cor.ecode as cgroup,shp.ecode as sgroup from ps_c_pro pro\n" +
            "left join ps_c_specgroup cor on pro.ps_c_spec1group_id = cor.id\n" +
            "left join ps_c_specgroup shp on pro.ps_c_spec2group_id = shp.id\n" +
            "WHERE pro.ecode = #{ecode} and pro.ISACTIVE = 'Y' ")
    JSONObject queryColorAndSizeGroupCode(@Param("ecode") String goodsCode);

    @Select("select * from ps_c_pro where ecode= #{ecode}")
    PsCPro selectProbyEcode(@Param("ecode") String goodsCode);

    @SelectProvider(type = QuerySkuSqlProvider.class, method = "selectPdaQueryProsql")
    List<PdaQueryProResult> queryProByCodeAndName(List<String> proEcodeList, List<String> proEnameList, String isactive);

    @Select("<script> "
            + "select * from \n"
            + "ps_c_pro  b\n"
            + "where  b.id in <foreach item='item' index='index' collection='proList' open='(' separator=',' close=')'> #{item} </foreach> "
            + " and b.ISACTIVE = 'Y'"
            + "</script>")
    List<PsCPro> selByIds(@Param("proList") List<Long> proList);

    @Select("select * from ps_c_pro where ecode=#{ecode}")
    PsCPro queryPsCPro(@Param("ecode") String ecode);

    @Select("select id from PS_C_SPECOBJ where ecode='RYYTN' and PS_C_SPEC_ID='1'")
    Long colorId();

    @Select("select id from PS_C_SPECOBJ where ecode='RYYTN' and PS_C_SPEC_ID='2'")
    Long sizeId();

    @Select("select t.* from PS_C_PRODIM_ITEM t,PS_C_PRO p where p.M_DIM3_ID=t.id and p.ecode=#{ecode}")
    PsCProdimItem queryDimItem(@Param("ecode") String ecode);

    @Select("select t.* from PS_C_PRODIM_ITEM t,PS_C_PRO p where p.M_DIM1_ID=t.id and p.ecode=#{ecode}")
    PsCProdimItem queryDim1Item(@Param("ecode") String ecode);

    @Select(
            "<script>" +
                    "select p.*,t.ecode taxClassification,f.ename unit,d.ecode material "+
                    "from ps_c_pro p "+
                    "LEFT JOIN PS_C_PRODIM_ITEM as t ON t.id=p.M_DIM10_ID "+
                    "LEFT JOIN PS_C_PRODIM_ITEM as f ON f.id=p.M_DIM3_ID "+
                    "LEFT JOIN PS_C_PRODIM_ITEM as d ON d.id=p.M_DIM2_ID "+
                    " where p.ecode in " +
                    " <foreach collection='ecodeList' item='ecode' open='(' separator=',' close=')'> #{ecode} " +
                    "</foreach> " +
                    "</script>")
    List<ProExtResult> queryProExtByEcodes(@Param("ecodeList") List<String> ecodeList);

    @Select("<script>" +
            "select p.ecode as skuCode, t.ename as ename from PS_C_PRODIM_ITEM t,PS_C_PRO p where p.M_DIM8_ID = t.id and p.ecode in " +
            " <foreach collection='ecodes' item='ecode' open='(' separator=',' close=')'> #{ecode} " +
            "</foreach> " +
            "</script>")
    List<Dim8Result> queryDim8Item(@Param("ecodes") List<String> ecodes);

    @Select("select count(*) from ps_c_pro where ECODE = #{ecode}")
    int proCountByCode(@Param("ecode") String ecode);

    @Select("<script> "
            + "select * from \n"
            + "ps_c_pro  b\n"
            + "where  b.ecode in <foreach item='item' index='index' collection='proList' open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<PsCPro> queryProAllByEcode(@Param("proList") List<String> proList);

    @Select("SELECT * FROM ps_c_pro WHERE m_dim2_id IN (SELECT id FROM ps_c_prodim_item WHERE ecode = #{ecode})")
    List<PsCPro> queryByDim2Ecode(@Param("ecode") String ecode);
}