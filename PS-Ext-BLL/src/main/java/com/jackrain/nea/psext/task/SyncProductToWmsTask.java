package com.jackrain.nea.psext.task;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.xxl.job.starter.helper.R3XxlJobParamHelper;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.psext.services.PushProductToWmsService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SyncProductToWmsTask implements IR3Task {

    @Autowired
    PushProductToWmsService pushProductToWmsService;

    @Override
    @XxlJob(value = "SyncProductToWmsTask")
    public RunTaskResult execute(JSONObject params) {
        params = R3XxlJobParamHelper.xxlParam2R3Json();

        log.info(LogUtil.format("=============SyncProductToWmsTask beginning====================="));

        try {
            RunTaskResult result = new RunTaskResult();
            ValueHolderV14 holderV14 = pushProductToWmsService.pushProductToWMS();
            if (holderV14.getCode() == ResultCode.FAIL) {
                log.error(LogUtil.format("SyncProductToWmsTask.result：" + holderV14.getMessage()));
                result.setSuccess(false);
                result.setMessage(holderV14.getMessage());
            } else {
                result.setSuccess(true);
            }
            return result;

        } catch (Exception e) {
            log.error(LogUtil.format("SyncProductToWmsTask===异常！！！{}"), Throwables.getStackTraceAsString(e));
        }
        return null;
    }
}
