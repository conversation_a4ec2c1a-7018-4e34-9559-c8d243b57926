package com.jackrain.nea.psext.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.psext.mapper.PsCUnitConvertMapper;
import com.jackrain.nea.psext.model.table.PsCUnitConvert;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> lin yu
 * @date : 2022/6/13 下午4:49
 * @describe :
 */

@Component
@Slf4j
public class PsCUnitConvertDelService {

    @Autowired
    private PsCUnitConvertMapper mapper;

    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);

        int count = 0;

        if (param != null) {

            long id = param.getLong("objid");

            PsCUnitConvert unitConvert = mapper.selectById(id);

            if (unitConvert == null) {
                throw new NDSException(Resources.getMessage("当前记录已不存在！", querySession.getLocale()));
            }

            mapper.deleteById(id);
            count++;
        }

        vh.put("code", 0);
        vh.put("message", Resources.getMessage("删除成功的记录数：" + count, querySession.getLocale()));

        return vh;
    }
}
