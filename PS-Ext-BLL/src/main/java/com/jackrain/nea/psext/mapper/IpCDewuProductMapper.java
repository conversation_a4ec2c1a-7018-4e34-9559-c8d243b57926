package com.jackrain.nea.psext.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.psext.model.table.IpCDewuProduct;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @ClassName IpCDewuProductMapper
 * @Description 得物库存同步
 * <AUTHOR>
 * @Date 2025/1/17 10:17
 * @Version 1.0
 */
@Mapper
public interface IpCDewuProductMapper extends ExtentionMapper<IpCDewuProduct> {

    @Select("select * from ip_c_dewu_product where sku_id = #{skuId} and dewu_warehouse_code = #{dewuWarehouseCode}")
    List<IpCDewuProduct> queryBySkuIdAndDewuWarehouseCode(@Param("skuId") Long skuId, @Param("dewuWarehouseCode") Long dewuWarehouseCode);
}
