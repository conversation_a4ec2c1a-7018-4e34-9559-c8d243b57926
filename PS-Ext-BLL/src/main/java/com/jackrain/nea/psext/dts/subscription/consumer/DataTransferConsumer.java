//package com.jackrain.nea.psext.dts.subscription.consumer;
//
//import com.alibaba.fastjson.JSON;
//import com.aliyun.dts.subscribe.clients.common.RecordListener;
//import com.aliyun.dts.subscribe.clients.record.DefaultUserRecord;
//import com.aliyun.dts.subscribe.clients.record.OperationType;
//import com.aliyun.dts.subscribe.clients.record.RecordSchema;
//import com.aliyun.dts.subscribe.clients.record.RowImage;
//import com.aliyun.dts.subscribe.clients.record.value.Value;
//import com.google.common.base.Throwables;
//import com.jackrain.nea.cpext.api.CpCDtsFailCmd;
//import com.jackrain.nea.cpext.model.table.CpCDtsFail;
//import com.jackrain.nea.exception.NDSRuntimeException;
//import com.jackrain.nea.psext.converter.BeanProcessorFactory;
//import com.jackrain.nea.psext.converter.R3Processor;
//import com.jackrain.nea.psext.utils.DateTimeHelper;
//import com.jackrain.nea.sys.domain.BaseModel;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.dubbo.config.annotation.Reference;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.math.BigDecimal;
//import java.util.Date;
//import java.util.HashMap;
//import java.util.Map;
//import java.util.Optional;
//
///**
// * @ClassName : DataTransferConsumer
// * @Description :
// * <AUTHOR> CD
// * @Date: 2021-07-26 13:40
// */
//@Component
//@Slf4j
//public class DataTransferConsumer implements RecordListener {
//    @Autowired
//    private BeanProcessorFactory beanProcessorFactory;
//
//    @Autowired
//    private BaseProcessorUtil baseProcessorUtil;
//    @Reference(group = "cp-ext", version = "1.0")
//    private CpCDtsFailCmd cpCDtsFailCmd;
//
//    //开始更新时间：0:不做限制;1:yyyy-MM-dd HH:mm:ss
//    @org.springframework.beans.factory.annotation.Value("${r3.dts.update.start.time:0}")
//    private String updateStartTime = "0";
//
//    /**
//     * 消费DTS 传输数据处理器
//     * @param defaultUserRecord
//     */
//    @Override
//    public void consume(DefaultUserRecord defaultUserRecord) {
//        try {
//            SyncResult syncResult = new SyncResult();
//            OperationType operationType  =  defaultUserRecord.getOperationType();
//            if(!operationType.equals(OperationType.INSERT)&&!operationType.equals(OperationType.UPDATE)&&!operationType.equals(OperationType.DELETE)){
//                return ;
//            }
//            R3Processor<RowImage, BaseModel> r3Processor = beanProcessorFactory.match(defaultUserRecord.getSchema().getTableName().get());
//            if (null == r3Processor) {
//                log.warn(" the message ignored, please check the table processor. ");
//                return;
//            }
//            RowImage rowImage = defaultUserRecord.getAfterImage();
//            if (null != rowImage) {
//                Value<BigDecimal> tableId = rowImage.getValue("ID");
//                if (null == tableId) {
//                    log.warn(" defaultUserRecord id is null, please check the table data. ");
//                    return ;
//                }
//            }
//            if(log.isDebugEnabled()) {
//                log.debug(" Record  meesage info:{}",getRecordMessageID(defaultUserRecord));
//            }
//            if (defaultUserRecord.getOperationType() == OperationType.INSERT) {
//                if (!judgeOperationByUpdateTime(rowImage)) {
//                    return;
//                }
//                syncResult = baseProcessorUtil.operateInsertModel(r3Processor.getMapper(), r3Processor.convert(rowImage), defaultUserRecord);
//            } else if (defaultUserRecord.getOperationType() == OperationType.UPDATE) {
//                if (!judgeOperationByUpdateTime(rowImage)) {
//                    return;
//                }
//                BaseModel baseModel = r3Processor.convert(rowImage);
//                int result = r3Processor.getMapper().updateById(baseModel);
//                if (result == 0) {
//                    syncResult = baseProcessorUtil.operateInsertModel(r3Processor.getMapper(), baseModel, defaultUserRecord);
//                } else {
//                    syncResult = new SyncResult(true, "", defaultUserRecord);
//                }
//            } else if (defaultUserRecord.getOperationType() == OperationType.DELETE) {
//                if (!judgeOperationByUpdateTime(defaultUserRecord.getBeforeImage())) {
//                    return;
//                }
//                Optional.ofNullable(defaultUserRecord.getBeforeImage().getValue("ID")).orElseThrow(()->new NDSRuntimeException(" the DTS Image not have ID key."));
//                r3Processor.getMapper().deleteById(defaultUserRecord.getBeforeImage().getValue("ID").getData().toString());
//                syncResult = new SyncResult(true, "", defaultUserRecord);
//            } else {
//                syncResult = new SyncResult(true, "", defaultUserRecord);
//            }
//        }catch(RuntimeException e){
//            log.error(" consumer Dts is failed. {}", Throwables.getStackTraceAsString(e));
//            try {
//                CpCDtsFail dtsFail = new CpCDtsFail();
//                dtsFail.setTableName(defaultUserRecord.getSchema().getTableName().get());
//                dtsFail.setOperationType(defaultUserRecord.getOperationType().name());
//                RowImage rowImage = defaultUserRecord.getAfterImage();
//                Value<BigDecimal> tableId = rowImage.getValue("ID");
//                dtsFail.setTableId(tableId.getData().longValue());
//                Map<String, Object> map = new HashMap<>();
//                for (String fieldName : defaultUserRecord.getSchema().getFieldNames()) {
//                    Value value = rowImage.getValue(fieldName);
//                    if (null == value) continue;
//                    map.put(fieldName, value.toString());
//                }
//                dtsFail.setData(JSON.toJSONString(map));
//                dtsFail.setErrorInfo(e.getMessage());
//                cpCDtsFailCmd.saveData(dtsFail);
//            } catch (Exception e1) {
//                log.error("record error data push cp fail. e:{}", Throwables.getStackTraceAsString(e1));
//            }
//        }finally {
//            defaultUserRecord.commit(String.valueOf(System.currentTimeMillis()));
//        }
//    }
//
//    private boolean judgeOperationByUpdateTime(RowImage rowImage) {
//        boolean bl = true;
//        try {
//            if (!"0".equals(updateStartTime) && DateTimeHelper.isValidDate(updateStartTime, DateTimeHelper.YYYY_MM_DD_HH_MM_SS)) {
//                Value<String> modifieddateValue = rowImage.getValue("MODIFIEDDATE");
//                if (null != modifieddateValue) {
//                    Date modifieddate = DateTimeHelper.stringToDate(modifieddateValue.toString(), DateTimeHelper.YYYY_MM_DD_HH_MM_SS);
//                    Date updateStartDate = DateTimeHelper.stringToDate(updateStartTime, DateTimeHelper.YYYY_MM_DD_HH_MM_SS);
//                    if (modifieddate.getTime() < updateStartDate.getTime()) {
//                        //如果数据的更新时间小于设置的开始更新时间，则返回false
//                        bl = false;
//                    }
//                }
//            }
//        } catch (Exception e) {
//            log.error("DataTransferConsumer.judgeOperationByUpdateTime error {}", e);
//        }
//        return bl;
//    }
//
//    private String getRecordMessageID(DefaultUserRecord record){
//        RecordSchema recordSchema = record.getSchema();
//        StringBuilder stringBuilder = new StringBuilder();
//        stringBuilder
//                .append("\n")
//                // record id can not be used as unique identifier
//                .append("RecordID [").append(record.getId()).append("]\n")
//                // record generate timestamp in source log
//                .append("RecordTimestamp [").append(record.getSourceTimestamp()).append("] \n")
//                // source info contains which source this record came from
//                .append("Source [").append(recordSchema.getDatabaseInfo()).append("]\n")
//                // record type
//                .append("RecordType [").append(record.getOperationType()).append("]\n")
//                .append("table [").append(record.getSchema().getTableName().get()).append("]\n");
//        return stringBuilder.toString();
//    }
//}
