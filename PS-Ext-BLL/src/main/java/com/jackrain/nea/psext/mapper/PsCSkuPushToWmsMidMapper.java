package com.jackrain.nea.psext.mapper;

import com.jackrain.nea.hub.request.naika.NaiKaSkuSyncRequest;
import com.jackrain.nea.ip.model.jingdong.request.JdSyncSkuRequest;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.psext.model.table.PsCSkuPushWmsMidModel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * Description:
 *
 * @Author: guo.kw
 * @Since: 2022/7/18
 * create at: 2022/7/18 18:07
 */
@Mapper
public interface PsCSkuPushToWmsMidMapper extends ExtentionMapper<PsCSkuPushWmsMidModel> {
    @Select("SELECT * FROM PS_C_SKU_PUSH_WMS_MID WHERE THIRD_PARTY_SYSTEM_NAME = 'QMWMS' and WMS_STATUS in (0, 1, 3) AND FAILURE_NUM < 6 LIMIT #{size}")
    List<PsCSkuPushWmsMidModel> queryPushWmsList(@Param("size") Integer size);

    @Select("SELECT * FROM PS_C_SKU_PUSH_WMS_MID WHERE THIRD_PARTY_SYSTEM_NAME = 'JDWMS' and WMS_STATUS in (0, 1, 3) AND FAILURE_NUM < 6 LIMIT #{size}")
    List<PsCSkuPushWmsMidModel> queryPushJdWmsList(@Param("size") Integer size);


    @Select("SELECT * FROM PS_C_SKU_PUSH_WMS_MID WHERE THIRD_PARTY_SYSTEM_NAME = 'DBWMS' and WMS_STATUS in (0, 1, 3) AND FAILURE_NUM < 6 LIMIT #{size}")
    List<PsCSkuPushWmsMidModel> queryPushDbWmsList(@Param("size") Integer size);

    @Select("SELECT * FROM PS_C_SKU_PUSH_WMS_MID WHERE THIRD_PARTY_SYSTEM_NAME = 'FLWMS' and WMS_STATUS in (0, 1, 3) AND FAILURE_NUM < 6 LIMIT #{size}")
    List<PsCSkuPushWmsMidModel> queryPushFLWmsList(@Param("size") Integer size);

    @Update("UPDATE PS_C_SKU_PUSH_WMS_MID SET WMS_STATUS = #{wmsStatus},MODIFIEDDATE = NOW() WHERE id IN (${ids}) ")
    int updateBatchByIds(@Param("wmsStatus") Integer wmsStatus, @Param("ids") String ids);

    /**
     * 查询第三方系统是京东云仓，并且是未传，传中，或（失败且失败次数小于6次）
     *
     * @param limit
     * @return
     */
    @Select("SELECT m.id mainTableId,m.OPERATION_TYPE actionType,m.WMS_WAREHOUSE_CODE warehouseCode,m.PS_C_SKU_ECODE itemCode," +
            "t.THIRD_PARTY_SKU_CODE itemId,m.PS_C_SKU_ENAME itemName,m.BARCODE barCode,m.ITEM_TYPE itemType,s.LENGTH length,s.WIDTH width,s.HEIGHT height," +
            "s.bulk volume,s.WEIGHT grossWeight,s.net_weight netWeight,p.isactive isValid,p.simple_ename shortName," +
            "p.ps_c_brand_id brandName,p.is_enable_expiry isSNMgmt,NOW() createTime,NOW() updateTime " +
            "FROM `ps_c_sku_push_wms_mid` m " +
            "LEFT JOIN ps_c_sku s on s.id = m.PS_C_SKU_ID " +
            "LEFT JOIN ps_c_pro p on p.id = s.PS_C_PRO_ID " +
            "LEFT JOIN ps_c_third_system_sku t on t.SKE_CODE = m.PS_C_SKU_ECODE " +
            "WHERE m.THIRD_PARTY_SYSTEM_NAME = 'JDWMS' and m.WMS_STATUS IN (0,1,3) " +
            "and m.FAILURE_NUM < 6 ORDER BY m.id LIMIT #{limit}")
    List<JdSyncSkuRequest> queryToJdSku(@Param("limit") Integer limit);

    /**
     * 查询第三方系统是奶卡，并且是未传，传中，或（失败且失败次数小于6次）
     * @param limit
     * @return
     */
    @Select("SELECT m.id mainTableId,m.PS_C_SKU_ECODE skuCode,m.PS_C_SKU_ENAME skuName, " +
            "CASE WHEN m.ISACTIVE = 'Y' THEN 0 ELSE 1 END AS delFlag " +
            "FROM `ps_c_sku_push_wms_mid` m  " +
            "WHERE m.THIRD_PARTY_SYSTEM_NAME = 'NK' " +
            "AND m.WMS_STATUS IN ( 0, 1, 3 )  " +
            "AND m.FAILURE_NUM < 6 " +
            "ORDER BY m.id LIMIT #{limit}")
    List<NaiKaSkuSyncRequest> querySkuMidToNaiKa(@Param("limit") Integer limit);
}
