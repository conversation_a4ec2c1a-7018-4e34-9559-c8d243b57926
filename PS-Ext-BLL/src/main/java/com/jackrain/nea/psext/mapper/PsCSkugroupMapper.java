package com.jackrain.nea.psext.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.psext.model.table.PsCSkugroup;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

@Mapper
public interface PsCSkugroupMapper extends ExtentionMapper<PsCSkugroup> {

    /**
     * 根据条码档案表id查询子子表商品组合明细表数据
     *
     * @param psCSkuId
     * @return
     */
    @Select("SELECT * FROM PS_C_SKUGROUP WHERE PS_C_SKU_GRP_ID = #{psCSkuId} limit #{startIndex},#{count}")
    List<PsCSkugroup> queryPsCSkuInfoByProId(@Param("psCSkuId") Long psCSkuId, @Param("startIndex") Integer startIndex, @Param("count") Integer count);

    @Select("SELECT * FROM PS_C_SKUGROUP WHERE PS_C_SKU_GRP_ID = #{psCSkuId} limit #{groupIndex},#{groupCount}")
    List<PsCSkugroup> queryPsCSkuInfoByPId(@Param("psCSkuId") Long psCSkuId, @Param("groupIndex") Long groupIndex, @Param("groupCount") Long groupCount);

    @Select("select count(1) from PS_C_SKUGROUP where PS_C_SKU_GRP_ID = #{psCSkuId}")
    Integer queryCount(@Param("psCSkuId") Long psCSkuId);

    /**
     * 根据商品ID获取skuGroup信息
     *
     * @param proId
     * @return
     */
    @Select("select * from ps_c_skugroup where ps_c_pro_id=#{proId} group by ps_c_sku_ecode")
    List<PsCSkugroup> selectSkuGroupInfoByProId(@Param("proId") Long proId);

    /**
     * 根据skuEcode获取skuId
     *
     * @param skuEcode
     * @param proId
     * @return
     */
    @Select("select * from ps_c_skugroup where ps_c_sku_ecode = #{skuEcode} and ps_c_pro_id=#{proId}")
    List<PsCSkugroup> selectSkuIdByEcode(@Param("skuEcode") String skuEcode, @Param("proId") Long proId);

    /**
     * 根据skuId获取skuGroup信息
     *
     * @param skuEcode
     * @return
     */
    @Select("select * from ps_c_skugroup where ps_c_sku_ecode=#{skuEcode}")
    List<PsCSkugroup> selectSkuGroupInfoBySkuEcode(@Param("skuEcode") String skuEcode);

    /**
     * 根据skuId查询明细信息
     *
     * @param psCSkuId
     * @return
     */
    @Select("select * from ps_c_skugroup where ps_c_sku_grp_id =#{psCSkuId}")
    List<PsCSkugroup> selectBySkuId(@Param("psCSkuId") Long psCSkuId);

    @Select("select ware_type from ps_c_sku where ECODE = #{splitGroupProSku}")
    Integer selectGroupType(@Param("splitGroupProSku") String splitGroupProSku);

    @Select("select count(*) from ps_c_skugroup where ps_c_sku_ecode =#{psCSkuEcode} and isactive='Y'")
    int selectSkuGroupInfo(@Param("psCSkuEcode") String psCSkuEcode);

    @Select("select ps_c_pro_ecode,groupnum from ps_c_skugroup where ps_c_sku_id =#{skuId} and ps_c_sku_grp_id=#{skuGrpId}")
    PsCSkugroup selectSkuGroup(@Param("skuId") Long skuId, @Param("skuGrpId") Long skuGrpId);

    @Select("select * from ps_c_skugroup where ps_c_sku_ecode =#{psCSkuEcode} and isactive='Y'")
    List<PsCSkugroup> selectSkuGroupBySkuEcode(@Param("psCSkuEcode") String psCSkuEcode);

    /**
     * 根据主表ID查询SKUGROUP信息
     *
     * @param proId
     * @return
     */
    @Select("SELECT * FROM PS_C_SKUGROUP WHERE PS_C_PRO_ID = #{proId}")
    List<PsCSkugroup> selectByProId(@Param("proId") Long proId);


    @Select("select * from ps_c_skugroup t where t.ps_c_sku_grp_ecode=#{psCSkuEcode} and t.ps_c_pro_id in (select s.id from ps_c_pro_group s where s.cp_c_store_ids like CONCAT('%',#{cpCStoreId},'%')  and s.ecode=#{psCProEcode} and s.isactive='Y') and t.isactive='Y'")
    List<PsCSkugroup> psSkuGroupQurey(@Param("cpCStoreId") Long cpCStoreId, @Param("psCProEcode") String psCProEcode, @Param("psCSkuEcode") String psCSkuEcode);

    /**
     * 根据虚拟条码id查询真实条码
     * @param virtualSkuId
     * @return
     */
    @Select(" select * from ps_c_skugroup where ps_c_sku_grp_id = #{virtualSkuId} " )
    List<PsCSkugroup> querySkuGroupList(@Param("virtualSkuId") Long virtualSkuId);

    /**
     * 删除虚拟条码
     * @param virtualSkuList
     */
    @Delete(" <script> delete from ps_c_skugroup where isactive='N' and ps_c_sku_grp_ecode in " +
            " <foreach collection='ecodes' item='item' open='(' separator=',' close=')'> #{item}  </foreach>  </script>")
    void deleteRealSku(@Param("ecodes") List<String> virtualSkuList);

    @Select(" select * from ps_c_skugroup where ps_c_sku_grp_id in ${idsStr} and isactive = 'Y'")
    List<PsCSkugroup> queryGroupPushWmsList(@Param("idsStr") String idsStr);

    /**
     * 查询组合商品关联的sku是否允许拆单
     * @param skuIdList 条码id
     * @return 允许拆单的条码id
     */
    @Select({
            "<script>",
            " SELECT ",
            " s.ps_c_sku_id ",
            " FROM ps_c_skugroup s ",
            " LEFT JOIN ps_c_pro_group p on s.ps_c_pro_id=p.id ",
            " WHERE " ,
            " s.ps_c_sku_id in " ,
            " <foreach collection='skuIdList' item='skuId' open='(' separator=',' close=')'> #{skuId, jdbcType=INTEGER} </foreach>" +
            " AND p.can_split='Y' ",
            "</script>"
    })
    List<Long> queryCanSplitSku(@Param("skuIdList")List<Long> skuIdList);

    @Select({"<script>",
            "select c.ecode ps_c_pro_ecode,d.ps_c_pro_id,d.ps_c_sku_id,d.num " +
            "from ps_c_pro_group c left join ps_c_skugroup d on c.id = d.ps_c_pro_id " +
            "where c.id in ( " +
            "select a.id from ps_c_pro_group a inner join ps_c_skugroup b on a.id = b.ps_c_pro_id " +
            "where b.ps_c_sku_id = #{psCSkuId} and b.num = #{num} and a.can_split = #{canSplit} and a.ISACTIVE = 'Y' and b.ISACTIVE = 'Y' " +
            ") and c.ISACTIVE = 'Y' and d.ISACTIVE = 'Y'",
            "</script>"})
    List<PsCSkugroup> checkSku(@Param("psCSkuId") Long psCSkuId, @Param("canSplit") String canSplit, @Param("num") BigDecimal num);


    @Select("select * from ps_c_skugroup where ps_c_pro_id in (select id from ps_c_pro_group where ecode = #{groupEcode} and ISACTIVE = 'Y') and ISACTIVE = 'Y'")
    List<PsCSkugroup> selectByGroupEcode(@Param("groupEcode") String groupEcode);
}