package com.jackrain.nea.psext.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.model.result.SgGroupStorageQueryResult;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.data.basic.model.request.SkuInfoQueryRequest;
import com.jackrain.nea.data.basic.services.BasicPsQueryService;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.enums.IsActiveEnum;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.psext.common.PsExtConstantsIF;
import com.jackrain.nea.psext.mapper.PsCProGroupMapper;
import com.jackrain.nea.psext.mapper.PsCSkuMapper;
import com.jackrain.nea.psext.mapper.PsCSkugroupMapper;
import com.jackrain.nea.psext.model.table.ExtractLuckyBag;
import com.jackrain.nea.psext.model.table.PsCProGroup;
import com.jackrain.nea.psext.model.table.PsCSku;
import com.jackrain.nea.psext.model.table.PsCSkugroup;
import com.jackrain.nea.psext.model.table.SingleProInfo;
import com.jackrain.nea.psext.model.table.SplitVirtualPro;
import com.jackrain.nea.psext.request.LuckyBagGenerateRealProRequest;
import com.jackrain.nea.psext.request.LuckyBagGenerateRequest;
import com.jackrain.nea.psext.request.VirtualProSplitRequest;
import com.jackrain.nea.psext.rpc.RpcSgService;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> lin yu
 * @since 2019/7/11
 * create at : 2019/7/11 13:45
 */
@Component
@Slf4j
public class VirtualProSplitService {

    @Autowired
    private PsCSkugroupMapper psCSkugroupMapper;

    @Autowired
    private PsCProGroupMapper psCProGroupMapper;

    @Autowired
    private PsCSkuMapper psCSkuMapper;

    @Autowired
    private BasicPsQueryService basicPsQueryService;

    @Autowired
    private LuckyBagGenerateService luckyBagGenerateService;

    @Autowired
    private RpcSgService sgService;


    public ValueHolderV14<Map<String, List<ExtractLuckyBag>>> excute(VirtualProSplitRequest virtualProSplitRequest) {
        ValueHolderV14<Map<String, List<ExtractLuckyBag>>> holder;

        //入参日志记录
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start.SplitGroupProService.excute.params：") + virtualProSplitRequest);
        }

        //参数校验
        holder = checkParam(virtualProSplitRequest);
        if (ResultCode.FAIL == holder.getCode()) {
            log.error(LogUtil.format("参数校验：") + holder.getMessage());
            return holder;
        }

        //错误信息定义
        List<SplitVirtualPro> splitVirtualProList = virtualProSplitRequest.getSplitVirtualProList();
        int totalNum = splitVirtualProList.size();
        int errorNum = 0;
        StringBuilder errorData = new StringBuilder();

        Map<String, List<ExtractLuckyBag>> resultMap = new HashMap<>();

        //调用拆分方法
        for (SplitVirtualPro splitVirtualPro : splitVirtualProList) {
            holder = groupProSplit(splitVirtualPro, resultMap);
            if (ResultCode.FAIL == holder.getCode()) {
                errorData.append(holder.getMessage());
                errorNum++;
                log.error(LogUtil.format("失败：") + holder.getMessage());
            }
        }

        //结果返回
        if (errorNum > 0) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("总共" + totalNum + "条数据,失败" + errorNum + "条," + "错误信息:" + errorData.toString());
            log.error(LogUtil.format("失败：") + holder.getMessage());
        } else {
            holder.setMessage("拆分服务成功");
            holder.setData(resultMap);
        }
        return holder;
    }

    /**
     * 福袋或组合商品拆分
     *
     * @param splitVirtualPro 要拆分商品的条码
     * @param resultMap       封装的结果集
     * @return 处理结果
     */
    private ValueHolderV14<Map<String, List<ExtractLuckyBag>>> groupProSplit(SplitVirtualPro splitVirtualPro,
                                                                             Map<String, List<ExtractLuckyBag>> resultMap) {
        ValueHolderV14<Map<String, List<ExtractLuckyBag>>> holder = new ValueHolderV14<>(ResultCode.SUCCESS, "");

        //获取要拆分的商品（虚拟条码），以及该商品的件数
        String virtualSku = splitVirtualPro.getVirtualSku();
        Integer num = splitVirtualPro.getNum();

        Long itemId = splitVirtualPro.getItemId();

        //从数据库查询的该虚拟条码下的所有真实商品信息
        List<PsCSkugroup> psCSkuGroupList = this.psCSkugroupMapper.selectList(new QueryWrapper<PsCSkugroup>().lambda()
                .eq(PsCSkugroup::getPsCSkuGrpEcode, virtualSku));
        // 商品IDS
        List<Long> proIds = psCSkuGroupList.stream().map(PsCSkugroup::getPsCProId).collect(Collectors.toList());
        /*
         * 判断该虚拟条码的类型，【正常商品】【福袋商品】【组合商品】【预售商品】
         */
        Integer groupType = this.psCSkugroupMapper.selectGroupType(virtualSku);
        if (groupType == null || groupType == PsExtConstantsIF.NORMAL_PRODUCT
                || groupType == PsExtConstantsIF.PRESELL_PRODUCT) {
            //是正常商品或预售商品时直接返回
            return encapsulateErrorData(virtualSku + ":该条码不属于组合商品或福袋商品");
        } else if (groupType == PsExtConstantsIF.GROUP_PRODUCT) {

            //是组合商品时，直接返回查询到的真实条码信息
            List<SingleProInfo> singleProInfoList = new ArrayList<>();
            List<ExtractLuckyBag> extractLuckyBagList = new ArrayList<>();
            ExtractLuckyBag extractLuckYBag = new ExtractLuckyBag();

            Map<Long, PsCProGroup> proGroupMap = Maps.newHashMap();

            if (!proIds.isEmpty()) {
                proGroupMap = psCProGroupMapper.selectBatchIds(proIds).stream().collect(Collectors.toMap(PsCProGroup::getId, Function.identity()));
            }
            for (PsCSkugroup psCSkugroup : psCSkuGroupList) {
                SingleProInfo singleProInfo = new SingleProInfo();
                BeanUtils.copyProperties(psCSkugroup, singleProInfo);
                singleProInfo.setNum(psCSkugroup.getNum().multiply(new BigDecimal(num)));

                // 判断是否可拆单
                PsCProGroup psCProGroup = proGroupMap.get(psCSkugroup.getPsCProId());

                singleProInfo.setCanSplit(psCProGroup == null ? IsActiveEnum.N.getKey() :
                        Optional.ofNullable(psCProGroup.getCanSplit()).orElse(IsActiveEnum.N.getKey()));
                singleProInfoList.add(singleProInfo);
            }
            extractLuckYBag.setExtractLuckyBag(singleProInfoList);
            extractLuckyBagList.add(extractLuckYBag);

            resultMap.put(virtualSku + itemId, extractLuckyBagList);
            return holder;

        } else if (groupType == PsExtConstantsIF.GOODY_BAG_PRODUCT) {

            //是福袋商品时，从redis中取一个福袋
            String luckyBagKey = PsExtConstantsIF.LUCKY_BAG_PRODUCT_KEY_PREFIX;
            CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
            DefaultRedisScript<List> redisScript = new DefaultRedisScript<>();
            redisScript.setLocation(new ClassPathResource("lua/GetGroupSkuItems.lua"));
            redisScript.setResultType(List.class);

            List<String> keyList = new ArrayList<>();
            keyList.add(luckyBagKey + virtualSku);
            List<String> qtyList = new ArrayList<>();
            qtyList.add(num.toString());
            qtyList.add(String.valueOf(System.currentTimeMillis()));
            List executeResult;
            executeResult = redisTemplate.execute(redisScript, keyList, qtyList.toArray(new String[qtyList.size()]));

            if (CollectionUtils.isEmpty(executeResult)) {
                return encapsulateErrorData("lua脚本调用失败");
            }

            //判断取到的福袋数量是否大于购买数量，状态码【0是成功】【-1是没有key】【】
            if ((Long) (executeResult.get(0)) != 0) {

//                SgGroupStorageQueryRequest sgCombinedCommodityQueryRequest = new SgGroupStorageQueryRequest();
//                List<SgGroupStorageQueryRequest> sgCombinedCommodityQueryRequestList = new ArrayList<>();

                try {
                    //获取虚拟条码信息（【虚拟条码ID】、【虚拟条码对应的品牌ID】）
                    SkuInfoQueryRequest skuInfoQueryRequest = new SkuInfoQueryRequest();
                    skuInfoQueryRequest.setSkuEcodeList(Lists.newArrayList(virtualSku));
                    HashMap<String, PsCProSkuResult> skuInfoByEcode =
                            this.basicPsQueryService.getSkuInfoByEcode(skuInfoQueryRequest);
                    PsCProSkuResult psCProSkuResult = skuInfoByEcode.get(virtualSku);

                    //聚合该虚拟条码下的所有的【真实商品idList】）
                    List<Long> allRealProIdList =
                            psCSkuGroupList.stream().map(PsCSkugroup::getPsCSkuId).collect(Collectors.toList());

                    //通过虚拟条码对应的商品ID查出该虚拟条码的所有【逻辑仓ID】
                    PsCProGroup psCProGroup = this.psCProGroupMapper.selectById(psCProSkuResult.getPsCProId());
                    String cpCStoreIds = psCProGroup.getCpCStoreIds();

                    //封装【查询福袋库存，福袋内各自商品库存服务】入参
                    if (StringUtils.isNotEmpty(cpCStoreIds)) {
                        String[] storeIdArray = cpCStoreIds.split(",");
                        List<String> storeIdStringList = Arrays.asList(storeIdArray);
                        List<Long> storeIdLongList = storeIdStringList.stream().map(Long::parseLong).collect(Collectors.toList());
//                        sgCombinedCommodityQueryRequest.setId(psCProSkuResult.getId());
//                        sgCombinedCommodityQueryRequest.setSkuIds(allRealProIdList);
//                        sgCombinedCommodityQueryRequest.setStoreIds(storeIdLongList);

                        //调用【福袋库存，福袋内各自商品库存服务】
//                        sgCombinedCommodityQueryRequestList.add(sgCombinedCommodityQueryRequest);
                        ValueHolderV14<List<SgGroupStorageQueryResult>> listValueHolderV14 = sgService.queryGroupStorage(psCProSkuResult.getId(), allRealProIdList, storeIdLongList);

                        if (log.isDebugEnabled()) {
                            log.debug(LogUtil.format("查询组合商品库存返回值：param={}"), listValueHolderV14);
                        }
                        if (ResultCode.FAIL == listValueHolderV14.getCode()) {
                            return encapsulateErrorData(listValueHolderV14.getMessage());
                        }

                        List<SgGroupStorageQueryResult> sgGroupStorageQueryResultList = listValueHolderV14.getData();
                        if (log.isDebugEnabled()) {
                            log.debug(LogUtil.format("调用查询库存服务，接收参数：params:{}"), sgGroupStorageQueryResultList);
                        }

                        if (CollectionUtils.isEmpty(sgGroupStorageQueryResultList)) {
                            if (log.isDebugEnabled()) {
                                log.debug(LogUtil.format("随机生成福袋商品"));
                            }
                            List<ExtractLuckyBag> extractLuckyBags = randomlyGenerateLuckyBags(splitVirtualPro);
                            resultMap.put(virtualSku + itemId, extractLuckyBags);
                            return holder;
                        }

                        //封装【福袋生成服务】入参
                        List<LuckyBagGenerateRequest> luckyBagGenerateRequestList = new ArrayList<>();
                        for (SgGroupStorageQueryResult sgGroupStorageQueryResult : sgGroupStorageQueryResultList) {
                            LuckyBagGenerateRequest luckyBagGenerateRequest = new LuckyBagGenerateRequest();

                            Map<Long, BigDecimal> skuStorageList = sgGroupStorageQueryResult.getSku();
                            Set<Long> skuStorageIdSet = skuStorageList.keySet();

                            List<LuckyBagGenerateRealProRequest> luckyBagGenerateRealProRequestList = new ArrayList<>();
                            psCSkuGroupList.forEach(psCSkuGroup -> {
                                LuckyBagGenerateRealProRequest luckyBagGenerateRealProRequest = new LuckyBagGenerateRealProRequest();
                                BeanUtils.copyProperties(psCSkuGroup, luckyBagGenerateRealProRequest);
                                if (skuStorageIdSet.contains(psCSkuGroup.getPsCSkuId())) {
                                    // 库存
                                    luckyBagGenerateRealProRequest.setStorage(skuStorageList.get(psCSkuGroup.getPsCSkuId()));
                                } else {
                                    luckyBagGenerateRealProRequest.setStorage(BigDecimal.ZERO);
                                }
                                luckyBagGenerateRealProRequestList.add(luckyBagGenerateRealProRequest);
                            });

                            luckyBagGenerateRequest.setGroupNum(psCProSkuResult.getGroupExtractNum());
                            luckyBagGenerateRequest.setStoreId(sgGroupStorageQueryResult.getCpCStoreId());
                            // 福袋的库存量
                            luckyBagGenerateRequest.setGroupSkuStorage(sgGroupStorageQueryResult.getQtyStorage());
                            luckyBagGenerateRequest.setLuckyBagGenerateRealProRequestList(luckyBagGenerateRealProRequestList);
                            luckyBagGenerateRequest.setGoodyBagGroupSku(virtualSku);
                            luckyBagGenerateRequestList.add(luckyBagGenerateRequest);
                        }

                        ValueHolderV14 excute = this.luckyBagGenerateService.excute(luckyBagGenerateRequestList);
                        if (ResultCode.FAIL == excute.getCode()) {
                            return encapsulateErrorData(excute.getMessage());
                        }
                        executeResult = redisTemplate.execute(redisScript, keyList, qtyList.toArray(new String[qtyList.size()]));
                    }
                } catch (Exception e) {
                    return encapsulateErrorData(e.getMessage());
                }
            }

            //再次从redis中取出

            if (CollectionUtils.isEmpty(executeResult) || (Long) (executeResult.get(0)) != 0) {
                // return encapsulateErrorData("福袋库存数量不足");
                List<ExtractLuckyBag> extractLuckyBags = randomlyGenerateLuckyBags(splitVirtualPro);
                resultMap.put(virtualSku + itemId, extractLuckyBags);
                return holder;
            }

            Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
            String s = executeResult.get(1).toString();
            List<ExtractLuckyBag> billList = gson.fromJson(s,
                    new TypeToken<List<ExtractLuckyBag>>() {
                    }.getType());
            // 易邵峰2020-07-11 修改：组合商品和福袋商品 的Map Key值更改为：virtualSku+itemId
            resultMap.put(virtualSku + itemId, billList);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("从redis中取出的福袋:{}"), resultMap);
            }
            return holder;
        }
        return holder;
    }

    /**
     * @param splitVirtualPro
     * @return
     */
    private List<ExtractLuckyBag> randomlyGenerateLuckyBags(SplitVirtualPro splitVirtualPro) {
        // 获取要拆分的商品（虚拟条码）
        String virtualSku = splitVirtualPro.getVirtualSku();
        // 件数
        Integer num = splitVirtualPro.getNum();
        PsCSku skuInfo = this.psCSkuMapper.selectSkuByECodeAndWareType(virtualSku, PsExtConstantsIF.GOODY_BAG_PRODUCT);
        // 每组抽取行数
        Integer groupExtractNum = skuInfo.getGroupExtractNum();

        // 该虚拟条码下的所有真实商品信息
        List<PsCSkugroup> skuGroups = this.psCSkugroupMapper.selectBySkuId(skuInfo.getId());
        // 组编号分组
        Map<Integer, List<PsCSkugroup>> skuGroupMap = skuGroups.stream()
                .collect(Collectors.groupingBy(PsCSkugroup::getGroupnum));
        // 福袋购买数量，从福袋池里随机抽取 num 个
        List<ExtractLuckyBag> extractLuckyBags = new ArrayList<>(num);
        for (int i = 0; i < num; i++) {
            ExtractLuckyBag luckyBag = this.generateLuckyBags(groupExtractNum, skuGroupMap);
            extractLuckyBags.add(luckyBag);
        }
        return extractLuckyBags;
    }

    /**
     * 参数校验
     *
     * @param virtualProSplitRequest 虚拟条码集合
     * @return 校验结果
     */
    private ValueHolderV14<Map<String, List<ExtractLuckyBag>>> checkParam(VirtualProSplitRequest virtualProSplitRequest) {
        ValueHolderV14<Map<String, List<ExtractLuckyBag>>> holder = new ValueHolderV14<>(ResultCode.SUCCESS, "");

        if (virtualProSplitRequest == null) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("请求体不能为空");
        }

        if (virtualProSplitRequest != null && CollectionUtils.isEmpty(virtualProSplitRequest.getSplitVirtualProList())) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("请求的虚拟条码不能为空");
        }

        return holder;
    }

    private ValueHolderV14<Map<String, List<ExtractLuckyBag>>> encapsulateErrorData(String s) {
        ValueHolderV14<Map<String, List<ExtractLuckyBag>>> holderV14 = new ValueHolderV14<>();
        holderV14.setCode(ResultCode.FAIL);
        holderV14.setMessage(s);
        log.error(LogUtil.format("出参：") + s);
        return holderV14;
    }

    /**
     * 库存不足时，根据虚拟sku随机生成福袋
     *
     * @param virtualProSplitRequest 福袋抽取请求参数
     * @return ValueHolderV14<Map < String, List < ExtractLuckyBag>>>
     */
    public ValueHolderV14<Map<String, List<ExtractLuckyBag>>> randomlyGenerateLuckyBags(VirtualProSplitRequest virtualProSplitRequest) {

        this.checkParam(virtualProSplitRequest);

        ValueHolderV14<Map<String, List<ExtractLuckyBag>>> vh = new ValueHolderV14<>();

        List<SplitVirtualPro> splitVirtualProList = virtualProSplitRequest.getSplitVirtualProList();

        Map<String, List<ExtractLuckyBag>> resultMap = new HashMap<>();

        for (SplitVirtualPro splitVirtualPro : splitVirtualProList) {
            List<ExtractLuckyBag> extractLuckyBags = randomlyGenerateLuckyBags(splitVirtualPro);
            resultMap.put(splitVirtualPro.getVirtualSku() + splitVirtualPro.getItemId(), extractLuckyBags);
        }
        vh.setMessage("随机抽取福袋成功");
        vh.setCode(ResultCode.SUCCESS);
        vh.setData(resultMap);
        return vh;
    }

    private ExtractLuckyBag generateLuckyBags(Integer extractNum, Map<Integer, List<PsCSkugroup>> skuGroupMap) {
        List<SingleProInfo> singleProInfoList = new ArrayList<>();
        // sku分组后，
        for (Map.Entry<Integer, List<PsCSkugroup>> entry : skuGroupMap.entrySet()) {
            // 从每组中抽取商品
            List<PsCSkugroup> cSkuGroups = entry.getValue();
            if (extractNum > cSkuGroups.size()) {
                throw new NDSException("抽取的数量小于预定数量");
            }
            // 随机排序 cSkuGroups
            Collections.shuffle(cSkuGroups);
            // 抽取【extractNum=福袋抽取的行数】个真实条码
            for (int i = 0; i < extractNum; i++) {
                //Collections.shuffle(cSkuGroups);
                SingleProInfo singleProInfo = new SingleProInfo();
                // 真实商品条码ecode
                singleProInfo.setPsCSkuEcode(cSkuGroups.get(i).getPsCSkuEcode());
                // 每个真实商品抽取的件数
                singleProInfo.setNum(cSkuGroups.get(i).getNum());
                singleProInfo.setRatio(cSkuGroups.get(i).getRatio());
                singleProInfoList.add(singleProInfo);
            }
        }
        ExtractLuckyBag extractLuckYBag = new ExtractLuckyBag();
        extractLuckYBag.setExtractLuckyBag(singleProInfoList);
        return extractLuckYBag;
    }

    /**
     * 查询组合商品关联的sku是否允许拆单
     * @param psCSkuIds 条码id
     * @return 是否允许拆单的条码id Map
     */
    public ValueHolderV14<Map<Long, Boolean>> querySkuIsSplit(List<Long> psCSkuIds) {
        Map<Long, Boolean> isSplitMap = new HashMap<>(16);
        ValueHolderV14<Map<Long, Boolean>> vh = new ValueHolderV14<>(ResultCode.SUCCESS, "success!");
        if (CollectionUtils.isNotEmpty(psCSkuIds)) {
            log.info(LogUtil.format("psCSkuIds={}", "VirtualProSplitService.querySkuIsSplit",
                    JSONObject.toJSONString(psCSkuIds)));
            List<Long> skuIdList = psCSkuIds.stream().distinct().collect(Collectors.toList());
            List<Long> splitSkuIds = psCSkugroupMapper.queryCanSplitSku(skuIdList);
            boolean notEmpty = CollectionUtils.isNotEmpty(splitSkuIds);
            for (Long skuId : skuIdList) {
                if (notEmpty) {
                    if (splitSkuIds.contains(skuId)) {
                        isSplitMap.put(skuId, true);
                    } else {
                        isSplitMap.put(skuId, false);
                    }
                } else {
                    isSplitMap.put(skuId, false);
                }
            }
        }
        vh.setData(isSplitMap);
        return vh;
    }
}
