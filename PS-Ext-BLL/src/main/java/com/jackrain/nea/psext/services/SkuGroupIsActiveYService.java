package com.jackrain.nea.psext.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.api.SgGroupStorageCalculateCmd;
import com.burgeon.r3.sg.basic.api.SgStorageQueryCmd;
import com.burgeon.r3.sg.basic.model.request.SgGroupStorageCalculateRequest;
import com.burgeon.r3.sg.basic.model.request.SgGroupStorageSkuRequest;
import com.burgeon.r3.sg.basic.model.request.SgGroupStorageVirtualSkuRequest;
import com.burgeon.r3.sg.basic.model.result.SgGoupStorageCalculateResult;
import com.burgeon.r3.sg.basic.model.result.SgGoupStorageGroupSkuResult;
import com.burgeon.r3.sg.basic.model.result.SgGoupStorageSkuResult;
import com.google.common.base.Throwables;
import com.jackrain.nea.data.basic.services.BasicCpQueryService;
import com.jackrain.nea.psext.mapper.PsCProGroupMapper;
import com.jackrain.nea.psext.mapper.PsCProMapper;
import com.jackrain.nea.psext.mapper.PsCSkuMapper;
import com.jackrain.nea.psext.mapper.PsCSkugroupMapper;
import com.jackrain.nea.psext.mapper.SkuGroupIsActiveMapper;
import com.jackrain.nea.psext.model.result.PsProGroupResult;
import com.jackrain.nea.psext.model.table.PsCProGroup;
import com.jackrain.nea.psext.model.table.PsCSku;
import com.jackrain.nea.psext.model.table.PsCSkugroup;
import com.jackrain.nea.psext.request.LuckyBagGenerateRealProRequest;
import com.jackrain.nea.psext.request.LuckyBagGenerateRequest;
import com.jackrain.nea.psext.request.SkuRequest;
import com.jackrain.nea.psext.request.SkuVituralInfoRequest;
import com.jackrain.nea.psext.request.VirtualSkuRealInfoRequest;
import com.jackrain.nea.psext.request.VirtualSkuRequest;
import com.jackrain.nea.psext.rpc.RpcCpService;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: 汪聿森
 * @Date: Created in 2019-07-15 9:14
 * @Description : 组合商品启用
 */

@Component
@Slf4j
public class SkuGroupIsActiveYService {
    @Autowired
    private PsCProGroupMapper psCProGroupMapper;
    @Autowired
    private PsCProMapper psCProMapper;
    @Autowired
    private PsCSkuMapper psCSkuMapper;
    @Autowired
    private PsCSkugroupMapper psCSkugroupMapper;

    @Reference(group = "sg", version = "1.0")
    private SgGroupStorageCalculateCmd sgGroupStorageCalculateCmd;
    @Autowired
    private BasicCpQueryService basicCpQueryService;
    @Reference(group = "sg", version = "1.0")
    private SgStorageQueryCmd sgStorageQueryCmd;
    @Autowired
    private RpcCpService rpcCpService;

    @Autowired
    private LuckyBagGenerateService luckyBagGenerateService;

    /**
     * 提供单对象页面保存时调用
     *
     * @param querySession
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<HashMap<String, String>> startGoods(QuerySession querySession, Long id) {
        ValueHolderV14 vh14 = calculateStorage(querySession, id);
        if (vh14.getCode() == -1) {
            return vh14;
        }
        //主表启用
        setY(id, querySession);
        List<PsCSku> psCSkuList = psCSkuMapper.selectByProId(id);
        for (PsCSku psCSku : psCSkuList) {
            //明细表sku启用
            psCSku.setIsactive("Y");
            psCSku.setModifierid(querySession.getUser().getId() + 0L);
            psCSku.setModifiername(querySession.getUser().getName());
            psCSku.setModifierename(querySession.getUser().getEname());
            psCSku.setModifieddate(new Date());
            psCSkuMapper.updateById(psCSku);
            List<PsCSkugroup> psCSkugroupList = psCSkugroupMapper.selectBySkuId(psCSku.getId());
            for (PsCSkugroup psCSkugroup : psCSkugroupList) {
                //明细表skuGroup启用
                psCSkugroup.setIsactive("Y");
                psCSkugroup.setModifierid(querySession.getUser().getId() + 0L);
                psCSkugroup.setModifiername(querySession.getUser().getName());
                psCSkugroup.setModifierename(querySession.getUser().getEname());
                psCSkugroup.setModifieddate(new Date());
                psCSkugroupMapper.updateById(psCSkugroup);
            }
        }
        addRedis(id);
        addRealRedis(id);
        vh14.setCode(0);
        vh14.setData(vh14.toString());
        return vh14;
    }

    public ValueHolder execute(QuerySession querySession) {
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("SkuGroupIsActiveYService.Receive.Param：{}"), JSON.toJSONString(param));
        }
        SkuGroupIsActiveMapper mapper = ApplicationContextHandle.getBean(SkuGroupIsActiveMapper.class);
        JSONArray ids = param.getJSONArray("ids");
        JSONArray errorInfo = new JSONArray();
        JSONArray proEcodes = new JSONArray();
        for (Object id : ids) {
            PsCProGroup psCProGroup = psCProGroupMapper.selectById(Long.valueOf(String.valueOf(id)));
           /* String storeIdStr = psCProGroup.getCpCStoreIds();
            List<Long> idList = new ArrayList<>();
            String[] d = storeIdStr.split(",");
            long[] strArrNum = (long[]) ConvertUtils.convert(d, long.class);
            for (int i = 0; i < strArrNum.length; i++) {
                Collections.addAll(idList, strArrNum[i]);
            }*/
            JSONObject jsonObj = new JSONObject();
            if ("N".equals(psCProGroup.getIsactive())) {
                List<PsCSku> psCSkuList = psCSkuMapper.selectByProId(Long.valueOf(String.valueOf(id)));
                List<PsCSkugroup> psCSkugroups = psCSkugroupMapper.selectByProId(Long.valueOf(String.valueOf(id)));
                if (CollectionUtils.isEmpty(psCSkuList)) {
                    jsonObj.put("objid", id);
                    jsonObj.put("ecode", -1);
                    jsonObj.put("message", "商品【" + psCProGroup.getEname() + "】下无明细");
                    errorInfo.add(jsonObj);
                } else {
                    StringBuilder sbSku = new StringBuilder();
                    //StringBuilder sbCalStore=new StringBuilder();
                    // StringBuilder sbSkuGroup=new StringBuilder();
                    // String  psCProEname=null;
                    for (PsCSku psCSku : psCSkuList) {
                        List<PsCSkugroup> psCSkugroupList = psCSkugroupMapper.selectBySkuId(psCSku.getId());
                        if (CollectionUtils.isEmpty(psCSkugroupList)) {
                            sbSku.append(psCSku.getEcode()).append(",");
                        }/*else {
                            for (PsCSkugroup psCSkugroup : psCSkugroupList) {
                                List<PsCSkugroup> isExistSkuGroup = psCSkugroupMapper.selectSkuGroupBySkuEcode(psCSkugroup.getPsCSkuEcode());
                                if (CollectionUtils.isNotEmpty(isExistSkuGroup)) {
                                    // sbSkuGroup.append(psCSkugroup.getPsCSkuEcode()+",");
                                    PsCProGroup isExistPsCProGroup = psCProGroupMapper.selectById(isExistSkuGroup.get(0).getPsCProId());
                                    // psCProEname=isExistPsCProGroup.getEname();
                                }
                            }
                        }*/
                    }
//                    String calMessage=calStorage(psCSkugroups,idList,querySession);
//                    if (calMessage != null) {
//                        sbCalStore.append(calMessage+",");
//                    }
                    StringBuilder sbTotalMessage = new StringBuilder();
                    if (sbSku.length() > 0
                            && !"null".equals(sbSku.toString())
                            && !"".equals(sbSku.toString())) {
                        sbSku.deleteCharAt(sbSku.length() - 1);
                        sbTotalMessage.append("商品【" + psCProGroup.getEname() + "】,虚拟条码【" + sbSku + "】下无明细" + ",");
                    }
//                    if(sbCalStore.length()>0
//                            && !"null".equals(sbCalStore.toString())
//                            && !"".equals(sbCalStore.toString())){
//                        sbCalStore.deleteCharAt(sbCalStore.length()-1);
//                        sbTotalMessage.append(sbCalStore+",");
//                    }

                    /*if(sbSkuGroup.length()>0
                            && !"null".equals(sbSkuGroup.toString())
                            && !"".equals(sbSkuGroup.toString())){
                        sbSkuGroup.deleteCharAt(sbSkuGroup.length()-1);
                        sbTotalMessage.append("实际条码【" + sbSkuGroup + "】已经在商品【" + psCProEname + "】中使用"+",");
                    }*/

                    if (sbTotalMessage.length() <= 0) {
//                        ValueHolderV14 vh14 = calculateStorage(querySession, Long.valueOf(String.valueOf(id)));
//                        if (vh14.getCode() == -1) {
//                            jsonObj.put("objid", id);
//                            jsonObj.put("ecode", -1);
//                            jsonObj.put("message", vh14.getMessage());
//                            errorInfo.add(jsonObj);
//                        } else {
                            //主表启用
                            setY(Long.valueOf(String.valueOf(id)), querySession);
                            for (PsCSku psCSku : psCSkuList) {
                                //明细表sku启用
                                psCSku.setIsactive("Y");
                                psCSku.setModifierid(querySession.getUser().getId() + 0L);
                                psCSku.setModifiername(querySession.getUser().getName());
                                psCSku.setModifierename(querySession.getUser().getEname());
                                psCSku.setModifieddate(new Date());
                                psCSkuMapper.updateById(psCSku);
                                List<PsCSkugroup> psCSkugroupList = psCSkugroupMapper.selectBySkuId(psCSku.getId());
                                for (PsCSkugroup psCSkugroup : psCSkugroupList) {
                                    //明细表skuGroup启用
                                    psCSkugroup.setIsactive("Y");
                                    psCSkugroup.setModifierid(querySession.getUser().getId() + 0L);
                                    psCSkugroup.setModifiername(querySession.getUser().getName());
                                    psCSkugroup.setModifierename(querySession.getUser().getEname());
                                    psCSkugroup.setModifieddate(new Date());
                                    psCSkugroupMapper.updateById(psCSkugroup);
                                }
                            }
                            addRedis(id);
                            addRealRedis(id);
                            proEcodes.add(psCProGroup.getEcode());
//                        }
                    } else {
                        sbTotalMessage.deleteCharAt(sbTotalMessage.length() - 1);
                        jsonObj.put("objid", id);
                        jsonObj.put("ecode", -1);
                        jsonObj.put("message", sbTotalMessage.toString());
                        errorInfo.add(jsonObj);
                    }
                }
            } else {
                jsonObj.put("objid", id);
                jsonObj.put("ecode", -1);
                jsonObj.put("message", "商品【" + psCProGroup.getEname() + "】已启用，不允许启用");
                errorInfo.add(jsonObj);
            }
        }
        // 成功记录数
        int successCount = proEcodes.size();
        // 失败记录数
        int failCount = ids.size() - successCount;
        if (errorInfo.size() > 0) {
            vh.put("data", errorInfo);
            vh.put("code", -1);
            vh.put("message", "成功" + successCount + "条,失败" + failCount + "条");
        } else {
            vh.put("code", 0);
            vh.put("message", "成功" + successCount + "条");
        }
        return vh;
    }

    /**
     * @param id
     * @param querySession
     */
    public void setY(Long id, QuerySession querySession) {
        PsCProGroup psCProGroup = psCProGroupMapper.selectById(id);
//        PsCPro psCPro = psCProMapper.selectById(id);
        psCProGroup.setIsactive("Y");
        psCProGroup.setModifierid(querySession.getUser().getId() + 0L);
        psCProGroup.setModifiername(querySession.getUser().getName());
        psCProGroup.setModifierename(querySession.getUser().getEname());
        psCProGroup.setModifieddate(new Date());
        psCProGroupMapper.updateById(psCProGroup);
//        psCPro.setIsactive("Y");
//        psCPro.setIsGroup("Y");
//        psCPro.setModifierid(querySession.getUser().getId() + 0L);
//        psCPro.setModifiername(querySession.getUser().getName());
//        psCPro.setModifierename(querySession.getUser().getEname());
//        psCPro.setModifieddate(new Date());
//        psCProMapper.updateById(psCPro);
    }

    /**
     * 添加组合商品redis
     *
     * @param o 组合商品id
     */
    public void addRedis(Object o) {
        try {
            PsCProGroup psCProGroup = psCProGroupMapper.selectById(Long.valueOf(String.valueOf(o)));

            QueryWrapper<PsCSku> wrapperSku = new QueryWrapper<PsCSku>();
            wrapperSku.eq("PS_C_PRO_ID", Long.valueOf(String.valueOf(o)));
            List<PsCSku> psCSkus = psCSkuMapper.selectList(wrapperSku);
            /*String cpCStoreIds = psCProGroup.getCpCStoreIds();
            String cpCStoreEcodes = psCProGroup.getCpCStoreEcode();
            String cpCStoreEnames = psCProGroup.getCpCStoreEname();
            String[] splitStoreId = cpCStoreIds.split(",");
            String[] splitStoreEcode = cpCStoreEcodes.split(",");
            String[] splitStoreEname = cpCStoreEnames.split(",");
*/
            for (PsCSku psCSku : psCSkus) {
                //for (int i = 0; i < splitStoreId.length; i++) {
                    List<VirtualSkuRealInfoRequest> virtualSkuRealInfoRequests = new ArrayList<VirtualSkuRealInfoRequest>();
                    VirtualSkuRequest virtualSkuRequest = new VirtualSkuRequest();
                    virtualSkuRequest.setId(psCSku.getId());
                    virtualSkuRequest.setEcode(psCSku.getEcode());
                    virtualSkuRequest.setGroupExtractNum(psCSku.getGroupExtractNum());
                    virtualSkuRequest.setPsCProId(psCProGroup.getId());
                    virtualSkuRequest.setPsCProEcode(psCProGroup.getEcode());
                    virtualSkuRequest.setPsCProEname(psCProGroup.getEname());
                   /* virtualSkuRequest.setCpCStoreId(Long.valueOf(splitStoreId[i]));
                    String storeEcode = i >= splitStoreEcode.length ? "" : splitStoreEcode[i];
                    virtualSkuRequest.setCpCStoreEcode(storeEcode);
                    String storeEname = i >= splitStoreEname.length ? "" : splitStoreEname[i];
                    virtualSkuRequest.setCpCStoreEname(storeEname);*/
                    QueryWrapper<PsCSkugroup> wrapperSkuGroup = new QueryWrapper<PsCSkugroup>();
                    wrapperSkuGroup.eq("PS_C_SKU_GRP_ID", psCSku.getId());
                    List<PsCSkugroup> psCSkugroupList = psCSkugroupMapper.selectList(wrapperSkuGroup);
                    //循环遍历List，获取商品子表，组合商品明细档案表的实体对象
                    for (PsCSkugroup psCSkugroup : psCSkugroupList) {
                        VirtualSkuRealInfoRequest virtualSkuRealInfoRequest = new VirtualSkuRealInfoRequest();
                        virtualSkuRealInfoRequest.setId(psCSkugroup.getPsCSkuId());
                        virtualSkuRealInfoRequest.setECode(psCSkugroup.getPsCSkuEcode());
                        virtualSkuRealInfoRequest.setNum(psCSkugroup.getNum());
                        virtualSkuRealInfoRequest.setGroupNum(psCSkugroup.getGroupnum());
                        virtualSkuRealInfoRequests.add(virtualSkuRealInfoRequest);
                    }

                    virtualSkuRequest.setSku(virtualSkuRealInfoRequests);
                    JSONObject json = JSONObject.parseObject(JSON.toJSONStringWithDateFormat(virtualSkuRequest,
                            "yyyy-MM-dd HH:mm:ss", SerializerFeature.IgnoreNonFieldGetter), Feature.OrderedField);
                    String hashKey = "COMBINED:SKUGROUP:" + psCSku.getId();
                    CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
                    redisTemplate.opsForHash().put(hashKey, "0", json.toString());
               // }
            }
        } catch (NumberFormatException e) {
            log.debug(LogUtil.format("虚拟条码插入redis异常：") + Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * 实际条码新增redis
     *
     * @param o
     */
    public void addRealRedis(Object o) {
        resPushRedisRealSku(Long.valueOf(String.valueOf(o)));
        /*try {
            List<PsCSkugroup> psCSkugroups=psCSkugroupMapper.selectSkuGroupInfoByProId(Long.valueOf(String.valueOf(o)));
            PsCProGroup psCProGroup=psCProGroupMapper.selectById(Long.valueOf(String.valueOf(o)));
            String cpCStoreIds=psCProGroup.getCpCStoreIds();
            String[] splitStoreId= cpCStoreIds.split(",");

            for(int i=0 ;i< splitStoreId.length;i++) {
                for (PsCSkugroup psCSkugroup : psCSkugroups) {
                    SkuRequest skuRequest = new SkuRequest();
                    skuRequest.setId(psCSkugroup.getPsCSkuId());
                    skuRequest.setEcode(psCSkugroup.getPsCSkuEcode());
                    skuRequest.setCpCStoreId(Long.valueOf(splitStoreId[i]));
                    List<SkuVituralInfoRequest> skuVituralInfoRequests=new ArrayList<SkuVituralInfoRequest> ();
                    List<PsCSkugroup> sCSkugroups=psCSkugroupMapper.selectSkuIdByEcode(psCSkugroup.getPsCSkuEcode(),Long.valueOf(String.valueOf(o)));

                    for(PsCSkugroup sCSkugroup:sCSkugroups){
                        PsCSku psSku = psCSkuMapper.selectById(sCSkugroup.getPsCSkuGrpId());
                        SkuVituralInfoRequest skuVituralInfoRequest=new SkuVituralInfoRequest();
                        skuVituralInfoRequest.setId(psSku.getId());
                        skuVituralInfoRequest.setECode(psSku.getEcode());
                        skuVituralInfoRequest.setNum(sCSkugroup.getNum());
                        skuVituralInfoRequest.setGroupNum(sCSkugroup.getGroupnum());
                        skuVituralInfoRequest.setGroupExtractNum(psSku.getGroupExtractNum());
                        skuVituralInfoRequest.setPsCProId(sCSkugroup.getPsCProId());
                        skuVituralInfoRequests.add(skuVituralInfoRequest);
                    }
                    skuRequest.setSkuGroup(skuVituralInfoRequests);
                    pushRealSkuToRedis(psCSkugroup.getPsCSkuId(), splitStoreId[i], skuRequest);
                    JSONObject json=JSONObject.parseObject(JSON.toJSONStringWithDateFormat(skuRequest,
                           "yyyy-MM-dd HH:mm:ss", SerializerFeature.IgnoreNonFieldGetter), Feature.OrderedField);
                    redisTemplate.opsForHash().put("COMBINED:SKU:"+psCSkugroup.getPsCSkuId(),splitStoreId[i],json.toString());
                }
            }
        } catch (Exception e) {
            log.debug(LogUtil.format("实际条码插入redis异常"+e.getMessage());
        }*/
    }

    /**
     * 重推组合福袋redis数据
     *
     * @param psProId
     */
    public void resPushRedisRealSku(Long psProId) {
        List<PsProGroupResult> proGroupResults = psCProGroupMapper.selectProGroupResult(psProId);
        List<PsProGroupResult> proGroupResult = new ArrayList<>();
        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
        Map<Long, String> skuMap = proGroupResults.stream().collect(
                Collectors.toMap(PsProGroupResult::getPsCSkuId, PsProGroupResult::getPsCSkuEcode, (v1, v2) -> v1)
        );
        for (PsProGroupResult result : proGroupResults) {
            String storeIds = result.getCpCStoreIds();
            if (StringUtils.isNotEmpty(storeIds) && storeIds.contains(",")) {
                String[] storeArray = storeIds.split(",");
                for (String s : storeArray) {
                    PsProGroupResult proGroup = new PsProGroupResult();
                    BeanUtils.copyProperties(result, proGroup);
                    proGroup.setCpCStoreId(s);
                    proGroupResult.add(proGroup);
                }
            } else {
                PsProGroupResult proGroup = new PsProGroupResult();
                BeanUtils.copyProperties(result, proGroup);
                proGroup.setCpCStoreId(storeIds);
                proGroupResult.add(proGroup);
            }
        }

        Map<Long, List<PsProGroupResult>> mapBySkuId = proGroupResult.stream().collect(
                Collectors.groupingBy(PsProGroupResult::getPsCSkuId)
        );
        for (Long skuId : mapBySkuId.keySet()) {
            String hashKey = "COMBINED:SKU:" + skuId;
            redisTemplate.delete(hashKey);
            BoundHashOperations<String, String, String> hashOperations = redisTemplate.boundHashOps(hashKey);
            List<PsProGroupResult> groupResults = mapBySkuId.get(skuId);
            SkuRequest skuRequest = new SkuRequest();
            skuRequest.setId(skuId);
            skuRequest.setEcode(skuMap.get(skuId));
            List<SkuVituralInfoRequest> skuGroup = new ArrayList<>();
            for (PsProGroupResult result : groupResults) {
                SkuVituralInfoRequest virtualInfoRequest = new SkuVituralInfoRequest();
                virtualInfoRequest.setNum(result.getNum());
                virtualInfoRequest.setGroupExtractNum(result.getGroupExtractNum());
                virtualInfoRequest.setPsCProId(result.getPsCProId());
                virtualInfoRequest.setECode(result.getPsCSkuGrpEcode());
                virtualInfoRequest.setId(result.getPsCSkuGrpId());
                virtualInfoRequest.setGroupNum(result.getGroupnum());
                skuGroup.add(virtualInfoRequest);
            }
            skuRequest.setSkuGroup(skuGroup);
            JSONObject json = JSONObject.parseObject(JSON.toJSONStringWithDateFormat(skuRequest,
                    "yyyy-MM-dd HH:mm:ss", SerializerFeature.IgnoreNonFieldGetter),
                    Feature.OrderedField);
            hashOperations.put(skuId+"", json.toString());
        }
    }

    /**
     * 调用福袋生成服务
     *
     * @param querySession
     * @param id
     */
    public ValueHolderV14 generateLuckyBag(QuerySession querySession, Object id, ValueHolderV14<SgGoupStorageCalculateResult> result) {
        ValueHolderV14 vh = new ValueHolderV14();
        SgGoupStorageCalculateResult sgGoupStorageCalculateResult = result.getData();
        List<SgGoupStorageGroupSkuResult> sgGoupStorageGroupSkuResults = sgGoupStorageCalculateResult.getGroupSkuResults();
        PsCProGroup psCProGroup = psCProGroupMapper.selectById(Long.valueOf(String.valueOf(id)));
        int groupType = psCProGroup.getGroupType();
        if (groupType == 1) {
            List<LuckyBagGenerateRequest> luckyBagGenerateRequestList = new ArrayList<LuckyBagGenerateRequest>();
            for (SgGoupStorageGroupSkuResult sgGoupStorageGroupSkuResult : sgGoupStorageGroupSkuResults) {
                LuckyBagGenerateRequest luckyBagGenerateRequest = new LuckyBagGenerateRequest();
                luckyBagGenerateRequest.setGoodyBagGroupSku(sgGoupStorageGroupSkuResult.getEcode());
                luckyBagGenerateRequest.setGroupNum(sgGoupStorageGroupSkuResult.getGroupExtractNum());
                luckyBagGenerateRequest.setGroupSkuStorage(sgGoupStorageGroupSkuResult.getMinStorage());
                luckyBagGenerateRequest.setStoreId(sgGoupStorageGroupSkuResult.getCpCStoreId());
                List<SgGoupStorageSkuResult> sgGoupStorageSkuResults = sgGoupStorageGroupSkuResult.getSkuResults();
                List<LuckyBagGenerateRealProRequest> luckyBagGenerateRealProRequests = new ArrayList<LuckyBagGenerateRealProRequest>();
                for (SgGoupStorageSkuResult sgGoupStorageSkuResult : sgGoupStorageSkuResults) {
                    LuckyBagGenerateRealProRequest luckyBagGenerateRealProRequest = new LuckyBagGenerateRealProRequest();
                    PsCSkugroup psCSkugroup = psCSkugroupMapper.selectSkuGroup(sgGoupStorageSkuResult.getId(), sgGoupStorageGroupSkuResult.getId());
                    luckyBagGenerateRealProRequest.setPsCProEcode(psCSkugroup.getPsCProEcode());
                    luckyBagGenerateRealProRequest.setPsCSkuEcode(sgGoupStorageSkuResult.getEcode());
                    luckyBagGenerateRealProRequest.setGroupnum(psCSkugroup.getGroupnum());
                    luckyBagGenerateRealProRequest.setStorage(sgGoupStorageSkuResult.getQtyAvaliable());
                    luckyBagGenerateRealProRequest.setNum(sgGoupStorageSkuResult.getNum());
                    luckyBagGenerateRealProRequests.add(luckyBagGenerateRealProRequest);
                }
                luckyBagGenerateRequest.setLuckyBagGenerateRealProRequestList(luckyBagGenerateRealProRequests);
                luckyBagGenerateRequestList.add(luckyBagGenerateRequest);
            }
            try {
                log.debug(LogUtil.format("生成福袋入参：") + luckyBagGenerateRequestList);
                vh = luckyBagGenerateService.excute(luckyBagGenerateRequestList);
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("luckyBag.result：") + JSON.toJSONString(vh));
                }
                if (vh.getCode() == -1) {
                    vh.setCode(-1);
                    vh.setMessage("福袋生成失败:" + vh.getMessage());
                }
            } catch (Exception e) {
                log.error(LogUtil.format("启用调用福袋生成服务异常：{}"), Throwables.getStackTraceAsString(e));
                vh.setCode(-1);
                vh.setMessage("福袋生成失败，原因：" + e.getMessage());
            }
        }
        return vh;
    }

    /**
     * 计算虚拟条码库存
     *
     * @param querySession
     * @param id
     * @return
     */
    public ValueHolderV14 calculateStorage(QuerySession querySession, Long id) {
        ValueHolderV14 vh = new ValueHolderV14();
        PsCProGroup psCProGroup = psCProGroupMapper.selectById(Long.valueOf(String.valueOf(id)));
        /*String storeIds = psCProGroup.getCpCStoreIds();
        String cpCStoreEcodes = psCProGroup.getCpCStoreEcode();
        String cpCStoreEnames = psCProGroup.getCpCStoreEname();
        String[] splitStoreId = storeIds.split(",");
        String[] splitStoreEcode = cpCStoreEcodes.split(",");
        String[] splitStoreEname = cpCStoreEnames.split(",");*/
        SgGroupStorageCalculateRequest sgGroupStorageCalculateRequest = new SgGroupStorageCalculateRequest();
        List<SgGroupStorageVirtualSkuRequest> sList = new ArrayList<SgGroupStorageVirtualSkuRequest>();
        //for (int i = 0; i < splitStoreId.length; i++) {
            QueryWrapper<PsCSku> wrapperSku = new QueryWrapper<PsCSku>();
            wrapperSku.eq("PS_C_PRO_ID", Long.valueOf(String.valueOf(id)));
            List<PsCSku> psCSkus = psCSkuMapper.selectList(wrapperSku);
            for (PsCSku psCSku : psCSkus) {
                List<SgGroupStorageSkuRequest> sgGroupStorageSkuRequestList = new ArrayList<SgGroupStorageSkuRequest>();
                SgGroupStorageVirtualSkuRequest sgGroupStorageVirtualSkuRequest = new SgGroupStorageVirtualSkuRequest();
                sgGroupStorageVirtualSkuRequest.setId(psCSku.getId());
                sgGroupStorageVirtualSkuRequest.setEcode(psCSku.getEcode());
                sgGroupStorageVirtualSkuRequest.setGroupExtractNum(psCSku.getGroupExtractNum());
                sgGroupStorageVirtualSkuRequest.setPsCProId(psCProGroup.getId());
                sgGroupStorageVirtualSkuRequest.setPsCProEcode(psCProGroup.getEcode());
                sgGroupStorageVirtualSkuRequest.setPsCProEname(psCProGroup.getEname());
                /*sgGroupStorageVirtualSkuRequest.setCpCStoreId(Long.valueOf(splitStoreId[i]));
                String storeEcode = i >= splitStoreEcode.length ? "" : splitStoreEcode[i];
                sgGroupStorageVirtualSkuRequest.setCpCStoreEcode(storeEcode);
                String storeEname = i >= splitStoreEname.length ? "" : splitStoreEname[i];
                sgGroupStorageVirtualSkuRequest.setCpCStoreEname(storeEname);*/
                QueryWrapper<PsCSkugroup> wrapperSkuGroup = new QueryWrapper<PsCSkugroup>();
                wrapperSkuGroup.eq("PS_C_SKU_GRP_ID", psCSku.getId());
                List<PsCSkugroup> psCSkugroups = psCSkugroupMapper.selectList(wrapperSkuGroup);
                //循环遍历List，获取商品子表，组合商品明细档案表的实体对象
                for (PsCSkugroup psCSkugroup : psCSkugroups) {
                    SgGroupStorageSkuRequest sgGroupStorageSkuRequest = new SgGroupStorageSkuRequest();
                    sgGroupStorageSkuRequest.setId(psCSkugroup.getPsCSkuId());
                    sgGroupStorageSkuRequest.setEcode(psCSkugroup.getPsCSkuEcode());
                    sgGroupStorageSkuRequest.setNum(psCSkugroup.getNum());
                    sgGroupStorageSkuRequest.setGroupnum(psCSkugroup.getGroupnum());
                    sgGroupStorageSkuRequestList.add(sgGroupStorageSkuRequest);
                }
                sgGroupStorageVirtualSkuRequest.setSku(sgGroupStorageSkuRequestList);
                sList.add(sgGroupStorageVirtualSkuRequest);
            }
        //}
        sgGroupStorageCalculateRequest.setVirtualSkuList(sList);
        sgGroupStorageCalculateRequest.setLoginUser(querySession.getUser());
        try {
            log.debug(LogUtil.format("计算库存入参：") + sgGroupStorageCalculateRequest);
            ValueHolderV14<SgGoupStorageCalculateResult> result = sgGroupStorageCalculateCmd.calculateGroupStorage(sgGroupStorageCalculateRequest);
            log.debug(LogUtil.format("calculate.skuGroup.result：") + result);
            if (result.getCode() == 0 && CollectionUtils.isNotEmpty(result.getData().getGroupSkuResults())) {
                List<SgGoupStorageGroupSkuResult> groupSkuResults = result.getData().getGroupSkuResults();
                int count = 0;
                int skuResultSize = 0;
                for (SgGoupStorageGroupSkuResult sgGoupStorageGroupSkuResult : groupSkuResults) {
                    List<SgGoupStorageSkuResult> skuResults = sgGoupStorageGroupSkuResult.getSkuResults();
                    skuResultSize = skuResults.size();
                    for (SgGoupStorageSkuResult sgGoupStorageSkuResult : skuResults) {
                        BigDecimal qtyAvaliable = sgGoupStorageSkuResult.getQtyAvaliable();
                        if (qtyAvaliable.compareTo(BigDecimal.ZERO) == 0) {
                            count++;
                        }
                    }
                }
                if ((skuResultSize * groupSkuResults.size()) == count) {
                    vh.setCode(-1);
                    vh.setMessage(Arrays.asList() + "下子商品中全部商品库存为0或全部库存记录都为空");
                } else {
                    vh = generateLuckyBag(querySession, id, result);
                }
            } else if (result.getCode() == 0 && CollectionUtils.isEmpty(result.getData().getGroupSkuResults())) {
                vh.setCode(-1);
                vh.setMessage(Arrays.asList() + "下子商品中全部商品库存为0或全部库存记录都为空");
            } else if (result.getCode() == -1) {
                vh.setCode(-1);
                vh.setMessage(result.getMessage());
            }
        } catch (Exception e) {
            log.error(LogUtil.format("调用计算库存服务失败：") + Throwables.getStackTraceAsString(e));
        }
        return vh;
    }
    /**
     * 计算逻辑仓对应的条码库存
     * @param psCSkugroupList
     * @param ids
     * @return
     */
//    public String calStorage(List<PsCSkugroup> psCSkugroupList,List<Long> ids,QuerySession session){
//        List<String> skuEcodeLists=psCSkugroupList.stream().map(PsCSkugroup::getPsCSkuEcode).collect(toList());
//        //多个虚拟条码下可以存在相同的实际条码  计算库存的时候  传入相同的实际条码只会返回一条库存信息  所以这里为了避免传入的条数与返回的条数一致 做了去重处理
//        List<String> skuEcodeList=skuEcodeLists.stream()
//                .distinct()
//                .collect(Collectors.toList());
//        for(Long id:ids){
//            List<Integer> storeId=new ArrayList<Integer>();
//            storeId.add(id.intValue());
//            List<CpCStore> cpCStores=null;
//            try {
//                cpCStores = rpcCpService.queryStoreInfoByIds(storeId);
//                log.info(LogUtil.format("add getCPCSoreInfo=>"+cpCStores);
//            } catch (Exception e) {
//                log.debug(LogUtil.format("add getCPCSoreInfo error=>"+e.getMessage());
//            }
//            SgStoreStorageQueryRequest sgStoreStorageQueryRequest=new SgStoreStorageQueryRequest();
//            sgStoreStorageQueryRequest.setStoreId(id);
//            ValueHolderV14<List<SgBStorage>> sgBStorageList=new ValueHolderV14<List<SgBStorage>>();
//            List<SgBStorage> sgBStorages=new ArrayList<SgBStorage>();
//            //无库存信息
//            List<String> noStock=new ArrayList<String>();
//            try {
//                int itemNum = skuEcodeList.size();
//                int pageSize = 500;
//                int page = itemNum / pageSize;
//                if (itemNum % pageSize != 0) {
//                    page++;
//                }
//                for (int i = 0; i < page; i++) {
//                    int startIndex = i * pageSize;
//                    int endIndex = (i + 1) * pageSize;
//                    List<String> skuEcodes = skuEcodeList.subList(startIndex, endIndex < itemNum ? endIndex : itemNum);
//                    sgStoreStorageQueryRequest.setSkuEcodes(skuEcodes);
//                    sgBStorageList=sgStorageQueryCmd.queryStoreStorage(sgStoreStorageQueryRequest,session.getUser());
//                    if(sgBStorageList.getData().size()<=0){//如果返回的为空，记录这一批条码在该逻辑仓下面无库存信息
//                        noStock=skuEcodeList.subList(startIndex, endIndex < itemNum ? endIndex : itemNum);
//                    }else {
//                        sgBStorages.addAll(sgBStorageList.getData());
//                    }
//                }
//                //部分无库存
//                List<String> partNoStock=new ArrayList<String>();
//                if(CollectionUtils.isNotEmpty(sgBStorages)){
//                    //有库存信息
//                    List<String> haveStock=sgBStorages.stream().map(SgBStorage::getPsCSkuEcode).collect(toList());
//                    //部分有库存=所有条码-有库存
//                    partNoStock = skuEcodeList.stream().filter(item -> !haveStock.contains(item)).collect(toList());
//                    if(haveStock.size() != skuEcodeList.size()){//说明所传入的都有库存信息
//                        return partNoStock.toString()+"在逻辑仓:【"+cpCStores.get(0).getEname()+"】下无库存信息";
//                    }
//                }else{
//                    return noStock.toString()+"在逻辑仓:【"+cpCStores.get(0).getEname()+"】下无库存信息";
//                }
//            } catch (Exception e) {
//                log.error(LogUtil.format("计算库存异常:" + e.getMessage(), e);
//            }
//        }
//        return null;
//    }
}