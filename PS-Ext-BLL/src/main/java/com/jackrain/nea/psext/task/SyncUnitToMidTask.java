package com.jackrain.nea.psext.task;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.xxl.job.starter.helper.R3XxlJobParamHelper;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.psext.services.PsCUnitToMidFLService;
import com.jackrain.nea.psext.services.PsCUnitToMidService;
import com.jackrain.nea.psext.services.PsCUnitToMidService.SubmitPsCUnitConvert;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.Assert;

/**
 * Description:
 *
 * @Author: guo.kw
 * @Since: 2022/7/4
 * create at: 2022/7/4 18:16
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SyncUnitToMidTask implements IR3Task {

    @Autowired
    private PsCUnitToMidService psCUnitToMidService;

    @Autowired
    private PsCUnitToMidFLService psCUnitToMidFLService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Override
    @XxlJob(value = "SyncUnitToMidTask")
    public RunTaskResult execute(JSONObject params) {
        params = R3XxlJobParamHelper.xxlParam2R3Json();

        log.info(LogUtil.format("SyncUnitToMidTask beginning"));
        RunTaskResult result = new RunTaskResult();
        try {
            transactionTemplate.execute(status -> {
                ValueHolderV14 holderV14;

                ValueHolderV14 flHolderV14;

                SubmitPsCUnitConvert submitPsCUnitConvert = null;
                try {
                    holderV14 = psCUnitToMidService.pushUnitToTask();

                    submitPsCUnitConvert = (SubmitPsCUnitConvert)holderV14.getData();
                }
                catch (Exception e) {
                    log.error("巨沃wms 单位转换推送中间表异常 ===> {}", Throwables.getStackTraceAsString(e)); throw e;
                }

                try {
                    flHolderV14 = psCUnitToMidFLService.pushUnitToTask();
                }
                catch (Exception e) {
                    log.error("富勒wms 单位转换推送中间表异常 ===> {}", Throwables.getStackTraceAsString(e)); throw e;
                }

                if (holderV14.getCode() == ResultCode.FAIL || flHolderV14.getCode() == ResultCode.FAIL) {
                    result.setSuccess(false);
                    result.setMessage("单位转换任务失败");

                    throw new RuntimeException("单位转换任务失败");
                }
                else {
                    result.setSuccess(true);
                }

                Assert.notNull(submitPsCUnitConvert, "提交单位转换表 推送到中间表状态异常");
                submitPsCUnitConvert.submitToMidTableStatus();

                return status;
            });

        }
        catch (Exception e) {
            log.error(LogUtil.format("SyncUnitToMidTask异常={}", "单位转换推送中间表异常"),
                    Throwables.getStackTraceAsString(e));
            result.setSuccess(false);
            result.setMessage(e.getMessage());
        }
        return result;
    }
}
