package com.jackrain.nea.psext.services;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jackrain.nea.cpext.model.Enum.ThirdWmsTypeEnum;
import com.jackrain.nea.ip.api.jingdong.JdServiceCmd;
import com.jackrain.nea.ip.common.JdClpsResultFlagEnum;
import com.jackrain.nea.ip.model.jingdong.request.JdSyncSkuRequest;
import com.jackrain.nea.ip.model.jingdong.response.JdSyncSkuResponse;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.psext.common.PsExtConstantsIF;
import com.jackrain.nea.psext.mapper.PsCSkuPushToWmsMidMapper;
import com.jackrain.nea.psext.mapper.PsCThirdSystemSkuMapper;
import com.jackrain.nea.psext.model.table.PsCSkuPushWmsMidModel;
import com.jackrain.nea.psext.model.table.PsCThirdSystemSku;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/7/27 22:34
 * @Description
 */
@Deprecated
@Component
@Slf4j
public class PsCSkuMidToJdService extends ServiceImpl<PsCSkuPushToWmsMidMapper,PsCSkuPushWmsMidModel> {

    @Autowired
    private PsCSkuPushToWmsMidMapper psCSkuPushToWmsMidMapper;

    @Autowired
    private PsCThirdSystemSkuMapper psCThirdSystemSkuMapper;

    @Reference(version = "1.0", group = "ip")
    private JdServiceCmd jdServiceCmd;

    @NacosValue(value = "${lts.jdSkuMid.taskQuery.limit:100}", autoRefreshed = true)
    private Integer limit;

    public ValueHolderV14 pushSkuToJd(){
        ValueHolderV14<Object> holderV14 = new ValueHolderV14<>();
        List<JdSyncSkuRequest> requestList = psCSkuPushToWmsMidMapper.queryToJdSku(limit);
        log.info(LogUtil.format("PsCSkuMidToJdService.pushSkuToJd.size={};",
                "PsCSkuMidToJdService.pushSkuToJd"), requestList.size());
        if (!CollectionUtils.isEmpty(requestList)) {
            //更新为传中
            List<Long> mainTableIds =
                    requestList.stream().map(JdSyncSkuRequest::getMainTableId).collect(Collectors.toList());
            PsCSkuPushWmsMidModel update = new PsCSkuPushWmsMidModel();
            update.setWmsStatus(PsExtConstantsIF.TO_WMS_STATUS_GOING);
            update.setPassWmsTime(new Date());
            psCSkuPushToWmsMidMapper.update(update, new LambdaUpdateWrapper<PsCSkuPushWmsMidModel>()
                    .in(PsCSkuPushWmsMidModel::getId, mainTableIds));


            List<PsCSkuPushWmsMidModel> successList = new ArrayList<>(); //更新成功集合
            List<PsCSkuPushWmsMidModel> failureList = new ArrayList<>(); //更新失败集合
            List<PsCThirdSystemSku> systemSkuList = new ArrayList<>();  //第三方系统商品信息表
            ValueHolderV14<List<JdSyncSkuResponse>> vh = new ValueHolderV14<>(ResultCode.SUCCESS, "success");
            try {
                vh = jdServiceCmd.syncSku(requestList);
            } catch (Exception e) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("调用IP服务异常!" + e.getMessage());
            }

            if (!CollectionUtils.isEmpty(vh.getData())) {
                Map<Long,JdSyncSkuResponse> failureMap = new HashMap<>();
                for (JdSyncSkuResponse response : vh.getData()) {
                    if (JdClpsResultFlagEnum.SUCCESS.getFlag().equals(response.getFlag())) {
                        //成功
                        PsCSkuPushWmsMidModel midModel = new PsCSkuPushWmsMidModel();
                        midModel.setId(response.getMainTableId());
                        midModel.setWmsStatus(PsExtConstantsIF.TO_WMS_STATUS_SUCCESS);
                        midModel.setFailureReason("");
                        successList.add(midModel);

                        //第三方系统商品信息表
                        PsCThirdSystemSku systemSku = new PsCThirdSystemSku();
//                        systemSku.setId(ModelUtil.getSequence("PS_C_THIRD_SYSTEM_SKU"));
                        systemSku.setSkeCode(response.getSkuCode());
                        /*systemSku.setThirdPartySystemName(PsSkuPushSystemConstants.SYSTEM_TYPE_JDYC);*/
                        systemSku.setThirdPartySystemName(ThirdWmsTypeEnum.JDWMS.getCode());
                        systemSku.setThirdPartySkuCode(response.getClpsGoodsCode());
                        systemSkuList.add(systemSku);
                    }else {
                        //失败
                        failureMap.put(response.getMainTableId(),response);
                    }
                }
                if (!CollectionUtils.isEmpty(failureMap)) {
                    Set<Long> ids = failureMap.keySet();
                    List<PsCSkuPushWmsMidModel> list = psCSkuPushToWmsMidMapper.selectBatchIds(ids);
                    for (PsCSkuPushWmsMidModel model : list) {
                        PsCSkuPushWmsMidModel midModel = new PsCSkuPushWmsMidModel();
                        midModel.setId(model.getId());
                        midModel.setWmsStatus(PsExtConstantsIF.TO_WMS_STATUS_FAIL);
                        midModel.setFailureNum(model.getFailureNum() == null ? 1 : model.getFailureNum() + 1);
                        String message = failureMap.get(model.getId()).getMessage();
                        midModel.setFailureReason(message.length() > 1000 ? message.substring(0,999) : message);
                        failureList.add(midModel);
                    }
                }
            }

            //补偿
            if ((successList.size() + failureList.size()) < mainTableIds.size()) {
                //已收集的ID集合
                List<Long> collectIds = new ArrayList<>();
                if (!CollectionUtils.isEmpty(successList)) {
                    collectIds.addAll(successList.stream().map(PsCSkuPushWmsMidModel::getId).collect(Collectors.toList()));
                }
                if (!CollectionUtils.isEmpty(failureList)) {
                    collectIds.addAll(failureList.stream().map(PsCSkuPushWmsMidModel::getId).collect(Collectors.toList()));
                }

                //未收集ID
                List<Long> noCollectIds = new ArrayList<>();
                if (!CollectionUtils.isEmpty(collectIds)) {
                    mainTableIds.removeAll(collectIds);
                }
                noCollectIds = mainTableIds;
                if (!CollectionUtils.isEmpty(noCollectIds)) {
                    List<PsCSkuPushWmsMidModel> list = psCSkuPushToWmsMidMapper.selectBatchIds(noCollectIds);
                    for (PsCSkuPushWmsMidModel model : list) {
                        PsCSkuPushWmsMidModel wmsMidModel = new PsCSkuPushWmsMidModel();
                        wmsMidModel.setId(model.getId());
                        wmsMidModel.setWmsStatus(PsExtConstantsIF.TO_WMS_STATUS_FAIL);
                        wmsMidModel.setFailureNum(model.getFailureNum() == null ? 1 : model.getFailureNum() + 1);
                        String message = vh.getMessage();
                        wmsMidModel.setFailureReason(message.length() > 1000 ? message.substring(0,999) : message);
                        failureList.add(wmsMidModel);
                    }
                }
            }

            if (!CollectionUtils.isEmpty(successList)) {
                this.updateBatchById(successList);
            }
            if (!CollectionUtils.isEmpty(failureList)) {
                this.updateBatchById(failureList);
            }
            if (!CollectionUtils.isEmpty(systemSkuList)) {
                List<String> list = systemSkuList.stream().map(PsCThirdSystemSku::getSkeCode).collect(Collectors.toList());
                List<PsCThirdSystemSku> selectList = psCThirdSystemSkuMapper.selectList(new LambdaQueryWrapper<PsCThirdSystemSku>()
                        .in(PsCThirdSystemSku::getSkeCode, list));
                Map<String, PsCThirdSystemSku> systemSkuMap = new HashMap<>();
                if (!CollectionUtils.isEmpty(selectList)) {
                    systemSkuMap = selectList.stream().collect(Collectors.toMap(PsCThirdSystemSku::getSkeCode, Function.identity(), (x, y) -> y));
                }
                for (PsCThirdSystemSku systemSku : systemSkuList) {
                    if (!CollectionUtils.isEmpty(systemSkuMap) && systemSkuMap.get(systemSku.getSkeCode()) != null) {
                        continue;
                    }
                    systemSku.setId(ModelUtil.getSequence("PS_C_THIRD_SYSTEM_SKU"));
                    psCThirdSystemSkuMapper.insert(systemSku);
                }
            }
        }
        return holderV14;
    }
}
