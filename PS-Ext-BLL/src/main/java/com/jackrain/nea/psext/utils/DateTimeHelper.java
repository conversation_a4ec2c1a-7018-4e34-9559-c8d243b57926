package com.jackrain.nea.psext.utils;

import com.google.common.base.Throwables;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;

/**
 * @ClassName : DateTimeHelper
 * @Description :
 * <AUTHOR> CD
 * @Date: 2021-08-26 14:23
 */
@Slf4j
public class DateTimeHelper {
    public static final String YYYY_MM_DD = "yyyy-MM-dd";

    public static final String HH_MM_SS = "HH:mm:ss";

    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    public static final String YYYY_MM_DD_HH_MM_SS_SSS = "yyyy-MM-dd HH:mm:ss.SSS";

    public static final String YYYY_MM_DD_HH_MM = "yyyy-MM-dd HH:mm";

    public static final String YYYYMMDD_HH_MM_SS = "yyyy/MM/dd HH:mm:ss";

    public static final String YYYYMM = "yyyyMM";

    public static final String YYMMDD = "yyMMdd";

    public static final String YYYYMMDD = "yyyyMMdd";

    public static final String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public static final String YYYYMMDDHH = "yyyyMMddHH";

    public static LocalDateTime dateToLocalDateTime(Date date) {
        return dateToLocalDateTime(date, ZoneId.systemDefault());
    }

    public static LocalDateTime dateToLocalDateTime(Date date, ZoneId tzId) {
        Instant instant = Instant.ofEpochMilli(date.getTime());
        return LocalDateTime.ofInstant(instant, tzId);
    }

    public static LocalDate dateToLocalDate(Date date) {
        return dateToLocalDate(date, ZoneId.systemDefault());
    }

    public static LocalDate dateToLocalDate(Date date, ZoneId tzId) {
        Instant instant = Instant.ofEpochMilli(date.getTime());
        return LocalDateTime.ofInstant(instant, tzId).toLocalDate();
    }

    public static LocalTime dateToLocalTime(Date date) {
        return dateToLocalTime(date, ZoneId.systemDefault());
    }

    public static LocalTime dateToLocalTime(Date date, ZoneId tzId) {
        Instant instant = Instant.ofEpochMilli(date.getTime());
        return LocalDateTime.ofInstant(instant, tzId).toLocalTime();
    }

    public static Date localDateTimeToDate(LocalDateTime ldt) {
        return localDateTimeToDate(ldt, ZoneId.systemDefault());
    }

    public static Date localDateTimeToDate(LocalDateTime ldt, ZoneId tzId) {
        Instant instant = ldt.atZone(tzId).toInstant();
        return Date.from(instant);
    }

    public static Date localDateToDate(LocalDate ld) {
        return localDateToDate(ld, ZoneId.systemDefault());
    }

    public static Date localDateToDate(LocalDate ld, ZoneId tzId) {
        Instant instant = ld.atStartOfDay().atZone(tzId).toInstant();
        return Date.from(instant);
    }

    public static Date localTimeToDate(LocalTime lt) {
        return localTimeToDate(lt, ZoneId.systemDefault());
    }

    public static Date localTimeToDate(LocalTime lt, ZoneId tzId) {
        Instant instant = lt.atDate(LocalDate.now()).atZone(tzId).toInstant();
        return Date.from(instant);
    }

    public static Date localTimeToDate(LocalTime lt, int year, int month, int day) {
        return localTimeToDate(lt, ZoneId.systemDefault(), year, month, day);
    }

    public static Date localTimeToDate(LocalTime lt, ZoneId tzId, int year, int month, int day) {
        Instant instant = lt.atDate(LocalDate.of(year, month, day)).atZone(tzId).toInstant();
        return Date.from(instant);
    }

    public static String dateToString(Date date, String formatter) {
        DateTimeFormatter f = DateTimeFormatter.ofPattern(formatter);
        LocalDateTime dateTime = dateToLocalDateTime(date);
        return dateTime.format(f);
    }

    public static String localDateToString(LocalDateTime dateTime, String formatter) {
        DateTimeFormatter f = DateTimeFormatter.ofPattern(formatter);
        return dateTime.format(f);
    }

    public static Date stringToDate(String date, String pattern) {
        LocalDateTime parse = LocalDateTime.parse(date, DateTimeFormatter.ofPattern(pattern));
        return localDateTimeToDate(parse);
    }

    public static Date stringToDateLong(String date) {
        if (StringUtils.isBlank(date)) {
            return null;
        }
        LocalDateTime parse = LocalDateTime.parse(date, DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS));
        return localDateTimeToDate(parse);
    }

    public static Date stringToDateShort(String date) {
        if (date == null || (date = date.trim()).isEmpty()) {
            return null;
        }
        LocalDate parse = LocalDate.parse(date, DateTimeFormatter.ofPattern(YYYY_MM_DD));
        return localDateToDate(parse);
    }

    public static Date stringToDateShort(String date, String pattern) {
        LocalDate parse = LocalDate.parse(date, DateTimeFormatter.ofPattern(pattern));
        return localDateToDate(parse);
    }

    public static LocalDateTime stringToLocalDateTime(String date, String pattern) {
        return LocalDateTime.parse(date, DateTimeFormatter.ofPattern(pattern));
    }

    public static LocalDate stringToLocalDate(String date, String pattern) {
        LocalDate parse = LocalDate.parse(date, DateTimeFormatter.ofPattern(pattern));
        return parse;
    }

    public static Date plusDays(Date date, int day) {
        if (date == null) {
            return null;
        }
        LocalDateTime dateTime = dateToLocalDateTime(date);
        dateTime = dateTime.plusDays(day);
        return localDateTimeToDate(dateTime);
    }

    public static Date clearToDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    public static Date getCurrentTime() {
        return new Date();
    }

    /**
     * 计算N个月前的时间
     */
    public static Date minusMonths(Date date, int months) {
        if (date == null) {
            return null;
        } else {
            LocalDateTime dateTime = dateToLocalDateTime(date);
            dateTime = dateTime.minusMonths(months);
            return localDateTimeToDate(dateTime);
        }
    }

    /**
     * 给时间加上几个小时
     *
     * @param date
     * @param hour 需要加的时间
     * @return
     */
    public static Date addDateMinut(Date date, int hour) {
        if (date == null) {
            return null;
        } else {
            LocalDateTime dateTime = dateToLocalDateTime(date);
            dateTime = dateTime.plusHours(hour);
            return localDateTimeToDate(dateTime);
        }
    }

    /**
     * 给时间减少天数
     *
     * @param date
     * @param day  需要减的时间
     * @return
     */
    public static Date subDateDay(Date date, int day) {
        if (date == null) {
            return null;
        } else {
            LocalDateTime dateTime = dateToLocalDateTime(date);
            dateTime = dateTime.minusDays(day);
            return localDateTimeToDate(dateTime);
        }
    }

    /**
     * 验证字符串是否为指定格式
     *
     * @param dateStr   日期字符串
     * @param formatter 格式
     * @return
     */
    public static boolean isValidDate(String dateStr, String formatter) {
        boolean convertSuccess = true;
        // 指定日期格式为四位年/两位月份/两位日期，注意yyyy/MM/dd区分大小写；
        SimpleDateFormat format = new SimpleDateFormat(formatter);
        try {
            // 设置lenient为false.
            // 否则SimpleDateFormat会比较宽松地验证日期，比如2007/02/29会被接受，并转换成2007/03/01
            format.setLenient(false);
            format.parse(dateStr);
        } catch (ParseException e) {
            log.error(LogUtil.format("DateTimeHelper.isValidDate错误,错误信息：{}"),
                    Throwables.getStackTraceAsString(e));
            // 如果throw java.text.ParseException或者NullPointerException，就说明格式不对
            convertSuccess = false;
        }
        return convertSuccess;
    }

    /**
     * 计算当前日期距离入参时间
     *
     * @param date
     * @return
     */
    public static long until(Date date) {
        return dateToLocalDate(date).until(LocalDate.now(), ChronoUnit.DAYS);
    }

    public static Date getTime(Date date, Integer delayTime, String timeUnit) {
        switch (timeUnit) {
            case "SECONDS":
            case "seconds":
                return DateUtils.addSeconds(date, delayTime);
            case "MINUTES":
            case "minutes":
                return DateUtils.addMinutes(date, delayTime);
            case "HOURS":
            case "hours":
                return DateUtils.addHours(date, delayTime);
            case "DAYS":
            case "days":
                return DateUtils.addDays(date, delayTime);
            default:
                throw new RuntimeException("MnoPayStateJobService--getTime 时间单位不符合预期");
        }
    }

    public static String getTimeInterval(String startTimestamp, String endTimestamp) {
        if (StringUtils.isBlank(startTimestamp) && StringUtils.isBlank(endTimestamp)) {
            return "";
        } else if (StringUtils.isNotBlank(startTimestamp) && StringUtils.isBlank(endTimestamp)) {
            int length = startTimestamp.length() > 10 ? 10 : startTimestamp.length();
            return startTimestamp.substring(0, length);
        } else if (StringUtils.isBlank(startTimestamp) && StringUtils.isNotBlank(endTimestamp)) {
            int length = endTimestamp.length() > 10 ? 10 : endTimestamp.length();
            return endTimestamp.substring(0, length);
        } else {
            int startLeng = startTimestamp.length() > 10 ? 10 : startTimestamp.length();
            int endLength = startTimestamp.length() > 10 ? 10 : endTimestamp.length();
            return startTimestamp.substring(0, startLeng) + " - " + endTimestamp.substring(0, endLength);
        }
    }

    public static Date secondToDate(long second) {
        Calendar calendar = Calendar.getInstance();
        //转换为毫秒
        calendar.setTimeInMillis(Long.valueOf(second) * 1000);
        Date date = calendar.getTime();

        return date;
    }

    public static long StringToSecond(String time, String pattern) {

        SimpleDateFormat format = new SimpleDateFormat(pattern);
        try {
            Date date = format.parse(time);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            return calendar.getTimeInMillis() / 1000;
        } catch (ParseException e) {
            log.error(LogUtil.format("DateTimeHelper.StringToSecond：time:{},pattern:{},errMsg:{}"), time, pattern,
                    Throwables.getStackTraceAsString(e));
        }
        return 0;
    }

    public static String secondToString(long second, String pattern) {
        Date date = secondToDate(second);
        SimpleDateFormat format = new SimpleDateFormat(pattern);
        return format.format(date);
    }

    /**
     * 给时间减去几天
     *
     * @param date
     * @param days 需要加的时间
     * @return
     */
    public static Date minusDays(Date date, int days) {
        if (date == null) {
            return null;
        } else {
            LocalDateTime dateTime = dateToLocalDateTime(date);
            dateTime = dateTime.minusDays(days);
            return localDateTimeToDate(dateTime);
        }
    }

    /**
     * 给时间加几秒
     *
     * @param date
     * @param seconds 需要加的秒
     * @return
     */
    public static String plusSecondsToString(String date, int seconds) {
        if (StringUtils.isBlank(date)) {
            return null;
        } else {
            LocalDateTime dateTime = LocalDateTime.parse(date, DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS));
            dateTime = dateTime.plusSeconds(seconds);
            Date d = localDateTimeToDate(dateTime);
            return dateToString(d, YYYY_MM_DD_HH_MM_SS);
        }
    }

    /**
     * 判断开始时间和结束时间相差结果
     *
     * @param startTimeStr
     * @param endTimeStr
     * @return 1：startTimeStr大于endTimeStr；0：startTimeStr等于endTimeStr；-1：startTimeStr小于endTimeStr
     */
    public static long judgeTimeResult(String startTimeStr, String endTimeStr) {
        Date startTime = DateTimeHelper.stringToDateLong(startTimeStr);
        Date endTime = DateTimeHelper.stringToDateLong(endTimeStr);
        if (startTime.getTime() > endTime.getTime()) {
            return 1;
        } else if (startTime.getTime() < endTime.getTime()) {
            return -1;
        } else {
            return 0;
        }
    }

    public static String getTransTime(String timeStr) {
        if (StringUtils.isBlank(timeStr)) {
            return null;
        }
        DateTimeFormatter inputFormat = DateTimeFormatter.ofPattern("yyyy/M/d H:m:s");
        DateTimeFormatter outputFormat = DateTimeFormatter.ofPattern(DateTimeHelper.YYYY_MM_DD_HH_MM_SS);
        return LocalDateTime.parse(timeStr, inputFormat).format(outputFormat);
    }
}
