package com.jackrain.nea.psext.services;

import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.hub.api.dewu.DewuStockSyncCmd;
import com.jackrain.nea.hub.request.dewu.DewuStockBatchSyncRequest;
import com.jackrain.nea.psext.enums.DewuStockSyncStatusEnum;
import com.jackrain.nea.psext.mapper.IpCDewuProductMapper;
import com.jackrain.nea.psext.model.table.IpCDewuProduct;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @ClassName DewuStockSyncService
 * @Description 得物库存同步
 * <AUTHOR>
 * @Date 2025/2/6 11:32
 * @Version 1.0
 */
@Component
@Slf4j
public class DewuStockSyncService {

    @Reference(group = "hub", version = "1.0")
    private DewuStockSyncCmd dewuStockSyncCmd;
    @Autowired
    private IpCDewuProductMapper ipCDewuProductMapper;

    @Async
    public ValueHolderV14 syncStock(IpCDewuProduct ipCDewuProduct) {
        List<DewuStockBatchSyncRequest> dewuStockBatchSyncRequestList = new ArrayList<>();
        DewuStockBatchSyncRequest dewuStockBatchSyncRequest = new DewuStockBatchSyncRequest();
        dewuStockBatchSyncRequest.setSkuId(ipCDewuProduct.getSkuId());
        dewuStockBatchSyncRequest.setMerchantWarehouseId(ipCDewuProduct.getDewuWarehouseCode());
        dewuStockBatchSyncRequest.setUsableQty(ipCDewuProduct.getQty());
        dewuStockBatchSyncRequestList.add(dewuStockBatchSyncRequest);
        ValueHolderV14 valueHolderV14 = new ValueHolderV14<>();
        IpCDewuProduct update = new IpCDewuProduct();
        update.setSyncTime(new Date());
        try {
            update.setId(ipCDewuProduct.getId());
            update.setSyncStatus(DewuStockSyncStatusEnum.SYNC_PROCESSING.getCode());
            update.setModifieddate(new Date());
            ipCDewuProductMapper.updateById(update);
            valueHolderV14 = dewuStockSyncCmd.stockSync(dewuStockBatchSyncRequestList);
            if (valueHolderV14.isOK()){
                update.setSyncStatus(DewuStockSyncStatusEnum.SYNC_SUCCESS.getCode());
                update.setModifieddate(new Date());
                ipCDewuProductMapper.updateById(update);
                return valueHolderV14;
            }
        }catch (Exception e){
            log.error("##DewuStockSyncService##syncStock##Error ", e);
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage(e.getMessage());
        }
        update.setSyncStatus(DewuStockSyncStatusEnum.SYNC_FAIL.getCode());
        update.setSyncMessage(valueHolderV14.getMessage());
        update.setModifieddate(new Date());
        ipCDewuProductMapper.updateById(update);
        return valueHolderV14;
    }
}
