package com.jackrain.nea.psext.task;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.xxl.job.starter.helper.R3XxlJobParamHelper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.psext.services.PushSkuToDemogicService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 将传送达摩中间表的条码信息同步至达摩 接口
 */
@Slf4j
@Component
public class PushSkuToDemogicInterfaceTask implements IR3Task {

    @Autowired
    PushSkuToDemogicService pushSkuToDemogicService;

    @Override
    @XxlJob(value = "PushSkuToDemogicInterfaceTask")
    public RunTaskResult execute(JSONObject params) {
        params = R3XxlJobParamHelper.xxlParam2R3Json();

        log.info(LogUtil.format("=============PushSkuToDemogicInterfaceTask beginning====================="));
        RunTaskResult result = new RunTaskResult();
        ValueHolderV14 holderV14 = pushSkuToDemogicService.PushSkuToDemogicInterface(params);
        if (holderV14.getCode() == ResultCode.FAIL) {
            log.error(LogUtil.format("PushSkuToDemogicInterfaceTask.result：") + holderV14.getMessage());
            result.setSuccess(false);
            result.setMessage(holderV14.getMessage());
        } else {
            result.setSuccess(true);
        }
        return result;
    }
}
