package com.jackrain.nea.psext.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.psext.enums.SkuGroupStatusEnum;
import com.jackrain.nea.psext.mapper.PsCProMapper;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * create by 2019-03-04
 */
@Component
@Slf4j
public class SkuGroupSubmitService {

    @Autowired
    private PsCProMapper psCProMapper;


    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder vh = new ValueHolder();
        try {
            DefaultWebEvent event = querySession.getEvent();
            //获取参数
            JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
            Timestamp timestamp = new Timestamp(System.currentTimeMillis());
            if (param != null) {
                Long id = param.getLong("objid");
                //查看当前记录是否存在
                PsCPro psCPro = psCProMapper.selectById(id);
                if (psCPro == null) {
                    throw new NDSException("当前记录不存在!");
                }
                //修改人 修改时间
                Long status = psCPro.getStatus();
                if (Long.valueOf(SkuGroupStatusEnum.SUBMITTED.getCode()).equals(status)) {
                    throw new NDSException("当前记录已是提交状态!");
                }
                if ("N".equals(psCPro.getIsactive())) {
                    throw new NDSException("当前记录已作废!不允许提交!");
                }
                //修改人 修改时间
                psCPro.setId(id);
                psCPro.setModifierid(Integer.valueOf(querySession.getUser().getId()).longValue());
                psCPro.setModifieddate(timestamp);
                psCPro.setModifiername(String.valueOf(querySession.getUser().getName()));
                psCPro.setModifierename(String.valueOf(querySession.getUser().getEname()));
                //设置状态为，已提交
                psCPro.setStatus(Long.valueOf(SkuGroupStatusEnum.SUBMITTED.getCode()));
                psCProMapper.updateById(psCPro);
                vh.put("code", 0);
                vh.put("message", "提交成功!");
            }
        } catch (Exception e) {
            log.error(LogUtil.format("组合商品提交异常：{}"), Throwables.getStackTraceAsString(e));
            throw new NDSException(e.getMessage());
        }
        return vh;
    }

}
