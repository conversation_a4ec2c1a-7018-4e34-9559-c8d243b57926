package com.jackrain.nea.psext.mapper;

import com.jackrain.nea.psext.model.table.PsCSkugroup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.HashMap;
import java.util.List;

/**
 * @author: 郑立轩
 * @since: 2019/4/1
 * create at : 2019/4/1 20:55
 */
@Mapper
public interface CproSkuGroupMapper {
    @Select("select ps_c_sku_ecode from ps_c_skugroup where ps_c_sku_id =#{skuId}")
    List<Long> getSkuEcodeById(@Param("skuId") Long skuId);

    @Select("select * from ps_c_skugroup where ps_c_sku_grp_ecode =#{ecode}")
    List<HashMap<String, Object>> getSkuEcodeByGrpEcode(@Param("ecode") String ecode);

    @SelectProvider(type = SkuGroup.class, method = "select")
    List<PsCSkugroup> findGroupBySkuId(String join2);

    @Select("select * from ps_c_skugroup where ps_c_sku_grp_id=#{skusId}")
    List<PsCSkugroup> selectGroupBySkuId(@Param("skusId") Long skusId);


    @Select("select * from ps_c_skugroup where ps_c_pro_id=#{psCProId} and `ISACTIVE` = 'Y'")
    List<PsCSkugroup> selectGroupByProId(@Param("psCProId") Long psCProId);

    /**
     * ps_c_sku_grp_ecode 为组合商品条码编码
     *
     * @param ecode
     * @return
     */
    @Select("select * from ps_c_skugroup where ps_c_sku_grp_ecode=#{ecode}")
    List<PsCSkugroup> selectGroupBySkuEcode(@Param("ecode") String ecode);

    class SkuGroup {
        public String select(String join2) {
            return "SELECT id ,title,ps_c_sku_grp_ecode FROM ps_c_skugroup WHERE ps_c_sku_id IN ( " + join2 + " ) ";
        }


    }

}
