//package com.jackrain.nea.psext.converter;
//
//import com.aliyun.dts.subscribe.clients.record.RowImage;
//import com.jackrain.nea.exception.NDSRuntimeException;
//import com.jackrain.nea.sys.domain.BaseModel;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//
///**
// * @ClassName : BeanProcessorFactory
// * @Description :
// * <AUTHOR> CD
// * @Date: 2021-07-26 13:42
// */
//@Component
//@Slf4j
//public class BeanProcessorFactory {
//
//    private Map<String, List<R3Processor>> ConverterMap;
//
//    private List<R3Processor> converterList;
//    @Autowired
//    public BeanProcessorFactory(List<R3Processor> converterList) {
//        converterList = converterList;
//        ConverterMap =  converterList.stream().collect(Collectors.groupingBy(R3Processor::getTableName));
//    }
//
//    /**
//     * 匹配表获取转换器
//     * @param tableName
//     * @return R3Converter
//     */
//    public R3Processor<RowImage, BaseModel> match(String tableName){
//        log.debug(" match  tableName info:{}",tableName);
//        if(StringUtils.isEmpty(tableName)){
//            throw new NDSRuntimeException(" tableName not put in the parameter.");
//        }
//        List<R3Processor> converterList = ConverterMap.get(tableName);
//        if (CollectionUtils.isNotEmpty(converterList)) {
//            if(converterList.size() == 1){
//                return converterList.get(0);
//            }else if(converterList.size()>0){
//                log.warn(" have duplicate converter for table {} ,while get first.",tableName);
//                return converterList.get(0);
//            }
//        }
//        log.warn(" not match tableName {} processor , ignore the table", tableName);
//        return null;
//
//    }
//
//}
