package com.jackrain.nea.psext.services;

import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.model.qimen.QimenSingleitemSynchronizeModel;
import com.jackrain.nea.ip.model.result.QimenlResult;
import com.jackrain.nea.psext.common.PsExtConstantsIF;
import com.jackrain.nea.psext.common.PsSkuPushSystemConstants;
import com.jackrain.nea.psext.mapper.PsCBrandMapper;
import com.jackrain.nea.psext.mapper.PsCProWmsMapper;
import com.jackrain.nea.psext.mapper.PsCProdimItemMapper;
import com.jackrain.nea.psext.mapper.PsCSkuPushToWmsMidMapper;
import com.jackrain.nea.psext.mapper.PsCSkuWmsMapper;
import com.jackrain.nea.psext.model.table.PsCBrand;
import com.jackrain.nea.psext.model.table.PsCProWms;
import com.jackrain.nea.psext.model.table.PsCProdimItem;
import com.jackrain.nea.psext.model.table.PsCSkuPushWmsMidModel;
import com.jackrain.nea.psext.model.table.PsCSkuWms;
import com.jackrain.nea.psext.rpc.RpcIpService;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/6/12
 */
@Component
@Slf4j
public class PsCSkuMidToFLService {

    @Autowired
    private PsCSkuWmsMapper psCSkuWmsMapper;

    @Autowired
    private PsCProWmsMapper psCProWmsMapper;

    @Autowired
    private PsCBrandMapper psCBrandMapper;

    @Autowired
    private PsCSkuPushToWmsMidMapper psCSkuPushToWmsMidMapper;

    @Autowired
    private PsCProdimItemMapper psCProdimItemMapper;

    @Autowired
    private RpcIpService rpcIpService;

    @Value("${lts.SyncSkuToJdWmsTask.range:100}")
    private Integer range;

    public ValueHolderV14<String> pushItemsToFLWms() {
        ValueHolderV14<String> result = new ValueHolderV14<>(ResultCode.SUCCESS, "success");
        log.info(LogUtil.format("PsCSkuMidToFLService.pushItemsToFLWms beginning"));
        try {
            // 查询推送中间表数据
            List<PsCSkuPushWmsMidModel> pushWmsList = psCSkuPushToWmsMidMapper.queryPushFLWmsList(range);
            if (CollectionUtils.isEmpty(pushWmsList)) {
                result.setMessage("未查询到数据");
                return result;
            }
            // 查询构建请求体
            List<QimenSingleitemSynchronizeModel> synchronizeModelList = queryBuildRequest(pushWmsList);
            List<List<QimenSingleitemSynchronizeModel>> partition = Lists.partition(synchronizeModelList, 20);
            for (List<QimenSingleitemSynchronizeModel> listObject : partition) {
                // 请求接口中心
                ValueHolderV14<List<QimenlResult>> v14 = rpcIpService.synchronizeQimenSingleItem(listObject, SystemUserResource.getRootUser());
                // 更新返回状态
                updateStatus(pushWmsList, v14);
            }
        }
        catch (Exception e) {
            log.error(LogUtil.format("PsCSkuSaveWms.error pushToWms message:{}",
                    "PsCSkuSaveWms.error pushToWms message"), Throwables.getStackTraceAsString(e));
            throw new NDSException(Throwables.getStackTraceAsString(e));
        }
        return result;
    }

    public List<QimenSingleitemSynchronizeModel> queryBuildRequest(List<PsCSkuPushWmsMidModel> pushWmsList) {
        try {
            // sku ids
            List<Long> skuIdList = pushWmsList.stream().map(PsCSkuPushWmsMidModel::getPsCSkuId).filter(Objects::nonNull).collect(Collectors.toList());
            // sku信息
            List<PsCSkuWms> psCSkuList = psCSkuWmsMapper.selectBatchIds(skuIdList);
            Map<Long, PsCSkuWms> psCSkuMap = psCSkuList.stream().collect(Collectors.toMap(PsCSkuWms::getId, Function.identity(), (k1, k2) -> k1));
            // 商品款号信息
            List<Long> proIdList = psCSkuList.stream().map(PsCSkuWms::getPsCProId).filter(Objects::nonNull).collect(Collectors.toList());

            List<PsCProWms> psCProList = psCProWmsMapper.selectBatchIds(proIdList);
            Map<Long, PsCProWms> psCProMap = psCProList.stream().collect(Collectors.toMap(PsCProWms::getId, Function.identity(), (k1, k2) -> k1));

            //加上物料类型 20220912
            List<Long> psMD1imIdList = psCProList.stream().map(PsCProWms::getMDim1Id).filter(Objects::nonNull).collect(Collectors.toList());
            // 标签值
            List<Long> psMD2imIdList = psCProList.stream().map(PsCProWms::getMDim2Id).filter(Objects::nonNull).collect(Collectors.toList());
            List<Long> psMD8imIdList = psCProList.stream().map(PsCProWms::getMDim8Id).filter(Objects::nonNull).collect(Collectors.toList());
            List<Long> psMD3imIdList = psCProList.stream().map(PsCProWms::getMDim3Id).filter(Objects::nonNull).collect(Collectors.toList());
            List<Long> psMD4imIdList = psCProList.stream().map(PsCProWms::getMDim4Id).filter(Objects::nonNull).collect(Collectors.toList());
            List<Long> psMD5imIdList = psCProList.stream().map(PsCProWms::getMDim5Id).filter(Objects::nonNull).collect(Collectors.toList());
            List<Long> psMD6imIdList = psCProList.stream().map(PsCProWms::getMDim6Id).filter(Objects::nonNull).collect(Collectors.toList());
            List<Long> psMD11imIdList = psCProList.stream().map(PsCProWms::getMDim11Id).filter(Objects::nonNull).collect(Collectors.toList());
            psMD3imIdList.addAll(psMD1imIdList);
            psMD3imIdList.addAll(psMD2imIdList);
            psMD3imIdList.addAll(psMD8imIdList);
            psMD3imIdList.addAll(psMD4imIdList);
            psMD3imIdList.addAll(psMD5imIdList);
            psMD3imIdList.addAll(psMD6imIdList);
            psMD3imIdList.addAll(psMD11imIdList);
            List<PsCProdimItem> psCProdimItems = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(psMD3imIdList)) {
                psCProdimItems = psCProdimItemMapper.selectBatchIds(psMD3imIdList);
            }
            Map<Long, PsCProdimItem> psCProdimItemMap = psCProdimItems.stream()
                    .collect(Collectors.toMap(PsCProdimItem::getId, Function.identity(), (k1, k2) -> k1));

            // 品牌信息
            List<Long> psCBrandIdList = psCProList.stream()
                    .map(PsCProWms::getPsCBrandId).filter(Objects::nonNull).collect(Collectors.toList());

            List<PsCBrand> psCBrands = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(psCBrandIdList)) {
                psCBrands = psCBrandMapper.selectBatchIds(psCBrandIdList);
            }
            Map<Long, PsCBrand> psCBrandMap = psCBrands.stream()
                    .collect(Collectors.toMap(PsCBrand::getId, Function.identity(), (k1, k2) -> k1));

            // 构建请求体
            return buildSyncRequest(pushWmsList, psCSkuMap, psCProMap, psCBrandMap, psCProdimItemMap);
        } catch (Exception e) {
            log.error(LogUtil.format("PsCSkuSaveWmsService.error queryBuildRequest message:{}",
                    "PsCSkuSaveWmsService.error queryBuildRequest message"), Throwables.getStackTraceAsString(e));
            throw new NDSException(Throwables.getStackTraceAsString(e));
        }
    }

    public List<QimenSingleitemSynchronizeModel> buildSyncRequest(List<PsCSkuPushWmsMidModel> psCSkuThirdItemList,
                                                                  Map<Long, PsCSkuWms> psCSkuMap,
                                                                  Map<Long, PsCProWms> psCProMap,
                                                                  Map<Long, PsCBrand> psCBrandMap,
                                                                  Map<Long, PsCProdimItem> psCProdimItemMap) {
        List<QimenSingleitemSynchronizeModel> synchronizeModelList = Lists.newArrayList();
        for (PsCSkuPushWmsMidModel psCSkuPushWmsMid : psCSkuThirdItemList) {
            QimenSingleitemSynchronizeModel model = new QimenSingleitemSynchronizeModel();
            QimenSingleitemSynchronizeModel.Item item = new QimenSingleitemSynchronizeModel.Item();
            model.setItem(item);

            PsCSkuWms psCSku = psCSkuMap.get(psCSkuPushWmsMid.getPsCSkuId());

            PsCProWms psCPro = psCProMap.get(psCSku.getPsCProId());
            if (Objects.isNull(psCPro)) {
                continue;
            }
            PsCBrand psCBrand = psCBrandMap.get(psCPro.getPsCBrandId());
            // 操作类型（两种类型：add/update）
            model.setActionType(psCSkuPushWmsMid.getOperationType());
            // 仓库编码
            model.setWarehouseCode(PsSkuPushSystemConstants.WMS_WAREHOUSE_CODE);
            // 货主编码
            model.setOwnerCode(PsSkuPushSystemConstants.OWNER_CODE);
            // 奇门customerId
            model.setCustomerId(psCSkuPushWmsMid.getWmsAccount());
            // 商品编码
            item.setItemCode(psCSku.getEcode());
            // 仓储系统商品编码
            item.setItemId(psCSkuPushWmsMid.getItemId());
            // 商品名称
            item.setItemName(psCSku.getPsCProEname());
            //商品简称
            item.setShortName(psCPro.getSimpleEname());
            //条形码
            item.setBarCode(psCSku.getGbcode());
            //基本计量单位
            PsCProdimItem psCProdimItem = psCProdimItemMap.get(psCPro.getMDim3Id());
            if (Objects.nonNull(psCProdimItem)) {
                item.setStockUnit(psCProdimItem.getEname());
            }
            // 长
            item.setLength(Objects.nonNull(psCSku.getLength()) ?
                    psCSku.getLength().stripTrailingZeros().toPlainString() : null);
            // 宽
            item.setWidth(Objects.nonNull(psCSku.getWidth()) ?
                    psCSku.getWidth().stripTrailingZeros().toPlainString() : null);
            // 高
            item.setHeight(Objects.nonNull(psCSku.getHeight()) ?
                    psCSku.getHeight().stripTrailingZeros().toPlainString() : null);
            //体积
            item.setVolume(Objects.nonNull(psCSku.getBulk()) ?
                    psCSku.getBulk().stripTrailingZeros().toPlainString() : null);
            // 毛重
            item.setGrossWeight(Objects.nonNull(psCSku.getWeight()) ?
                    psCSku.getWeight().stripTrailingZeros().toPlainString() : null);
            // 净重
            item.setNetWeight(Objects.nonNull(psCSku.getNetWeight()) ?
                    psCSku.getNetWeight().stripTrailingZeros().toPlainString() : null);
            PsCProdimItem psCProdim2Item = psCProdimItemMap.get(psCPro.getMDim2Id());
            if (Objects.nonNull(psCProdim2Item)) {
                // 商品类别ID
                item.setCategoryId(psCProdim2Item.getEcode());
                // 商品类别名称
                item.setCategoryName(psCProdim2Item.getEname());
            }
            // 商品类型
            PsCProdimItem psCProdimItem1 = psCProdimItemMap.get(psCPro.getMDim1Id());
            // todo 物料类型为包装物 是 赋值为包装
            if (Objects.nonNull(psCProdimItem1) && PsSkuPushSystemConstants.PRODIM_ZERP.equals(psCProdimItem1.getEcode())) {
                // todo 未赋值
                item.setItemType("HC");
            } else {
                item.setItemType("ZC");
            }


            if (Objects.nonNull(psCBrand)) {
                // 品牌代码
                item.setBrandCode(psCBrand.getEcode());
                // 品牌名称
                item.setBrandName(psCBrand.getEname());
            }
            // 保质期
            item.setShelfLife(psCSku.getShelfLife() == null ? 0L : psCSku.getShelfLife());

            item.setIsValid(StringUtils.isBlank(psCSku.getIsactive()) ? "N" : psCSku.getIsactive());
            //是否需要批次管理
            if ("Y".equals(psCPro.getIsEnableExpiry()) && "N".equals(psCPro.getIsSerialNumber())) {
                item.setIsBatchMgmt("Y");
            } else if ("Y".equals(psCPro.getIsEnableExpiry()) && "Y".equals(psCPro.getIsSerialNumber())) {
                item.setIsBatchMgmt("N");
            } else {
                item.setIsBatchMgmt(null);
            }

            Map extendProps = new HashMap();
            //extendProps.put("csBarcodes", psCSkuThirdItem.getEan14());
            //是否采集sn
            extendProps.put("snFlag", psCPro.getIsSerialNumber());
            //是否溯源
            extendProps.put("traceabilityFalg", psCPro.getIsTraceSource());
            PsCProdimItem psCProdim8Item = psCProdimItemMap.get(psCPro.getMDim8Id());

            if (Objects.isNull(psCProdim8Item)) {
                extendProps.put("traceabilityRatio", "");
            } else {
                //溯源比例
                extendProps.put("traceabilityRatio", psCProdim8Item.getEname().split("\\.")[0]);
            }
            // 规格型号（扩展字段）SPEC_MODEL
            extendProps.put("spec", psCPro.getSpecModel());
            //新加过量交货容量
            extendProps.put("UEBTO", psCPro.getDeliveryCapacity());
            //物料类型
            if (psCPro.getMDim1Id() != null) {
                PsCProdimItem prodim1Item = psCProdimItemMap.get(psCPro.getMDim1Id());
                if (prodim1Item != null) {
                    extendProps.put("materialType", prodim1Item.getEcode());
                }
            }
            //一级分类
            if (psCPro.getMDim4Id() != null) {
                PsCProdimItem prodim4Item = psCProdimItemMap.get(psCPro.getMDim4Id());
                if (prodim4Item != null) {
                    extendProps.put("M_DIM4_ID", prodim4Item.getEname());
                }
            }
            //二级分类
            if (psCPro.getMDim5Id() != null) {
                PsCProdimItem prodim5Item = psCProdimItemMap.get(psCPro.getMDim5Id());
                if (prodim5Item != null) {
                    extendProps.put("M_DIM5_ID", prodim5Item.getEname());
                }
            }
            //四级分类
            if (psCPro.getMDim6Id() != null) {
                PsCProdimItem prodim6Item = psCProdimItemMap.get(psCPro.getMDim6Id());
                if (prodim6Item != null) {
                    extendProps.put("M_DIM6_ID", prodim6Item.getEname());
                }
            }
            //三级分类
            if (psCPro.getMDim11Id() != null) {
                PsCProdimItem prodim6Item = psCProdimItemMap.get(psCPro.getMDim11Id());
                if (prodim6Item != null) {
                    extendProps.put("M_DIM11_ID", prodim6Item.getEname());
                }
            }
            item.setExtendProps(extendProps);
            synchronizeModelList.add(model);
        }
        return synchronizeModelList;
    }

    /**
     * @param pushWmsList 中间表list
     * @param v14         返回值
     */
    public void updateStatus(List<PsCSkuPushWmsMidModel> pushWmsList, ValueHolderV14<List<QimenlResult>> v14) {
        try {
            Map<String, PsCSkuPushWmsMidModel> skuMap = pushWmsList.stream().collect(Collectors
                    .toMap(item -> item.getPsCSkuEcode() + "_" + "OTHER", Function.identity(), (k1, k2) -> k1));
            List<PsCSkuPushWmsMidModel> updateList = Lists.newArrayList();
            List<QimenlResult> results = v14.getData();
            if (CollectionUtils.isNotEmpty(results)) {
                results.forEach(item -> {
                    if (StringUtils.isNotEmpty(item.getBillNo())) {
                        PsCSkuPushWmsMidModel orgModel = skuMap.get(item.getBillNo());
                        if (!Objects.isNull(orgModel)) {
                            PsCSkuPushWmsMidModel updateModel = new PsCSkuPushWmsMidModel();
                            updateModel.setId(orgModel.getId());
                            updateModel.setModifieddate(new Date());
                            updateModel.setPassWmsTime(new Date());
                            updateList.add(updateModel);
                            if (ResultCode.FAIL == item.getCode()) {
                                updateModel.setWmsStatus(PsExtConstantsIF.TO_WMS_STATUS_FAIL);
                                String s = item.getMsg().length() > 255 ? item.getMsg().substring(0, 255) : item.getMsg();
                                updateModel.setFailureReason(s);
                                Integer failNum = Optional.ofNullable(orgModel.getFailureNum()).orElse(0) + 1;
                                updateModel.setFailureNum(failNum);
                            }
                            else {
                                updateModel.setWmsStatus(PsExtConstantsIF.TO_WMS_STATUS_SUCCESS);
                                updateModel.setFailureReason(StringUtils.EMPTY);
                                updateModel.setItemId(item.getItemId());
                            }
                        }
                    }
                });
                if (CollectionUtils.isNotEmpty(updateList)) {
                    PsCSkuSaveWmsService bean = ApplicationContextHandle.getBean(PsCSkuSaveWmsService.class);
                    bean.updateBatchById(updateList, PsExtConstantsIF.SELECT_PAGE_SIZE_TWO_HUNDRED);
                }
            }
            else {
                log.info("数据为空，请检查参数后重试");
            }
        }
        catch (Exception e) {
            log.error(LogUtil.format("PsCSkuSaveService.error updateStatus message:{}",
                    "PsCSkuSaveService.error updateStatus message"), Throwables.getStackTraceAsString(e));
            throw new NDSException(Throwables.getStackTraceAsString(e));
        }
    }
}
