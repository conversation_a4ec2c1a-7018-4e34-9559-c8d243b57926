//package com.jackrain.nea.psext.dts.subscription.consumer;
//
//import com.aliyun.dts.subscribe.clients.metastore.AbstractUserMetaStore;
//import com.jackrain.nea.redis.config.CusRedisTemplate;
//import com.jackrain.nea.redis.util.RedisOpsUtil;
//
///**
// * store the checkpoint data in the shared storage, such us database, shared file storage...
// * this meta store need to be completed by consumer
// */
//public class UserMetaStore extends AbstractUserMetaStore {
//
//    private CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
//
//    @Override
//    protected void saveData(String groupID, String toStoreJson) {
//        redisTemplate.opsForValue().set("ps:dts:" + groupID, toStoreJson);
//    }
//
//    @Override
//    protected String getData(String groupID) {
//        return redisTemplate.opsForValue().get("ps:dts:" + groupID);
//    }
//}
