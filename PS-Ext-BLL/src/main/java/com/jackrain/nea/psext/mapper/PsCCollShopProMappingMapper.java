package com.jackrain.nea.psext.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jackrain.nea.psext.model.table.PsCCollShopProMapping;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/8/23
 */
@Mapper
public interface PsCCollShopProMappingMapper extends BaseMapper<PsCCollShopProMapping> {

    @Select("select count(id) from ps_c_coll_shop_pro_mapping where cp_c_shop_id =#{shopId} and ps_c_sku_id=#{skuId} and isactive='Y'")
    Integer selectItemsByShopIdAndSku(@Param("shopId") Long shopId, @Param("skuId") Long skuId);

    @Select("select * from ps_c_coll_shop_pro_mapping where cp_c_shop_id =#{shopId} and ps_c_sku_id=#{skuId} and isactive='Y' limit 1")
    PsCCollShopProMapping selectItemByShopIdAndSku(@Param("shopId") Long shopId, @Param("skuId") Long skuId);

    @Select("select * from ps_c_coll_shop_pro_mapping where cp_c_shop_id =#{shopId} and isactive='Y'")
    List<PsCCollShopProMapping> selectItemByShopId(@Param("shopId") Long shopId);

    @Select("<script>" +
            "select * from ps_c_coll_shop_pro_mapping  where cp_c_shop_id =#{shopId} and isactive='Y' and ps_c_sku_id in"
            + "<foreach item='skuId' index='index' collection='skuId' open='(' separator=',' close=')'>"
            + " #{skuId} "
            + "</foreach>"
            + "</script>")
    List<PsCCollShopProMapping> selectProMappingByShopIdAndSku(@Param("shopId") Long shopId, @Param("skuId") List<Long> skuId);

}
