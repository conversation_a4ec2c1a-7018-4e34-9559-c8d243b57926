//package com.jackrain.nea.psext.converter.impl;
//
//import com.alibaba.fastjson.JSONObject;
//import com.aliyun.dts.subscribe.clients.record.RowImage;
//import com.aliyun.dts.subscribe.clients.record.value.Value;
//import com.baomidou.mybatisplus.core.mapper.BaseMapper;
//import com.jackrain.nea.psext.converter.R3Processor;
//import com.jackrain.nea.psext.enums.DtsBeanEnum;
//import com.jackrain.nea.psext.mapper.PsErpiesProductMapper;
//import com.jackrain.nea.psext.model.table.PsErpiesProduct;
//import com.jackrain.nea.psext.utils.PsUtils;
//import com.jackrain.nea.resource.SystemUserResource;
//import com.jackrain.nea.web.face.User;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.math.BigDecimal;
//
///**
// * @ClassName : PsErpiesProductProcessor
// * @Description :
// * <AUTHOR>  YCH
// * @Date: 2022-03-08 10:53
// */
//@Component
//@Slf4j
//public class PsErpiesProductProcessor  implements R3Processor<RowImage, PsErpiesProduct> {
//
//    @Autowired
//    private PsErpiesProductMapper psErpiesProductMapper;
//
//    @Override
//    public String getTableName() {
//        return DtsBeanEnum.T_IESSTANDPLATSKU.name();
//    }
//
//    @Override
//    public BaseMapper<PsErpiesProduct> getMapper() {
//        return psErpiesProductMapper;
//    }
//
//    @Override
//    public PsErpiesProduct convert(RowImage rowImage) {
//        log.info(" PsErpiesProductProcessor开始 ");
//        PsErpiesProduct psErpiesProduct = new PsErpiesProduct();
//        try{
//            Value<BigDecimal> id = rowImage.getValue("ID");
//            if (null == id) {
//                return psErpiesProduct;
//            }
//            psErpiesProduct.setId(id.getData().longValue());
//
//            Value<BigDecimal> skuId = rowImage.getValue("SKUID");
//            if (null != skuId) {
//                psErpiesProduct.setPsCSkuId(skuId.getData().longValue());
//            }
//
//            Value<BigDecimal> shopId = rowImage.getValue("SHOPID");
//            if (null != shopId) {
//                psErpiesProduct.setCpCShopId(shopId.getData().longValue());
//            }
//            psErpiesProduct.setCpCShopId(Long.parseLong(shopId.toString()));
//
//            Value<String> mProductId = rowImage.getValue("OUTERID");
//            if (null != mProductId) {
//                psErpiesProduct.setPsCSkuEcode(mProductId.toString());
//            }
//            Value<String> productcode = rowImage.getValue("PRODUCTCODE");
//            if (null != productcode) {
//                psErpiesProduct.setPsCProEcode(productcode.toString());
//            }
//            psErpiesProduct.setStatus(1);
//            User user = SystemUserResource.getRootUser();
//            PsUtils.setBModelDefalutData(psErpiesProduct,user);
//            log.info(" PsErpiesProductProcessor.convert data PsErpiesProduct {}", JSONObject.toJSON(psErpiesProduct));
//            return psErpiesProduct;
//        }catch (Exception e){
//            log.error(" PsErpiesProductProcessor.convert error {}",e.getMessage());
//            return psErpiesProduct;
//        }
//
//    }
//}
