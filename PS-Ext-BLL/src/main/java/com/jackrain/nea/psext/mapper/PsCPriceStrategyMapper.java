package com.jackrain.nea.psext.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.psext.model.table.PsCPriceStrategy;
import com.jackrain.nea.psext.result.PriceResult;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface PsCPriceStrategyMapper extends ExtentionMapper<PsCPriceStrategy> {

    /*@SelectProvider(type = PriceQueryProvider.class, method = "getPscPriceStrategyByItem")
    @Results({@Result(property = "coefficient", column = "coefficient"), @Result(property = "coefficientType", column = "coefficient_type"),@Result(property = "priceStandard", column = "price_standard")})
    PriceResult getPscPriceStrategyByItem(QueryParam queryParam);

    @SelectProvider(type = PriceQueryProvider.class, method = "getPscPriceStrategyByType")
    @Results({@Result(property = "coefficient", column = "coefficient"), @Result(property = "coefficientType", column = "coefficient_type"),@Result(property = "priceStandard", column = "price_standard")})
    PriceResult getPscPriceStrategyByType(QueryParam queryParam);

   @SelectProvider(type = PriceQueryProvider.class, method = "getPscPriceStrategy")
    @Results({@Result(property = "coefficient", column = "coefficient"), @Result(property = "coefficientType", column = "coefficient_type"),@Result(property = "priceStandard", column = "price_standard")})
    PriceResult getPscPriceStrategy(QueryParam queryParam);*/

    @Select("SELECT b.coefficient_type,b.coefficient,a.price_standard FROM PS_C_PRICE_STRATEGY a " +
            "LEFT JOIN PS_C_PRICE_STRATEGY_PRO_ITEM b ON a.ID = b.PS_C_PRICE_STRATEGY_ID " +
            "where a.cp_c_customer_ecode = #{customerEcode} " +
            "AND a.sale_dis_type= #{saleDiscountType} " +
            "AND b.ps_c_pro_ecode = #{pscProEcode}")
    List<PriceResult> getPscPriceStrategyByItem(@Param("pscProEcode") String pscProEcode, @Param("customerEcode") String customerEcode, @Param("saleDiscountType") String saleDiscountType);

    @Select("SELECT b.coefficient_type,b.coefficient,a.price_standard FROM PS_C_PRICE_STRATEGY a " +
            "LEFT JOIN PS_C_PRICE_STRATEGY_DISCOUNT_TYPE b ON a.ID = b.PS_C_PRICE_STRATEGY_ID " +
            "where a.cp_c_customer_ecode = #{customerEcode} " +
            "AND a.sale_dis_type= #{saleDiscountType} " +
            "AND b.ps_c_prodim_discount_id = #{prodimDiscountId}")
    List<PriceResult> getPscPriceStrategyByType(@Param("prodimDiscountId") Long prodimDiscountId, @Param("customerEcode") String customerEcode, @Param("saleDiscountType") String saleDiscountType);

    @Select("SELECT coefficient_type,coefficient,price_standard FROM PS_C_PRICE_STRATEGY " +
            "where cp_c_customer_ecode = #{customerEcode} " +
            "AND sale_dis_type= #{saleDiscountType} ")
    List<PriceResult> getPscPriceStrategy(@Param("customerEcode") String customerEcode, @Param("saleDiscountType") String saleDiscountType);

}