package com.jackrain.nea.psext.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.data.basic.common.BasicConstants;
import com.jackrain.nea.data.basic.services.BasicCpQueryService;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.psext.mapper.PsCProGroupMapper;
import com.jackrain.nea.psext.mapper.PsCProMapper;
import com.jackrain.nea.psext.mapper.PsCSkuMapper;
import com.jackrain.nea.psext.mapper.PsCSkugroupMapper;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.psext.model.table.PsCProGroup;
import com.jackrain.nea.psext.model.table.PsCSku;
import com.jackrain.nea.psext.model.table.PsCSkugroup;
import com.jackrain.nea.psext.request.ProGroupRequest;
import com.jackrain.nea.psext.request.SkuGroupExportRequest;
import com.jackrain.nea.psext.request.SkuGroupRequest;
import com.jackrain.nea.psext.utils.PsUtils;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.utils.ValueHolderV14Utils;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * create by 2019-07-20
 */

@Component
@Slf4j
public class SkuGroupSaveService {
    @Autowired
    private PsCSkugroupMapper psCSkugroupMapper;
    @Autowired
    private PsCProMapper psCProMapper;
    @Autowired
    private PsCSkuMapper psCSkuMapper;
    @Autowired
    private PsCProGroupMapper psCProGroupMapper;
    @Autowired
    private BasicCpQueryService basicCpQueryService;
    @Autowired
    private SkuGroupIsActiveYService skuGroupIsActiveYService;

    public ValueHolderV14<HashMap<String, String>> excute(ProGroupRequest proRequest, QuerySession querySession) throws NDSException {
        ValueHolderV14<HashMap<String, String>> retVh = new ValueHolderV14<HashMap<String, String>>();
        log.info(LogUtil.format("##SkuGroupSaveService##excute##param：{}", "SkuGroupSaveService"), proRequest.toString());
        Long objid = proRequest.getObjid();
        String checkDataMsg = checkData(proRequest, objid, querySession);
        if (checkDataMsg != null) {
            retVh.setCode(-1);
            retVh.setMessage(checkDataMsg);
            return retVh;
        }
        String message = checkDataUnique(proRequest, objid, querySession);
        if (message != null) {
            retVh.setCode(-1);
            retVh.setMessage(message);
            return retVh;
        }
        try {
            if (objid < 0) {
                retVh = ApplicationContextHandle.getBean(SkuGroupSaveService.class).
                        insertDataService(proRequest, objid, querySession);

                if (retVh.getCode() == 0) {//保存成功后调用启用
                    HashMap<String, String> data = retVh.getData();
                    retVh = skuGroupIsActiveYService.startGoods(querySession, Long.valueOf(data.get("proId")));
                    if (retVh.getCode() == -1) {
                        retVh.setCode(0);
                        retVh.setMessage("保存成功，但启用失败，启用失败原因：" + retVh.getMessage());
                    } else {
                        retVh.setCode(0);
                        retVh.setMessage("保存成功,并启用成功");
                    }
                    //拿出主表id,是为了保存组合商品明细条码
                    objid = Long.valueOf(data.get("proId"));
                }
            } else {
                retVh = ApplicationContextHandle.getBean(SkuGroupSaveService.class).
                        updateDataService(proRequest, objid, querySession);
            }
        } catch (Exception e) {
            log.warn(LogUtil.format("组合商品新增保存记录组合商品条码明细异常:{}", "SkuGroupSaveService.execute"),
                    Throwables.getStackTraceAsString(e));
            retVh.setCode(-1);
            retVh.setMessage("组合商品新增保存记录组合商品条码明细异常:" + e.getMessage());
        }

        //保存组合商品明细条码
        log.info(LogUtil.format("组合商品新增保存记录组合商品条码明细主表id:{}", "SkuGroupSaveService"), objid);

        if (objid != null && objid > 0) {
            saveSkuEcode(objid);
        }

        return retVh;
    }

    /**
     * 组合商品新增保存记录组合商品条码明细
     *
     * @param objId 主表id
     */
    public void saveSkuEcode(Long objId) {
        try {
            List<Long> objIdList = new ArrayList<>();
            objIdList.add(objId);
            List<SkuGroupExportRequest> skuGroupExportRequests = psCProGroupMapper.selectSkuGroupExportById(objIdList);

            log.info(LogUtil.format("组合商品新增保存记录组合商品条码明细数据:{}",
                    "SkuGroupSaveService.saveSkuEcode"), CollectionUtils.isNotEmpty(skuGroupExportRequests) ? skuGroupExportRequests.size() : 0);

            if (CollectionUtils.isNotEmpty(skuGroupExportRequests)) {
                List<String> skuList = skuGroupExportRequests.stream().map(SkuGroupExportRequest::getPsCSkuEcode).collect(Collectors.toList());
                PsCProGroup psProGroup = new PsCProGroup();
                psProGroup.setId(objId);

                StringBuilder buffer = new StringBuilder();
                buffer.append(",");
                for (String str : skuList) {
                    buffer.append(str.trim()).append(",");
                }

                psProGroup.setPsCSkuEcode(buffer.toString());
                psProGroup.setPsCSkuEcodeSize(skuList.size());
                psCProGroupMapper.updateById(psProGroup);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("组合商品新增保存记录组合商品条码明细异常:{}",
                    "SkuGroupSaveService.error"), Throwables.getStackTraceAsString(e));
        }


    }

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<HashMap<String, String>> insertDataService(ProGroupRequest proRequest, Long objid, QuerySession querySession) throws NDSException {
        ValueHolderV14<HashMap<String, String>> vh = new ValueHolderV14<HashMap<String, String>>();
        String psCStoreIds = proRequest.getCP_C_STORE_IDS();
        StringBuilder sbEcode = new StringBuilder();
        StringBuilder sbEname = new StringBuilder();
        //获取商品主表，商品档案的实体对象
        PsCProGroup psCProGroup = proRequest.getPsCPro();
        PsCPro psCpro = new PsCPro();

        log.debug("获取主表ID》》》》》》");
        Long proId = ModelUtil.getSequence("PS_C_PRO");
        log.debug("获取主表ID成功》》》》》》" + proId);
        try {
            BeanUtils.copyProperties(psCProGroup, psCpro);
        } catch (Exception e) {
            log.error(LogUtil.format("字段值拷贝异常", "组合商品"), e);
            throw new NDSException("组合商品保存异常");
        }
        psCpro.setId(proId);
        psCpro.setIsGroup("Y");
        psCpro.setVideo("PS_C_PRO表插入数据时，排除非空字段的check");
        PsUtils.setBModelDefalutData(psCpro, querySession.getUser());
        psCpro.setOwnerename(querySession.getUser().getEname());
        psCpro.setModifierename(querySession.getUser().getEname());

        psCProGroup.setId(proId);
        psCProGroup.setCpCStoreIds(psCStoreIds);
        psCProGroup.setCpCStoreEcode(sbEcode.toString());
        psCProGroup.setCpCStoreEname(sbEname.toString());
        psCProGroup.setOwnerename(querySession.getUser().getEname());
        psCProGroup.setModifierename(querySession.getUser().getEname());
        PsUtils.setBModelDefalutData(psCProGroup, querySession.getUser());
        try {
            psCProGroupMapper.insert(psCProGroup);
        } catch (Exception e) {
            log.debug("新增PS_C_PRO_GROUP表失败》》》》》》异常信息：" + e.getMessage());
            throw new NDSException("组合商品主表保存异常");
        }
        log.debug("新增商品表成功》》》》》》");

        List<SkuGroupRequest> skuGroupRequestList = proRequest.getSkuGroupRequestList();
        if (CollectionUtils.isEmpty(skuGroupRequestList)) {
            throw new NDSException("组合商品明细不能为空");
        }
        BigDecimal totWeight = BigDecimal.ZERO;
        for (SkuGroupRequest skuGroupRequest : skuGroupRequestList) {
            //获取商品子表，条码档案表的实体对象
            PsCSku psCSku = skuGroupRequest.getPsCSku();
            Long skuId = ModelUtil.getSequence("PS_C_SKU");
            psCSku.setId(skuId);
            psCSku.setIsactive("Y");
            psCSku.setPsCProId(proId);
            psCSku.setOwnerid(Long.valueOf(querySession.getUser().getId()));
            psCSku.setOwnername(querySession.getUser().getName());
            psCSku.setOwnerename(querySession.getUser().getEname());
            psCSku.setAdClientId(Long.valueOf(querySession.getUser().getClientId()));
            psCSku.setAdOrgId(Long.valueOf(querySession.getUser().getOrgId()));
            psCSku.setPsCProEcode(psCpro.getEcode());
            psCSku.setCreationdate(new Timestamp(System.currentTimeMillis()));
            psCSku.setWareType(psCpro.getGroupType());
            psCSku.setIsTowms(0);
            psCSku.setIsPushWms(YesNoEnum.N.getKey());
            psCSku.setIsPushJdyc(YesNoEnum.N.getKey());
            psCSku.setIsPushNk(YesNoEnum.Y.getKey());
            try {
                psCSkuMapper.insert(psCSku);
            } catch (Exception e) {
                log.debug("新增PS_C_SKU表失败》》》》》》异常信息：" + e.getMessage());
                throw new NDSException("条码明细保存异常");
            }
            log.debug("新增PS_C_SKU表成功》》》》》》");
            List<PsCSkugroup> psCSkugroupList = skuGroupRequest.getPsCSkugroupList();
            //循环遍历List，获取商品子表，组合商品明细档案表的实体对象
            for (PsCSkugroup psCSkugroup : psCSkugroupList) {
                Long skuGroupId = ModelUtil.getSequence("PS_C_SKUGROUP");
                psCSkugroup.setId(skuGroupId);
                psCSkugroup.setIsactive("Y");
                psCSkugroup.setPricelist(BigDecimal.ZERO);
                PsCSku sku = psCSkuMapper.selectSkuBySkuEcode(psCSkugroup.getPsCSkuEcode());
                if (Objects.isNull(sku)) {
                    throw new NDSException("SKU不存在：" + psCSkugroup.getPsCSkuEcode());
                }
                //当不是赠品时需要查询价格并取标准成本价，否则为0
                if (sku != null && sku.getPsCProId() != null) {
                    PsCPro pro = psCProMapper.selectById(sku.getPsCProId());
                    if (pro != null) {
                        if (pro.getPriceSaleList() != null) {
                            psCSkugroup.setPricelist(pro.getPriceSaleList());
                        } else if (pro.getPriceCostList() != null) {
                            psCSkugroup.setPricelist(pro.getPriceCostList());
                        }
                        if (StringUtils.isNotEmpty(pro.getWeight())) {
                            totWeight = totWeight.add(new BigDecimal(pro.getWeight()).multiply(psCSkugroup.getNum()));
                        }
                    }
                }
                psCSkugroup.setPsCSkuId(sku.getId());
                psCSkugroup.setOwnerid(Long.valueOf(querySession.getUser().getId()));
                psCSkugroup.setOwnername(querySession.getUser().getName());
                psCSkugroup.setOwnerename(querySession.getUser().getEname());
                psCSkugroup.setAdOrgId(Long.valueOf(querySession.getUser().getOrgId()));
                psCSkugroup.setAdClientId(Long.valueOf(querySession.getUser().getClientId()));
                psCSkugroup.setCreationdate(new Timestamp(System.currentTimeMillis()));
                //设置组合商品的虚拟条码和id
                psCSkugroup.setPsCSkuGrpEcode(psCSku.getEcode());
                psCSkugroup.setPsCSkuGrpId(skuId);
                psCSkugroup.setPsCProId(proId);
                try {
                    psCSkugroupMapper.insert(psCSkugroup);
                } catch (Exception e) {
                    log.debug("新增PS_C_SKUGROUP表失败》》》》》》异常信息：" + e.getMessage());
                    throw new NDSException("组合商品明细保存异常");
                }
                log.info("组合商品，新增保存成功！");
            }
        }
        psCpro.setWeight(totWeight.stripTrailingZeros().toPlainString());
        try {
            psCProMapper.insert(psCpro);
        } catch (Exception e) {
            log.warn("新增PS_C_PRO表失败》》》》》》异常信息：{}",
                    Throwables.getStackTraceAsString(e));
            throw new NDSException("商品表保存异常:" + e.getMessage());
        }
        HashMap<String, String> map = new HashMap<String, String>();
        map.put("proId", String.valueOf(proId));
        vh.setData(map);
        vh.setCode(0);
        return vh;
    }

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<HashMap<String, String>> updateDataService(ProGroupRequest proRequest, Long objid, QuerySession querySession) {
        if (log.isDebugEnabled()) {
            log.info("update入参： ->" + proRequest);
        }
        ValueHolderV14<HashMap<String, String>> vh = new ValueHolderV14<HashMap<String, String>>();
        HashMap<String, Object> hashMap = new HashMap<>();
        PsCProGroup psCProGroup = proRequest.getPsCPro();
        PsCProGroup oldObj = psCProGroupMapper.selectById(objid);
        String psCStoreIds = proRequest.getCP_C_STORE_IDS();
        StringBuilder sbEcode = new StringBuilder();
        StringBuilder sbEname = new StringBuilder();
        //##2022-04-27 组合商品需求-去掉逻辑仓库项目
        /*StoreInfoQueryRequest storeInfoQueryRequest = new StoreInfoQueryRequest();
        List<Long> ids = new ArrayList<>();
        String[] d = psCStoreIds.split(",");
        long[] strArrNum = (long[]) ConvertUtils.convert(d, long.class);
        for (int i = 0; i < strArrNum.length; i++) {
            Collections.addAll(ids, strArrNum[i]);
        }
        storeInfoQueryRequest.setIds(ids);
        HashMap<Long, com.jackrain.nea.cp.result.CpCStore> storeInfos = null;
        log.info("根据逻辑仓id" + ids + "查询逻辑仓信息入参：" + storeInfoQueryRequest);
        try {
            storeInfos = basicCpQueryService.getStoreInfo(storeInfoQueryRequest);
            log.info("根据逻辑仓id" + ids + "查询逻辑仓信息：" + storeInfos);
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (storeInfos.size() != 0) {
            for (Map.Entry<Long, com.jackrain.nea.cp.result.CpCStore> entry : storeInfos.entrySet()) {
                com.jackrain.nea.cp.result.CpCStore value = entry.getValue();
                sbEcode.append(value.getEcode() + ",");
                sbEname.append(value.getEname() + ",");
            }
            sbEcode.deleteCharAt(sbEcode.length() - 1);
            sbEname.deleteCharAt(sbEname.length() - 1);
        } else {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("更新调用根据逻辑仓id获取逻辑仓信息为空");
            return vh;
        }*/

        PsCPro psCpro = new PsCPro();
        try {
            BeanUtils.copyProperties(psCProGroup, psCpro);
        } catch (Exception e) {
            log.error(LogUtil.format("字段值拷贝异常:{}", "组合商品"),
                    Throwables.getStackTraceAsString(e));
            throw new NDSException("组合商品保存异常:" + e.getMessage());
        }
        String proEcode = psCpro.getEcode();
        List<SkuGroupRequest> skuGroupRequestList = proRequest.getSkuGroupRequestList();

        psCpro.setId(objid);
        psCpro.setModifierid(Long.valueOf(querySession.getUser().getId()));
        psCpro.setModifiername(querySession.getUser().getName());
        psCpro.setModifierename(querySession.getUser().getEname());
        psCpro.setModifieddate(new Timestamp(System.currentTimeMillis()));

        psCProGroup.setId(objid);
        psCProGroup.setCpCStoreIds(psCStoreIds);
        psCProGroup.setCpCStoreEcode(sbEcode.toString());
        psCProGroup.setCpCStoreEname(sbEname.toString());
        psCProGroup.setRemark(psCpro.getRemark());
        psCProGroup.setModifierid(Long.valueOf(querySession.getUser().getId()));
        psCProGroup.setModifiername(querySession.getUser().getName());
        psCProGroup.setModifierename(querySession.getUser().getEname());
        psCProGroup.setModifieddate(new Timestamp(System.currentTimeMillis()));
//        if (StringUtils.isNotEmpty(psCpro.getCanSplit())){
//            psCProGroup.setCanSplit(psCpro.getCanSplit());
//        }
//        if (Objects.nonNull(psCpro.getCpCShopId())){
//            psCProGroup.setCpCShopId(psCpro.getCpCShopId());
//        }
//        if (Objects.nonNull(psCpro.getSkuGroupType())){
//            psCProGroup.setSkuGroupType(psCpro.getSkuGroupType());
//        }
//        if (StringUtils.isNotEmpty(psCpro.getCpCShopTitle())){
//            psCProGroup.setCpCShopTitle(psCpro.getCpCShopTitle());
//        }
//        if (StringUtils.isNotEmpty(psCpro.getCpCShopEcode())){
//            psCProGroup.setCpCShopEcode(psCpro.getCpCShopEcode());
//        }
        psCProGroupMapper.updateById(psCProGroup);
        if (CollectionUtils.isEmpty(skuGroupRequestList)) {
            throw new NDSException("组合商品新增页面保存时，传入数据为空！");
        }
        BigDecimal totWeight = BigDecimal.ZERO;
        for (SkuGroupRequest skuGroupRequest : skuGroupRequestList) {
            //获取商品子表，条码档案表的实体对象
            PsCSku psCSku = skuGroupRequest.getPsCSku();
            //通过sku_id是否为-1 判断新增 还是更新
            Long sku_id = psCSku.getId();
            if (sku_id == -1) {
                psCSku.setPsCProId(objid);
                psCSku.setOwnerid(Long.valueOf(querySession.getUser().getId()));
                psCSku.setOwnername(querySession.getUser().getName());
                psCSku.setOwnerename(querySession.getUser().getEname());
                psCSku.setAdClientId(Long.valueOf(querySession.getUser().getClientId()));
                psCSku.setAdOrgId(Long.valueOf(querySession.getUser().getOrgId()));
                psCSku.setModifierid(Long.valueOf(querySession.getUser().getId()));
                psCSku.setModifiername(querySession.getUser().getName());
                psCSku.setModifierename(querySession.getUser().getEname());
                psCSku.setCreationdate(new Timestamp(System.currentTimeMillis()));
                psCSku.setPsCProEcode(psCpro.getEcode());
                psCSku.setModifieddate(new Timestamp(System.currentTimeMillis()));
                sku_id = ModelUtil.getSequence("PS_C_SKU");
                psCSku.setId(sku_id);
                psCSku.setWareType(psCpro.getGroupType());
                psCSku.setIsactive("N");
                psCSkuMapper.insert(psCSku);
            }
            psCSku.setPsCProId(objid);
            psCSku.setModifierid(Long.valueOf(querySession.getUser().getId()));
            psCSku.setModifiername(querySession.getUser().getName());
            psCSku.setModifierename(querySession.getUser().getEname());
            psCSku.setPsCProEcode(psCpro.getEcode());
            psCSku.setModifieddate(new Timestamp(System.currentTimeMillis()));
            psCSkuMapper.updateById(psCSku);
            List<PsCSkugroup> psCSkugroupList = skuGroupRequest.getPsCSkugroupList();
            //循环遍历List，获取商品子表，组合商品明细档案表的实体对象
            for (PsCSkugroup psCSkugroup : psCSkugroupList) {
                Long groupId = psCSkugroup.getId();
                PsCSku sku = psCSkuMapper.selectSkuBySkuEcode(psCSkugroup.getPsCSkuEcode());
                PsCPro pro = null;
                //当不是赠品时需要查询标准成本价
                if (sku != null && sku.getPsCProId() != null) {
                    pro = psCProMapper.selectById(sku.getPsCProId());
                }
                if (groupId == -1) {
                    groupId = ModelUtil.getSequence("PS_C_SKUGROUP");
                    psCSkugroup.setId(groupId);
                    psCSkugroup.setPsCSkuId(sku.getId());
                    psCSkugroup.setOwnerid(Long.valueOf(querySession.getUser().getId()));
                    psCSkugroup.setOwnername(querySession.getUser().getName());
                    psCSkugroup.setOwnerename(querySession.getUser().getEname());
                    psCSkugroup.setModifierid(Long.valueOf(querySession.getUser().getId()));
                    psCSkugroup.setModifiername(querySession.getUser().getName());
                    psCSkugroup.setModifierename(querySession.getUser().getEname());
                    psCSkugroup.setAdClientId(Long.valueOf(querySession.getUser().getClientId()));
                    psCSkugroup.setAdOrgId(Long.valueOf(querySession.getUser().getOrgId()));
                    psCSkugroup.setPsCSkuGrpEcode(psCSku.getEcode());
                    psCSkugroup.setPsCSkuGrpId(sku_id);
                    psCSkugroup.setPsCProId(objid);
                    psCSkugroup.setIsactive("N");
                    psCSkugroup.setModifieddate(new Timestamp(System.currentTimeMillis()));
                    psCSkugroup.setPricelist(BigDecimal.ZERO);
                    if (pro != null) {
                        if (pro.getPriceSaleList() != null) {
                            psCSkugroup.setPricelist(pro.getPriceSaleList());
                        } else if (pro.getPriceCostList() != null) {
                            psCSkugroup.setPricelist(pro.getPriceCostList());
                        }
                        if (StringUtils.isNotEmpty(pro.getWeight())) {
                            totWeight = totWeight.add(new BigDecimal(pro.getWeight()).multiply(psCSkugroup.getNum()));
                        }
                    }
                    psCSkugroupMapper.insert(psCSkugroup);
                }
                psCSkugroup.setPsCSkuId(sku.getPsCSkuId());
                psCSkugroup.setModifierid(Long.valueOf(querySession.getUser().getId()));
                psCSkugroup.setModifiername(querySession.getUser().getName());
                psCSkugroup.setModifierename(querySession.getUser().getEname());
                psCSkugroup.setPsCSkuGrpEcode(psCSku.getEcode());
                psCSkugroup.setPsCSkuGrpId(sku_id);
                psCSkugroup.setPsCProId(objid);
                psCSkugroup.setModifieddate(new Timestamp(System.currentTimeMillis()));
                psCSkugroup.setPricelist(BigDecimal.ZERO);
                if (pro != null) {
                    if (pro.getPriceSaleList() != null) {
                        psCSkugroup.setPricelist(pro.getPriceSaleList());
                    } else if (pro.getPriceCostList() != null) {
                        psCSkugroup.setPricelist(pro.getPriceCostList());
                    }
                    if (StringUtils.isNotEmpty(pro.getWeight())) {
                        totWeight = totWeight.add(new BigDecimal(pro.getWeight()).multiply(psCSkugroup.getNum()));
                    }
                }
                psCSkugroupMapper.updateById(psCSkugroup);
                log.info("组合商品，修改保存成功！");
            }
        }
        psCpro.setWeight(totWeight.stripTrailingZeros().toPlainString());
        psCProMapper.updateById(psCpro);
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("修改保存成功");
        return vh;
    }

    /**
     * 插入虚拟条码到redis    (此代码目前提供给李杰用，后续将删除)
     * @param skuGroupRequestList
     */
//    public void insertRedis(List<SkuGroupRequest> skuGroupRequestList){
//        if (log.isDebugEnabled()) {
//            log.info("insert virtual redis入参： ->" + skuGroupRequestList);
//        }
//        try{
//            CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
//            for (SkuGroupRequest skuGroupRequest : skuGroupRequestList) {
//                VirtualSkuRequest virtualSkuRequest=new VirtualSkuRequest();
//                PsCSku psCSku = skuGroupRequest.getPsCSku();
//                if (log.isDebugEnabled()) {
//                    log.info("sku info： ->" + psCSku);
//                }
//                Long psCProId=psCSku.getPsCProId();
//                //主表信息
//                PsCProGroup psCProGroup=psCProGroupMapper.selectById(psCProId);
//                String cpCStoreIds=psCProGroup.getCpCStoreIds();
//                String cpCStoreEcodes= psCProGroup.getCpCStoreEcode();
//                String cpCStoreEnames=psCProGroup.getCpCStoreEname();
//                String[] splitStoreId= cpCStoreIds.split(",");
//                String[] splitStoreEcode= cpCStoreEcodes.split(",");
//                String[] splitStoreEname= cpCStoreEnames.split(",");
//                List<VirtualSkuRealInfoRequest> virtualSkuRealInfoRequests=null;
//                for (int i = 0; i < splitStoreId.length; i++) {
//                    virtualSkuRequest.setId(psCSku.getId());
//                    virtualSkuRequest.setEcode(psCSku.getEcode());
//                    virtualSkuRequest.setGroupExtractNum(psCSku.getGroupExtractNum());
//                    virtualSkuRequest.setPsCProId(psCProGroup.getId());
//                    virtualSkuRequest.setPsCProEcode(psCProGroup.getEcode());
//                    virtualSkuRequest.setPsCProEname(psCProGroup.getEname());
//                    virtualSkuRequest.setCpCStoreId(Long.valueOf(splitStoreId[i]));
//                    virtualSkuRequest.setCpCStoreEcode(splitStoreEcode[i]);
//                    virtualSkuRequest.setCpCStoreEname(splitStoreEname[i]);
//                    List<PsCSkugroup> psCSkugroupList = skuGroupRequest.getPsCSkugroupList();
//                    if (log.isDebugEnabled()) {
//                        log.info("skuGroup info： ->" + psCSkugroupList);
//                    }
//                    virtualSkuRealInfoRequests=new ArrayList<VirtualSkuRealInfoRequest>();
//                    for(PsCSkugroup psCSkugroup:psCSkugroupList){
//                        VirtualSkuRealInfoRequest virtualSkuRealInfoRequest=new VirtualSkuRealInfoRequest();
//                        PsCSku sku = psCSkuMapper.selectSkuBySkuEcode(psCSkugroup.getPsCSkuEcode());
//                        virtualSkuRealInfoRequest.setId(sku.getId());
//                        virtualSkuRealInfoRequest.setECode(psCSkugroup.getPsCSkuEcode());
//                        virtualSkuRealInfoRequest.setNum(psCSkugroup.getNum());
//                        virtualSkuRealInfoRequest.setGroupNum(psCSkugroup.getGroupnum());
//                        virtualSkuRealInfoRequests.add(virtualSkuRealInfoRequest);
//                    }
//                    virtualSkuRequest.setSku(virtualSkuRealInfoRequests);
//                    JSONObject json=JSONObject.parseObject(JSON.toJSONStringWithDateFormat(virtualSkuRequest,
//                            "yyyy-MM-dd HH:mm:ss", SerializerFeature.IgnoreNonFieldGetter), Feature.OrderedField);
//                    if (log.isDebugEnabled()) {
//                        log.info("virtualRedis save info： ->" + json);
//                    }
//                    redisTemplate.opsForHash().put("COMBINED:SKUGROUP:"+psCSku.getId(),splitStoreId[i],json.toString());
//                }
//            }
//        }catch (Exception e){
//            log.debug("插入虚拟条码到redis"+e.getMessage());
//        }
//    }

    /**
     * 插入实际条码到redis  (此代码目前提供给李杰用，后续将删除)
     * @param proRequest
     */
//    public void insertRealRedis(ProRequest proRequest){
//        PsCPro psCpro = proRequest.getPsCPro();
//        try{
//            CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
//            Long proId=psCpro.getId();
//            PsCProGroup psCProGroup=psCProGroupMapper.selectById(proId);
//            String cpCStoreIds=psCProGroup.getCpCStoreIds();
//            String[] splitStoreId= cpCStoreIds.split(",");
//            List<PsCSkugroup> psCSkugroups=psCSkugroupMapper.selectSkuGroupInfoByProId(proId);
//            for(int i=0 ;i< splitStoreId.length;i++) {
//                for (PsCSkugroup psCSkugroup : psCSkugroups) {
//                    SkuRequest skuRequest = new SkuRequest();
//                    skuRequest.setId(psCSkugroup.getPsCSkuId());
//                    skuRequest.setEcode(psCSkugroup.getPsCSkuEcode());
//                    skuRequest.setCpCStoreId(Long.valueOf(splitStoreId[i]));
//                    List<SkuVituralInfoRequest> skuVituralInfoRequests=new ArrayList<SkuVituralInfoRequest> ();
//                    List<PsCSkugroup>  sCSkugroups=psCSkugroupMapper.selectSkuIdByEcode(psCSkugroup.getPsCSkuEcode(),proId);
//
//                    for(PsCSkugroup sCSkugroup:sCSkugroups){
//                        PsCSku psSku = psCSkuMapper.selectById(sCSkugroup.getPsCSkuGrpId());
//                        SkuVituralInfoRequest skuVituralInfoRequest=new SkuVituralInfoRequest();
//                        skuVituralInfoRequest.setId(psSku.getId());
//                        skuVituralInfoRequest.setECode(psSku.getEcode());
//                        skuVituralInfoRequest.setNum(sCSkugroup.getNum());
//                        skuVituralInfoRequest.setGroupNum(sCSkugroup.getGroupnum());
//                        skuVituralInfoRequest.setGroupExtractNum(psSku.getGroupExtractNum());
//                        skuVituralInfoRequest.setPsCProId(sCSkugroup.getPsCProId());
//                        skuVituralInfoRequests.add(skuVituralInfoRequest);
//                    }
//                    skuRequest.setSkuGroup(skuVituralInfoRequests);
//                    JSONObject json=JSONObject.parseObject(JSON.toJSONStringWithDateFormat(skuRequest,
//                            "yyyy-MM-dd HH:mm:ss", SerializerFeature.IgnoreNonFieldGetter), Feature.OrderedField);
//                    if (log.isDebugEnabled()) {
//                        log.info("realRedis save info： ->" + json);
//                    }
//                    redisTemplate.opsForHash().put("COMBINED:SKU:"+psCSkugroup.getPsCSkuId(),splitStoreId[i],json.toString());
//
//                }
//            }
//        }catch (Exception e){
//            log.debug("插入实际条码到redis异常"+e.getMessage());
//        }
//    }

    /**
     * 计算库存
     * @param querySession
     * @param id
     */
//    public void calculateStorage( QuerySession querySession, Long id){
//        PsCProGroup psCProGroup = psCProGroupMapper.selectById(id);
//        String storeIds=psCProGroup.getCpCStoreIds();
//        String cpCStoreEcodes= psCProGroup.getCpCStoreEcode();
//        String cpCStoreEnames=psCProGroup.getCpCStoreEname();
//        String[] splitStoreId= storeIds.split(",");
//        String[] splitStoreEcode= cpCStoreEcodes.split(",");
//        String[] splitStoreEname= cpCStoreEnames.split(",");
//        SgGroupStorageCalculateRequest sgGroupStorageCalculateRequest=new SgGroupStorageCalculateRequest();
//        List<SgGroupStorageVirtualSkuRequest> sList=new ArrayList<SgGroupStorageVirtualSkuRequest>();
//
//        for (int i = 0; i < splitStoreId.length; i++) {
//            QueryWrapper<PsCSku> wrapperSku = new QueryWrapper<PsCSku>();
//            wrapperSku.eq("PS_C_PRO_ID",id);
//            List<PsCSku> psCSkus=psCSkuMapper.selectList(wrapperSku);
//            for(PsCSku psCSku:psCSkus){
//                List<SgGroupStorageSkuRequest> sgGroupStorageSkuRequestList=new ArrayList<SgGroupStorageSkuRequest>();
//                SgGroupStorageVirtualSkuRequest sgGroupStorageVirtualSkuRequest=new SgGroupStorageVirtualSkuRequest();
//                sgGroupStorageVirtualSkuRequest.setId(psCSku.getId());
//                sgGroupStorageVirtualSkuRequest.setEcode(psCSku.getEcode());
//                sgGroupStorageVirtualSkuRequest.setGroupExtractNum(psCSku.getGroupExtractNum());
//                sgGroupStorageVirtualSkuRequest.setPsCProId(psCProGroup.getId());
//                sgGroupStorageVirtualSkuRequest.setPsCProEcode(psCProGroup.getEcode());
//                sgGroupStorageVirtualSkuRequest.setPsCProEname(psCProGroup.getEname());
//                sgGroupStorageVirtualSkuRequest.setCpCStoreId(Long.valueOf(splitStoreId[i]));
//                sgGroupStorageVirtualSkuRequest.setCpCStoreEcode(splitStoreEcode[i]);
//                sgGroupStorageVirtualSkuRequest.setCpCStoreEname(splitStoreEname[i]);
//                QueryWrapper<PsCSkugroup> wrapperSkuGroup = new QueryWrapper<PsCSkugroup>();
//                wrapperSkuGroup.eq("PS_C_SKU_GRP_ID", psCSku.getId());
//                List<PsCSkugroup> psCSkugroups = psCSkugroupMapper.selectList(wrapperSkuGroup);
//                //循环遍历List，获取商品子表，组合商品明细档案表的实体对象
//                for (PsCSkugroup psCSkugroup : psCSkugroups) {
//                    SgGroupStorageSkuRequest sgGroupStorageSkuRequest=new SgGroupStorageSkuRequest();
//                    sgGroupStorageSkuRequest.setId(psCSkugroup.getPsCSkuId());
//                    sgGroupStorageSkuRequest.setEcode(psCSkugroup.getPsCSkuEcode());
//                    sgGroupStorageSkuRequest.setNum(psCSkugroup.getNum());
//                    sgGroupStorageSkuRequest.setGroupnum(psCSkugroup.getGroupnum());
//                    sgGroupStorageSkuRequestList.add(sgGroupStorageSkuRequest);
//                }
//                sgGroupStorageVirtualSkuRequest.setSku(sgGroupStorageSkuRequestList);
//                sList.add(sgGroupStorageVirtualSkuRequest);
//            }
//        }
//        sgGroupStorageCalculateRequest.setVirtualSkuList(sList);
//        sgGroupStorageCalculateRequest.setLoginUser(querySession.getUser());
//        try {
//            log.info("计算虚拟条码库存入参=》"+sgGroupStorageCalculateRequest);
//            ValueHolderV14<SgGoupStorageCalculateResult> result = sgGroupStorageCalculateCmd.calculateGroupStorage(sgGroupStorageCalculateRequest);
//            log.debug("计算虚拟条码库存结果："+result);
//            if(result.getData()!=null && result.getCode()==0) {
//                generateLuckyBag(id, result);
//            }
//        } catch (Exception e) {
//            log.debug("调用计算库存服务失败"+e.getMessage());
//        }
//    }

    /**
     * 调用福袋生成服务
     */
//    public void generateLuckyBag(Long id,ValueHolderV14<SgGoupStorageCalculateResult> result){
//        SgGoupStorageCalculateResult sgGoupStorageCalculateResult=result.getData();
//        List<SgGoupStorageGroupSkuResult> sgGoupStorageGroupSkuResults=sgGoupStorageCalculateResult.getGroupSkuResults();
//
//        PsCProGroup psCProGroup=psCProGroupMapper.selectById(id);
//        int groupType=psCProGroup.getGroupType();
//        if(groupType == 1){
//            List<LuckyBagGenerateRequest> luckyBagGenerateRequestList=new ArrayList<LuckyBagGenerateRequest>();
//            for(SgGoupStorageGroupSkuResult sgGoupStorageGroupSkuResult:sgGoupStorageGroupSkuResults){
//                LuckyBagGenerateRequest luckyBagGenerateRequest = new LuckyBagGenerateRequest();
//                luckyBagGenerateRequest.setGoodyBagGroupSku(sgGoupStorageGroupSkuResult.getEcode());
//                luckyBagGenerateRequest.setGroupNum(sgGoupStorageGroupSkuResult.getGroupExtractNum());
//                luckyBagGenerateRequest.setGroupSkuStorage(sgGoupStorageGroupSkuResult.getMinStorage());
//                luckyBagGenerateRequest.setStoreId(sgGoupStorageGroupSkuResult.getCpCStoreId());
//                List<SgGoupStorageSkuResult> sgGoupStorageSkuResults=sgGoupStorageGroupSkuResult.getSkuResults();
//                List<LuckyBagGenerateRealProRequest> luckyBagGenerateRealProRequests=new ArrayList<LuckyBagGenerateRealProRequest>();
//                for(SgGoupStorageSkuResult sgGoupStorageSkuResult:sgGoupStorageSkuResults){
//                    LuckyBagGenerateRealProRequest luckyBagGenerateRealProRequest = new LuckyBagGenerateRealProRequest();
//                    PsCSkugroup psCSkugroup=psCSkugroupMapper.selectSkuGroup(sgGoupStorageSkuResult.getId(),sgGoupStorageGroupSkuResult.getId());
//                    luckyBagGenerateRealProRequest.setPsCProEcode(psCSkugroup.getPsCProEcode());
//                    luckyBagGenerateRealProRequest.setPsCSkuEcode(sgGoupStorageSkuResult.getEcode());
//                    luckyBagGenerateRealProRequest.setGroupnum(psCSkugroup.getGroupnum());
//                    luckyBagGenerateRealProRequest.setStorage(sgGoupStorageSkuResult.getQtyAvaliable());
//                    luckyBagGenerateRealProRequest.setNum(sgGoupStorageSkuResult.getNum());
//                    luckyBagGenerateRealProRequests.add(luckyBagGenerateRealProRequest);
//                }
//                luckyBagGenerateRequest.setLuckyBagGenerateRealProRequestList(luckyBagGenerateRealProRequests);
//                luckyBagGenerateRequestList.add(luckyBagGenerateRequest);
//            }
//            try {
//                log.info("生成福袋入参=》"+luckyBagGenerateRequestList);
//                ValueHolderV14 vh = luckyBagGenerateCmd.generateLuckyBag(luckyBagGenerateRequestList);
//                log.info("福袋生成结果："+vh);
//            } catch (Exception e) {
//                log.debug("调用福袋生成服务失败"+e.getMessage());
//            }
//        }
//    }

    /**
     * 保存前数据校验
     *
     * @param proRequest
     * @param querySession
     * @return
     */
    public String checkData(ProGroupRequest proRequest, Long objid, QuerySession querySession) {
        ValueHolderV14<HashMap<String, String>> retVh = new ValueHolderV14<HashMap<String, String>>();
        String psCStoreIds = proRequest.getCP_C_STORE_IDS();
//        //判断逻辑仓不能为空
//        if (psCStoreIds == null) {
//            return "逻辑仓库不能为空！！";
//        }
        PsCProGroup psCpro = proRequest.getPsCPro();
        String proEcode = psCpro.getEcode();
        if (StringUtils.isBlank(proEcode)) {
            return "商品编码不能为空！！";
        }
        //判断商品名称必填
        if (StringUtils.isBlank(psCpro.getEname())) {
            return "商品名称不能为空！！";
        }
        //判断品牌必填
        if (psCpro.getPsCBrandId() == null) {
            return "品牌不能为空！！";
        }
//        //判断价格不能为空
//        if (psCpro.getPricelist() == null) {
//            return "价格不能为空！！";
//        }
        //获取sku信息
        List<SkuGroupRequest> skuList = proRequest.getSkuGroupRequestList();
        List<String> existSku = new ArrayList();
        if (skuList.size() > 0) {
            for (SkuGroupRequest skuGroupRequest : skuList) {
                //获取商品子表，条码档案表的实体对象
                PsCSku psCSku = skuGroupRequest.getPsCSku();
                //校验虚拟条码必填
                if (StringUtils.isBlank(psCSku.getEcode())) {
                    return "组合条码编码不能为空";
                }
                existSku.add(psCSku.getEcode());
                //校验商品名称不能为空
                if (StringUtils.isBlank(psCSku.getPsCProEname())) {
                    return "组合条码名称不能为空";
                }
                //如果是类型是福袋，校验每组抽取行数不能为空
                if (psCpro.getGroupType() == 1) {
                    if (psCSku.getGroupExtractNum() == null) {
                        return "虚拟条码【" + psCSku.getEcode() + "】下每组抽取行数不能为空";
                    }
                }
                List<PsCSkugroup> psCSkugroupListNew = new ArrayList<PsCSkugroup>();
                List<PsCSkugroup> psCSkugroupListOld = new ArrayList<PsCSkugroup>();
                List<PsCSkugroup> psCSkugroupLists = new ArrayList<PsCSkugroup>();
                if (objid < 0) {
                    psCSkugroupListNew = skuGroupRequest.getPsCSkugroupList();
                    psCSkugroupLists.addAll(psCSkugroupListNew);
                } else {
                    if (psCSku.getId() > 0) {
                        psCSkugroupListOld = psCSkugroupMapper.selectBySkuId(psCSku.getId());
                        psCSkugroupLists.addAll(psCSkugroupListOld);
                    }
                    psCSkugroupListNew = skuGroupRequest.getPsCSkugroupList();

                    psCSkugroupLists.addAll(psCSkugroupListNew);
                }
                if (CollectionUtils.isEmpty(psCSkugroupLists)) {
                    return "虚拟条码【" + psCSku.getEcode() + "】下无明细";
                } else {
                    for (PsCSkugroup psCSkugroup : psCSkugroupLists) {
                        //如果是福袋，判断分组必填
                        if (psCpro.getGroupType() == 1) {
                            if (psCSkugroup.getGroupnum() == null) {
                                return "条码【" + psCSkugroup.getPsCSkuEcode() + "】下的分组不能为空";
                            }
                        }
                    }
                }
            }
            long count = existSku.stream().distinct().count();
            boolean isRepeat = count < existSku.size();
            if (isRepeat) {

                List<String> duplicateElements = getDuplicateElements(existSku.stream());
                return "虚拟条码【" + duplicateElements + "】重复";
            }
            String duplicatedSkuGroup = getDuplicatedSkuGroup(psCpro, skuList);
            if (StringUtils.isNotEmpty(duplicatedSkuGroup)) {
                return duplicatedSkuGroup;
            }
        } else {
            return "组合商品新增页面保存时，明细表未录入！";
        }
        return null;
    }

    /**
     * 判断组合商品是否存在相同的组合条件
     *
     * @param psCpro
     * @param skuGroupRequestList
     * @return
     */
    public String getDuplicatedSkuGroup(PsCProGroup psCpro, List<SkuGroupRequest> skuGroupRequestList) {
        HashMap<String, List<String>> skuMap = new HashMap<>();

        for (SkuGroupRequest skuGroupRequest : skuGroupRequestList) {
            PsCSku psCSku = skuGroupRequest.getPsCSku();
            List<PsCSkugroup> psCSkugroupList = skuGroupRequest.getPsCSkugroupList();
//            if(Objects.isNull(psCpro.getPsCProId())){ //条码
//                //排序
//                List<String> sortSkuGroupList = psCSkugroupList.stream().sorted(Comparator.comparing(PsCSkugroup::getPsCSkuEcode))
//                        .map(item -> item.getPsCSkuEcode() + "_" + item.getNum() + "_" + item.getRatio()).collect(Collectors.toList());
//                skuMap.put(psCSku.getEcode(),sortSkuGroupList);
//            }else { //平台条码
//                //排序
//                List<String> sortSkuGroupList = psCSkugroupList.stream().sorted(Comparator.comparing(PsCSkugroup::getSkuId))
//                        .map(item -> item.getSkuId() + "_" + item.getNum() + "_" + item.getRatio()).collect(Collectors.toList());
//                skuMap.put(psCSku.getEcode(),sortSkuGroupList);
//            }
        }
        //PS:如果组合商品存在相同的组合条件：是否拆分条件、店铺条件、商品条件、组合数量、占比相同，系统提示有相同的组合，不能保存；
        //找出组合商品相同的数据
        try {
            skuMap.forEach((k, v) -> {
                skuMap.forEach((k1, v1) -> {
                    if (!k.equals(k1) && v.size() == v1.size()) {
                        List<String> diff = v.stream().filter(kn -> !v1.contains(kn)).collect(Collectors.toList());
                        if (diff.size() == 0) {
                            throw new NDSException("组合条码编码" + k + "有相同的组合，不能保存！");
                        }
                    }
                });
            });
        } catch (NDSException ndsException) {
            return ndsException.getMessage();
        } catch (Exception e) {
            log.error("##SkuGroupSaveService##getDuplicatedSkuGroup##Error", e);
        }
        return null;
    }

    /**
     * 查询List中重复的值
     *
     * @param stream
     * @param <T>
     * @return
     */
    public static <T> List<T> getDuplicateElements(Stream<T> stream) {
        return stream
                .collect(Collectors.toMap(e -> e, e -> 1, (a, b) -> a + b)) // 获得元素出现频率的 Map，键为元素，值为元素出现的次数
                .entrySet().stream() // Set<Entry>转换为Stream<Entry>
                .filter(entry -> entry.getValue() > 1) // 过滤出元素出现次数大于 1 的 entry
                .map(entry -> entry.getKey()) // 获得 entry 的键（重复元素）对应的 Stream
                .collect(Collectors.toList()); // 转化为 List
    }

    /**
     * 校验组合条码明细占比
     *
     * @param skuGroupRequestList
     * @return
     */
    public String checkSkuGroupRatio(List<SkuGroupRequest> skuGroupRequestList) {
        if (CollectionUtils.isNotEmpty(skuGroupRequestList)) {
            for (SkuGroupRequest skuGroupRequest : skuGroupRequestList) {
                PsCSku psCSku = skuGroupRequest.getPsCSku();
                BigDecimal totalRatio = skuGroupRequest.getPsCSkugroupList().stream().map(PsCSkugroup::getRatio).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (totalRatio.compareTo(new BigDecimal("100")) != 0) {
                    return "编码【" + psCSku.getEcode() + "】总占比和不等于100%，不允许保存";
                }
            }
        }
        return null;
    }

    /**
     * 保存前逻辑控制
     *
     * @param proRequest
     * @return
     */
    public String checkDataUnique(ProGroupRequest proRequest, Long objid, QuerySession querySession) {
        // 福袋导入条数限制默认值
        int limitNum = 500;
        try {
            CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
            Boolean hasKey = redisTemplate.hasKey(BasicConstants.LIMITED_NUMBER_OF_LUCKY_BAGS_KEY);
            if (Objects.nonNull(hasKey) && hasKey) {
                String num = redisTemplate.opsForValue().get(BasicConstants.LIMITED_NUMBER_OF_LUCKY_BAGS_KEY);
                if (Objects.nonNull(num)) {
                    limitNum = Integer.parseInt(num);
                }
            }
        } catch (Exception ex) {
            log.error("查询Redis，获取系统参数【福袋导入条数限制】异常");
        }
        /*String psCStoreIds = proRequest.getCP_C_STORE_IDS();
        List<Long> ids = new ArrayList<>();
        String[] d = psCStoreIds.split(",");
        long[] strArrNum = (long[]) ConvertUtils.convert(d, long.class);
        for (int i = 0; i < strArrNum.length; i++) {
            Collections.addAll(ids, strArrNum[i]);
        }*/
        //获取商品主表，商品档案的实体对象
        PsCProGroup psCpro = proRequest.getPsCPro();
        if (objid < 0) {
            //map封装查询条件
            HashMap<String, Object> proHashMap = new HashMap<>();
            proHashMap.put("ECODE", psCpro.getEcode());
            List<PsCPro> psCPros = psCProMapper.selectByMap(proHashMap);
            //如果不为空，说明该商品编码已经存在，给予提示
            if (CollectionUtils.isNotEmpty(psCPros)) {
                return "商品编码【" + psCpro.getEcode() + "】已经存在，不允许保存";
            }
        }
        //获取sku信息
        List<SkuGroupRequest> skuGroupRequestList = proRequest.getSkuGroupRequestList();
        if (CollectionUtils.isNotEmpty(skuGroupRequestList)) {
            for (SkuGroupRequest skuGroupRequest : skuGroupRequestList) {
                //获取商品子表psCSku信息，条码档案表的实体对象
                PsCSku psCSku = skuGroupRequest.getPsCSku();
                if (objid > 0) {
                    if (psCSku.getId() < 0) {
                        //map封装查询条件
                        HashMap<String, Object> skuHashMap = new HashMap<>();
                        skuHashMap.put("ECODE", psCSku.getEcode());
                        List<PsCSku> psCSkuList = psCSkuMapper.selectByMap(skuHashMap);
                        //如果不为空，说明该虚拟条码已经存在，给予提示
                        if (CollectionUtils.isNotEmpty(psCSkuList)) {
                            return "虚拟条码【" + psCSku.getEcode() + "】已经存在【" + psCSkuList.get(0).getPsCProEname() + "】中，不允许保存";
                        }
                    }
                } else {
                    //map封装查询条件
                    HashMap<String, Object> skuHashMap = new HashMap<>();
                    skuHashMap.put("ECODE", psCSku.getEcode());
                    List<PsCSku> psCSkuList = psCSkuMapper.selectByMap(skuHashMap);
                    //如果不为空，说明该虚拟条码已经存在，给予提示
                    if (CollectionUtils.isNotEmpty(psCSkuList)) {
                        return "虚拟条码【" + psCSku.getEcode() + "】已经存在【" + psCSkuList.get(0).getPsCProEname() + "】中，不允许保存";
                    }
                }
                //获取skuGroup信息
                List<PsCSkugroup> psCSkugroupList = new ArrayList<PsCSkugroup>();
                if (objid < 0) {
                    psCSkugroupList = skuGroupRequest.getPsCSkugroupList();
                } else {
                    if (psCSku.getId() > 0 && skuGroupRequest.getPsCSkugroupList().size() < 1) {
                        psCSkugroupList = psCSkugroupMapper.selectBySkuId(psCSku.getId());
                    } else {
                        psCSkugroupList = skuGroupRequest.getPsCSkugroupList();
                    }
                }
                if (CollectionUtils.isNotEmpty(psCSkugroupList)) {
                    //普通商品  校验一个虚拟条码下对多只能由30条sku
                    if (psCpro.getGroupType() == 2) {
                        if (skuGroupRequestList.size() > 30) {
                            return "虚拟条码【" + psCSku.getEcode() + "】下最多存储30条sku";
                        }
                    } else if (psCpro.getGroupType() == 1) {//福袋
                        //每组抽取行数
                        int groupExtractNum = psCSku.getGroupExtractNum().intValue();
                        //分组
                        List<Integer> groupNumList = new ArrayList<Integer>();
                        //实际条码
                        List<String> psCSkuEcodeList = new ArrayList<String>();
                        Map<Integer, Integer> map = new HashMap<>();
                        for (PsCSkugroup psCSkugroup : psCSkugroupList) {
                            Integer groupnum = psCSkugroup.getGroupnum();
                            String psCSkuEcode = psCSkugroup.getPsCSkuEcode();
                            /*List<PsCSkugroup> psCSkugroups = psCSkugroupMapper.selectSkuGroupBySkuEcode(psCSkuEcode);
                            if (CollectionUtils.isNotEmpty(psCSkugroups)) {
                                List<String> proEname = new ArrayList<String>();
                                for (PsCSkugroup skugroup : psCSkugroups) {
                                    PsCProGroup psCProGroup = psCProGroupMapper.selectById(skugroup.getPsCProId());
                                    proEname.add(psCProGroup.getEname());
                                }
                                return "实际条码【" + psCSkuEcode + "】已经存在福袋" + proEname + "中";
                            }*/
                            groupNumList.add(groupnum);
                            psCSkuEcodeList.add(psCSkuEcode);
                        }


                        for (Integer list : groupNumList) {
                            Integer i = 1; //定义一个计数器，用来记录重复数据的个数
                            if (map.get(list) != null) {
                                i = map.get(list) + 1;
                            }
                            map.put(list, i);
                        }
                        if (map.size() > 10) {
                            return "虚拟条码【" + psCSku.getEcode() + "】下最多允许10个分组";
                        }
                        for (Map.Entry<Integer, Integer> entry : map.entrySet()) {
                            if (entry.getValue() < groupExtractNum) {
                                return "虚拟条码【" + psCSku.getEcode() + "】明细下抽取的总行数小于每组抽取的行数";
                            }
                            if (entry.getValue() > limitNum) {
                                return String.format("虚拟条码【%s】下分组【%s】最多允许【%s】条sku",
                                        psCSku.getEcode(), entry.getKey(), limitNum);
                            }
                        }
                        Set<String> s = new HashSet<String>();
                        for (String str : psCSkuEcodeList) {
                            boolean b = s.add(str);
                            if (!b) {
                                return "虚拟条码【" + psCSku.getEcode() + "】明细下条码【" + str + "】重复";
                            }
                        }
                        //计算实际条码在逻辑仓下是否有库存
//                        String message = calStorage(psCSkugroupList, skuGroupRequest, ids,objid, querySession);
//                        if (message != null) {
//                            return message;
//                        }
                    }
                } else {
                    return "虚拟条码【" + psCSku.getEcode() + "】下无明细，不允许保存";
                }
            }
            String checkStr = checkSkuGroupRatio(skuGroupRequestList);
            if (StringUtils.isNotEmpty(checkStr)) {
                return checkStr;
            }
        } else {
            return "商品【" + psCpro.getEname() + "】下无明细，不允许保存";
        }
        return null;
    }

    /**
     * 计算逻辑仓对应的条码库存
     * @param psCSkugroupList
     * @param skuGroupRequest
     * @param ids
     * @param objid
     * @param querySession
     * @return
     */
//    public String calStorage(List<PsCSkugroup> psCSkugroupList,SkuGroupRequest skuGroupRequest,List<Long> ids,Long objid,QuerySession querySession){
//        List<String> skuEcodeList=new ArrayList<String>();
//
//        if(objid<0){
//            skuEcodeList=psCSkugroupList.stream().map(PsCSkugroup::getPsCSkuEcode).collect(toList());
//        }else{
//            if(skuGroupRequest.getPsCSkugroupList().size()>psCSkugroupList.size()){
//                skuEcodeList=skuGroupRequest.getPsCSkugroupList().stream().map(PsCSkugroup::getPsCSkuEcode).collect(toList());
//            }else{
//                skuEcodeList=psCSkugroupList.stream().map(PsCSkugroup::getPsCSkuEcode).collect(toList());
//            }
//        }
//        for(Long id:ids){
//            List<Integer> storeId=new ArrayList<Integer>();
//            storeId.add(id.intValue());
//            List<CpCStore> cpCStores=null;
//            try {
//                cpCStores = rpcCpService.queryStoreInfoByIds(storeId);
//                log.info("add getCPCSoreInfo=>"+cpCStores);
//            } catch (Exception e) {
//                log.debug("add getCPCSoreInfo error=>"+e.getMessage());
//            }
//            SgStoreStorageQueryRequest sgStoreStorageQueryRequest=new SgStoreStorageQueryRequest();
//            sgStoreStorageQueryRequest.setStoreId(id);
//            ValueHolderV14<List<SgBStorage>> sgBStorageList=new ValueHolderV14<List<SgBStorage>>();
//            List<SgBStorage> sgBStorages=new ArrayList<SgBStorage>();
//            //无库存信息
//            List<String> noStock=new ArrayList<String>();
//            try {
//                int itemNum = skuEcodeList.size();
//                int pageSize = 500;
//                int page = itemNum / pageSize;
//                if (itemNum % pageSize != 0) {
//                    page++;
//                }
//                for (int i = 0; i < page; i++) {
//                    int startIndex = i * pageSize;
//                    int endIndex = (i + 1) * pageSize;
//                    List<String> skuEcodes = skuEcodeList.subList(startIndex, endIndex < itemNum ? endIndex : itemNum);
//                    sgStoreStorageQueryRequest.setSkuEcodes(skuEcodes);
//                    sgBStorageList=sgStorageQueryCmd.queryStoreStorage(sgStoreStorageQueryRequest,querySession.getUser());
//                    if(sgBStorageList.getData().size()<=0){//如果返回的为空，记录这一批条码在该逻辑仓下面无库存信息
//                        noStock=skuEcodeList.subList(startIndex, endIndex < itemNum ? endIndex : itemNum);
//                    }else {
//                        sgBStorages.addAll(sgBStorageList.getData());
//                    }
//                }
//                //部分无库存
//                List<String> partNoStock=new ArrayList<String>();
//                if(CollectionUtils.isNotEmpty(sgBStorages)){
//                    //有库存信息
//                    List<String> haveStock=sgBStorages.stream().map(SgBStorage::getPsCSkuEcode).collect(toList());
//                    //部分有库存=所有条码-有库存
//                    partNoStock = skuEcodeList.stream().filter(item -> !haveStock.contains(item)).collect(toList());
//                    if(haveStock.size() != skuEcodeList.size()){//说明所传入的都有库存信息
//                        return partNoStock.toString()+"在逻辑仓:【"+cpCStores.get(0).getEname()+"】下无库存信息！！！";
//                    }
//                }else{
//                    return noStock.toString()+"在逻辑仓:【"+cpCStores.get(0).getEname()+"】下无库存信息！！！";
//                }
//            } catch (Exception e) {
//                log.error("计算库存异常:" + e.getMessage(), e);
//            }
//        }
//        return null;
//    }

    /**
     * 作废引用商品款号的组合商品
     *
     * @param codes
     * @return
     */
    public ValueHolderV14 voidProGroup(List<String> codes) {
        log.info(LogUtil.format("SkuGroupSaveService.voidProGroup,param:{}",
                "SkuGroupSaveService.voidProGroup"), JSON.toJSONString(codes));
        ValueHolderV14 v14 = ValueHolderV14Utils.getSuccessValueHolder("作废引用商品款号的组合商品成功");
        if (CollectionUtils.isEmpty(codes)) {
            v14.setCode(ResultCode.SUCCESS);
            v14.setMessage("商品编码为空");
            return v14;
        }
        List<PsCSkugroup> skugroupList = psCSkugroupMapper.selectList(new LambdaQueryWrapper<PsCSkugroup>()
                .in(PsCSkugroup::getPsCProEcode, codes));
        if (CollectionUtils.isEmpty(skugroupList)) {
            return v14;
        }
        Set<Long> proGroupIds = skugroupList.stream().map(PsCSkugroup::getPsCProId)
                .filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(proGroupIds)) {
            return v14;
        }
        List<PsCProGroup> psCProGroups = psCProGroupMapper.selectList(new LambdaQueryWrapper<PsCProGroup>()
                .in(PsCProGroup::getId, proGroupIds));
        if (CollectionUtils.isEmpty(psCProGroups)) {
            return v14;
        }
        Set<String> proGroupCodes = psCProGroups.stream().map(PsCProGroup::getEcode)
                .filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(proGroupCodes)) {
            PsCPro updatePro = new PsCPro();
            updatePro.setIsactive("N");
            psCProMapper.update(updatePro, new UpdateWrapper<PsCPro>().in("ECODE", proGroupCodes));
            PsCSku updateSku = new PsCSku();
            updateSku.setIsactive("N");
            psCSkuMapper.update(updateSku, new UpdateWrapper<PsCSku>().in("ECODE", proGroupCodes));
        }
        return v14;
    }

    /**
     * 组合商品比例重新计算
     *
     * @return
     */
    public ValueHolderV14 refreshCalculate() {
        CusRedisTemplate<String, String> strRedisTemplate = RedisOpsUtil.getStrRedisTemplate();
        Set<String> records = strRedisTemplate.boundSetOps("ps:sku:group:refresh").members();
        log.info(LogUtil.format("组合商品比例重新计算需要计算数据：{}", "refreshCalculate"), records);

        if (org.apache.commons.collections4.CollectionUtils.isEmpty(records)) {
            return ValueHolderV14Utils.getSuccessValueHolder("没有需要重新计算的组合商品");
        }

        List<String> skuKeys = Lists.newArrayList(records);
        //排序
        Collections.sort(skuKeys);

        for (String record : skuKeys) {
            try {

                String value = strRedisTemplate.opsForValue().get(record);
                List<String> skuCodes = JSONObject.parseArray(value, String.class);
                if (org.apache.commons.collections4.CollectionUtils.isEmpty(skuCodes)) {
                    continue;
                }
                log.info(LogUtil.format("组合商品比例重新计算需要计算数据 record:{},skuCodes：{}", "refreshCalculate"), record, skuCodes);

                //查询skucode对应的组合商品
                List<PsCSkugroup> skugroupList = psCSkugroupMapper.selectList(new LambdaQueryWrapper<PsCSkugroup>()
                        .in(PsCSkugroup::getPsCSkuEcode, skuCodes));
                if (CollectionUtils.isEmpty(skugroupList)) {
                    //没有组合商品
                    removeCache(strRedisTemplate, record);
                    continue;
                }

                List<Long> proIds = skugroupList.stream().distinct().map(PsCSkugroup::getPsCProId).collect(Collectors.toList());
                List<PsCSkugroup> cSkugroups = psCSkugroupMapper.selectList(new LambdaQueryWrapper<PsCSkugroup>()
                        .in(PsCSkugroup::getPsCProId, proIds));

                Map<Long, List<PsCSkugroup>> groupMap = cSkugroups.stream().collect(Collectors.groupingBy(PsCSkugroup::getPsCProId));
                log.info(LogUtil.format("组合商品比例重新计算需要计算数据groupMap：{}", "refreshCalculate"), JSON.toJSONString(groupMap));

                //proId,pros
                for (Map.Entry<Long, List<PsCSkugroup>> entry : groupMap.entrySet()) {
                    List<PsCSkugroup> skugroups = entry.getValue();
                    skugroups.sort((o1, o2) -> (o2.getIsGift().compareTo(o1.getIsGift())));

                    //查询款号信息，获取价格
                    if (CollectionUtils.isEmpty(skugroups)) {
                        continue;
                    }

                    if (skugroups.size() == 1) {
                        continue;
                    }

                    List<String> skus = skugroups.stream().map(PsCSkugroup::getPsCSkuEcode).collect(Collectors.toList());

                    List<PsCPro> proList = psCProMapper.selectList(new LambdaQueryWrapper<PsCPro>().in(PsCPro::getEcode, skus));
                    if (CollectionUtils.isEmpty(proList)) {
                        continue;
                    }

                    Map<String, PsCPro> proMap = proList.stream().collect(Collectors.toMap(PsCPro::getEcode, x -> x, (a, b) -> a));

                    //计算总金额
                    BigDecimal totalAmt = BigDecimal.ZERO;
                    for (PsCSkugroup skugroup : skugroups) {
                        String psCSkuEcode = skugroup.getPsCSkuEcode();
                        PsCPro psCPro = proMap.get(psCSkuEcode);
                        if (psCPro == null) {
                            continue;
                        }
                        BigDecimal amt = Objects.isNull(psCPro.getPriceSaleList()) ? psCPro.getPriceCostList() : psCPro.getPriceSaleList();
                        if (Objects.isNull(amt)) {
                            continue;
                        }
                        //赠品当0金额处理
                        if ("1".equals(skugroup.getIsGift())) {
                            amt = BigDecimal.ZERO;
                        }
                        BigDecimal num = skugroup.getNum();
                        totalAmt = totalAmt.add(amt.multiply(num));
                    }

                    if (totalAmt.compareTo(BigDecimal.ZERO) == 0) {
                        log.info(LogUtil.format("组合商品比例重新计算总金额为0过滤掉 skugroups：{}", "refreshCalculate"), JSON.toJSONString(skugroups));
                        continue;
                    }

                    //计算占比
                    List<PsCSkugroup> updates = Lists.newArrayList();
                    BigDecimal rate = BigDecimal.ZERO;
                    for (int i = 0; i < skugroups.size(); i++) {
                        PsCSkugroup skugroup = skugroups.get(i);
                        String psCSkuEcode = skugroup.getPsCSkuEcode();
                        PsCPro psCPro = proMap.get(psCSkuEcode);
                        if (psCPro == null) {
                            continue;
                        }
                        BigDecimal amt = Objects.isNull(psCPro.getPriceSaleList()) ? psCPro.getPriceCostList() : psCPro.getPriceSaleList();
                        if (Objects.isNull(amt)) {
                            continue;
                        }

                        //赠品当0金额处理
                        if ("1".equals(skugroup.getIsGift())) {
                            amt = BigDecimal.ZERO;
                        }

                        if (i == skugroups.size() - 1) {
                            PsCSkugroup update = new PsCSkugroup();
                            update.setId(skugroup.getId());
                            update.setModifieddate(new Date());
                            BigDecimal lastAmt = BigDecimal.valueOf(1).subtract(rate);
                            update.setRatio(new BigDecimal("100").multiply(lastAmt));
                            updates.add(update);
                            continue;
                        }

                        BigDecimal rateAmt = (skugroup.getNum().multiply(amt)).divide(totalAmt, 4, RoundingMode.DOWN);
                        rate = rate.add(rateAmt);

                        PsCSkugroup update = new PsCSkugroup();
                        update.setId(skugroup.getId());
                        update.setModifieddate(new Date());
                        update.setRatio(new BigDecimal("100").multiply(rateAmt));
                        updates.add(update);
                    }
                    if (CollectionUtils.isNotEmpty(updates)) {
                        log.info(LogUtil.format("组合商品比例重新计算总金额为0过滤掉 updates：{}", "refreshCalculate"), JSON.toJSONString(updates));
                        for (PsCSkugroup update : updates) {
                            psCSkugroupMapper.updateById(update);
                        }
                    }
                }

                removeCache(strRedisTemplate, record);

                log.info(LogUtil.format("组合商品比例重新计算结束 record：{}", "refreshCalculate"), record);
            } catch (Exception e) {
                log.error(LogUtil.format("组合商品比例重新计算需要计算数据异常,record：{}", "refreshCalculate"), record, e);
            }
        }

        return ValueHolderV14Utils.getSuccessValueHolder("success");
    }

    private void removeCache(CusRedisTemplate<String, String> strRedisTemplate, String record) {
        strRedisTemplate.delete(record);
        strRedisTemplate.boundSetOps("ps:sku:group:refresh").remove(record);
    }
}