package com.jackrain.nea.psext.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.psext.model.table.PsCProdimItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface PsCProdimItemMapper extends ExtentionMapper<PsCProdimItem> {
    @Select("SELECT * FROM `ps_c_prodim_item` n LEFT JOIN ps_c_prodim m on n.ps_c_prodim_id = m.id where m.AD_PROCOLUMN_NAME = #{type} and n.ECODE = #{sapEcode} and n.isactive='Y' ")
    PsCProdimItem findProdimItemByEcode(@Param("type") String type, @Param("sapEcode") String sapEcode);

    @Select("select count(1) from ps_c_prodim_item where PS_C_PRODIM_ID=#{psCProDimId} and ECODE=#{ecode} and isactive='Y'")
    Integer proDimCount(@Param("psCProDimId") Long psCProDimId, @Param("ecode") String ecode);

    @Select("select id from ps_c_prodim_item where PS_C_PRODIM_ID=#{psCProDimId} and ECODE=#{ecode} and isactive='Y'")
    Long proDimItemId(@Param("psCProDimId") Long psCProDimId, @Param("ecode") String ecode);

    @Select("select a.id from ps_c_prodim_item a,ps_c_prodim b where a.ps_c_prodim_id=b.id and  a.ename=#{ename} and b.ecode=#{ecode} and a.isactive='Y'")
    Long valueId(@Param("ename") String ename, @Param("ecode") String ecode);

    @Select("select a.id from ps_c_prodim_item a,ps_c_prodim b where a.ps_c_prodim_id=b.id and  a.ecode=#{itemCode} and b.ecode=#{ecode} and a.isactive='Y'")
    Long valueIdByCode(@Param("itemCode") String itemCode, @Param("ecode") String ecode);

    @Select("select ecode from ps_c_prodim_item where id=#{id}")
    String queryEcode(@Param("id") Long id);

}