package com.jackrain.nea.psext.utils;


import com.jackrain.nea.exception.QueryException;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.util.DateUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;


/**
 * Created with IntelliJ IDEA.
 * @Author: jg.zhan
 * @Date:  2022/4/27 14:29
 * @Description:  生成商品编码工具类
 */
@Slf4j
@Component
@Data
public class SkuEcodeUtils {

    public static String getSkuCode(String tableName) throws RuntimeException, QueryException {
        String seq = "code_sn_" + tableName.toUpperCase();
        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
        Boolean isExist = redisTemplate.opsForHash().hasKey("serialNumber", seq);
        String dateStr = DateUtil.dateNumberFormatter.format(new Date());
        if (!isExist) {
            redisTemplate.opsForHash().putIfAbsent("serialNumber", seq, "0");
            redisTemplate.opsForHash().putIfAbsent("serialNumber", "date", dateStr);
        }

        Object curDateOjb = redisTemplate.opsForHash().get("serialNumber", "date");
        if(Objects.nonNull(curDateOjb)){
            String curDate = (String)curDateOjb;
            if(!dateStr.equals(curDate)){
                redisTemplate.opsForHash().putIfAbsent("serialNumber", seq, "0");
            }
        }

        Long number = redisTemplate.opsForHash().increment("serialNumber", seq, 1L);
        redisTemplate.opsForHash().putIfAbsent("serialNumber", "date", dateStr);
        return dateStr+String.format("%04d",number);
    }

    /**
     *
     * @param tableName
     * @param number
     * @return
     * @throws RuntimeException
     * @throws QueryException
     */
    public static String getBillNo(String tableName,int number) throws RuntimeException, QueryException {
        if (StringUtils.isNotEmpty(tableName)){
            String seq = "code_sn_" + tableName.toUpperCase();
            CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
            Boolean isExist = redisTemplate.opsForHash().hasKey("serialNumber", seq);
            String dateStr = DateUtil.dateNumberFormatter.format(new Date());
            if (!isExist) {
                redisTemplate.opsForHash().putIfAbsent("serialNumber", seq, "0");
                redisTemplate.opsForHash().putIfAbsent("serialNumber", "date", dateStr);
            }

            Object curDateOjb = redisTemplate.opsForHash().get("serialNumber", "date");
            if(Objects.nonNull(curDateOjb)){
                String curDate = (String)curDateOjb;
                if(!dateStr.equals(curDate)){
                    redisTemplate.opsForHash().putIfAbsent("serialNumber", seq, "0");
                }
            }

            Long serialNumber = redisTemplate.opsForHash().increment("serialNumber", seq, 1L);
            redisTemplate.opsForHash().putIfAbsent("serialNumber", "date", dateStr);
            return dateStr+String.format("%0"+number+"d",serialNumber);
        }
       return null;
    }
}
