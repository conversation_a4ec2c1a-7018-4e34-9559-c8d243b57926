package com.jackrain.nea.psext.manager;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import com.jackrain.nea.web.query.QuerySessionImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;

@Slf4j
@Component
public class QuerySessionManager {


    /**
     * 封装新增保存参数
     * @param id
     * @param mainJo
     * @param tableName
     * @param itemTableName
     * @param itemArray
     * @return
     */
    public QuerySession fixFieldValue(Long id, JSONObject mainJo, String tableName, String itemTableName, JSONArray itemArray){
        //获取root用户信息
        User user= R3SystemUserResource.getSystemRootUser();
        log.info("QuerySession user:{}",user);
        QuerySession session = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("save", new HashMap(16));
        JSONObject param = new JSONObject();
        JSONObject fixC = new JSONObject();
        //主表字段
        fixC.put(tableName,mainJo);
        //子表表名为空不传
        if (!StringUtils.isEmpty(itemTableName)){
            //明细字段
            fixC.put(itemTableName,itemArray);
        }
        param.put("fixcolumn", fixC);
        param.put("objid",id);
        param.put("table", tableName);
        log.info("fixFieldValue param:{}",param);
        event.put("param", param);
        session.setEvent(event);
        return session;
    }
}