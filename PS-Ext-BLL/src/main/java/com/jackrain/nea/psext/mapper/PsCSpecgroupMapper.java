package com.jackrain.nea.psext.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.psext.model.table.PsCSpecgroup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * @Author: anna
 * @CreateDate: 2020/7/2$ 10:15$
 * @Description: 规格表(包含颜色组与尺寸组)
 */
@Mapper
public interface PsCSpecgroupMapper extends ExtentionMapper<PsCSpecgroup> {

    @Select("select id from PS_C_SPECGROUP where ecode='RYYTN' and isactive='Y' and PS_C_SPEC_ID=1")
    Long colorId();


}
