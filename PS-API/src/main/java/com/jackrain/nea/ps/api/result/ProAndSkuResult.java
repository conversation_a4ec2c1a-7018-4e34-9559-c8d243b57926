package com.jackrain.nea.ps.api.result;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @createDate 2020/8/17 10:37 上午
 **/
@Data
public class ProAndSkuResult implements Serializable {

    /**
     * skuId
     */
    @JSONField(name = "SKU_ID")
    private Long skuId;
    /**
     * sku编码
     */
    @JSONField(name = "PS_C_SKU_ECODE")
    private String skuEcode;
    /**
     * 商品ID
     */
    @J<PERSON>NField(name = "PRO_ID")
    private Long proId;
    /**
     * 商品编码
     */
    @JSONField(name = "PRO_ECODE")
    private String proEcode;
    /**
     * 商品名称
     */
    @JSONField(name = "PRO_ENAME")
    private String proEname;
    /**
     * 颜色ID
     */
    @JSONField(name = "COLOR_ID")
    private Long colorId;
    /**
     * 颜色编码
     */
    @JSONField(name = "COLOR_ECODE")
    private String colorEcode;
    /**
     * 颜色名称
     */
    @JSONField(name = "COLOR_ENAME")
    private String colorEname;
    /**
     * 尺寸ID
     */
    @JSONField(name = "SIZE_ID")
    private Long sizeId;
    /**
     * 尺寸编码
     */
    @JSONField(name = "SIZE_ECODE")
    private String sizeEcode;
    /**
     * 尺寸名称
     */
    @JSONField(name = "SIZE_ENAME")
    private String sizeEname;

    /**
     * 吊牌价
     */
    @JSONField(name = "PRICELIST")
    private String pricelist;

    /**
     * 国标码
     */
    @JSONField(name = "GB_CODE")
    private String gbCode;
    /**
     * 查询条件拼接
     */
    @JSONField(name = "POS_KEY")
    private String posKey;

}


