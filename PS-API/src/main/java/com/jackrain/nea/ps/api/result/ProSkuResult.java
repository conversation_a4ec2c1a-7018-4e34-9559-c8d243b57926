package com.jackrain.nea.ps.api.result;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @author: pankai
 * @since: 2019-01-15
 * create at : 2019-01-15 13:53
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProSkuResult implements Serializable {

    private List<PsCProSkuResult> proSkuList;


}
