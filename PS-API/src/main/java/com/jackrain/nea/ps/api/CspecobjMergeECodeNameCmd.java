package com.jackrain.nea.ps.api;

import com.jackrain.nea.sys.Command;
import com.jackrain.nea.sys.domain.ValueHolderV14;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2018/03/29
 */
public interface CspecobjMergeECodeNameCmd extends Command {
    /**
     * 根据id查询颜色信息或者尺寸信息
     * @param specId
     * @return
     */
    ValueHolderV14<HashMap<String, Object>> querySizeOrColorInfoById(Long specId);
}
