package com.jackrain.nea.ps.api.result;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

@Data
public class PsCProSkuResult extends BaseModel {
    @JSONField(name = "ID")
    private Long id;

    @JSONField(name = "CP_C_DISTRIB_ID")
    private Long cpCDistribId;

    @JSONField(name = "PS_C_PRO_ID")
    private Long psCProId;

    @JSONField(name = "PS_C_PRO_ECODE")
    private String psCProEcode;

    @JSONField(name = "PS_C_PRO_ENAME")
    private String psCProEname;

    @JSONField(name = "QTY_LOWSTOCK")
    private BigDecimal qtyLowstock;

    @J<PERSON>NField(name = "QTY_SAFENUM")
    private BigDecimal qtySafenum;

    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    @J<PERSON><PERSON>ield(name = "LENGTH")
    private BigDecimal length;

    @JSONField(name = "WIDTH")
    private BigDecimal width;

    @JSONField(name = "HEIGHT")
    private BigDecimal height;

    @JSONField(name = "WEIGHT")
    private BigDecimal weight;

    @JSONField(name = "ECODE")
    private String skuEcode;

    @JSONField(name = "FORCODE1")
    private String forcode1;

    @JSONField(name = "FORCODE2")
    private String forcode2;

    @JSONField(name = "FORCODE3")
    private String forcode3;

    @JSONField(name = "GBCODE")
    private String gbcode;

    @JSONField(name = "MAINCOLOR")
    private Long maincolor;

    @JSONField(name = "FABCOLOR")
    private String fabcolor;

    @JSONField(name = "STOPPUR")
    private Long stoppur;

    @JSONField(name = "STOPREPLEN")
    private Long stopreplen;

    @JSONField(name = "SYNC")
    private Long sync;

    @JSONField(name = "DISPALYSTANDARD")
    private Long dispalystandard;

    @JSONField(name = "PREAVGDAILY")
    private BigDecimal preavgdaily;

    @JSONField(name = "PRONATURE")
    private Long pronature;

    @JSONField(name = "PURCHASEMODE")
    private Long purchasemode;

    @JSONField(name = "REMARK")
    private String remark;

    @JSONField(name = "PS_C_SKU_ID")
    private Long psCSkuId;

    @JSONField(name = "PS_C_SPEC1OBJ_ID")
    private Long psCSpec1objId;

    @JSONField(name = "PS_C_SPEC2OBJ_ID")
    private Long psCSpec2objId;

    @JSONField(name = "PS_C_SPEC3OBJ_ID")
    private Long psCSpec3objId;

    @JSONField(name = "PS_C_SPEC4OBJ_ID")
    private Long psCSpec4objId;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "PS_C_BRAND_ID")
    private Long psCBrandId;

    @JSONField(name = "DISCONTID")
    private Long discontid;

    @JSONField(name = "DISCONTNAME")
    private String discontname;

    @JSONField(name = "DISCONTENAME")
    private String discontename;

    @JSONField(name = "DISCONTDATE")
    private Date discontdate;

    @JSONField(name = "ENABLEID")
    private Long enableid;

    @JSONField(name = "ENABLENAME")
    private String enablename;

    @JSONField(name = "ENABLEENAME")
    private String enableename;

    @JSONField(name = "ENABLEDATE")
    private Date enabledate;

    @JSONField(name = "IS_GROUP")
    private String isGroup;

    @JSONField(name = "GROUP_EXTRACT_NUM")
    private Integer groupExtractNum;

    @JSONField(name = "PS_C_PRORULE_ID")
    private Long psCProruleId;

    @JSONField(name = "PS_C_SKURULE_ID")
    private Long psCSkuruleId;

    @JSONField(name = "ENAME")
    private String skuEname;

    @JSONField(name = "DATEMARKET")
    private Long datemarket;

    @JSONField(name = "CP_C_SUPPLIER_ID")
    private Long cpCSupplierId;

    @JSONField(name = "PS_C_SHAPEGROUP_ID")
    private Long psCShapegroupId;

    @JSONField(name = "FACTORYCODE")
    private String factorycode;

    @JSONField(name = "FABDESC")
    private String fabdesc;

    @JSONField(name = "TAGSPEC")
    private String tagspec;

    @JSONField(name = "CP_C_WAREHOUSE_ID")
    private Long cpCWarehouseId;

    @JSONField(name = "PROYEAR")
    private Long proyear;

    @JSONField(name = "LARGECLASS")
    private Long largeclass;

    @JSONField(name = "SEX")
    private Long sex;

    @JSONField(name = "SUPBRAND")
    private Long supbrand;

    @JSONField(name = "PROSEA")
    private Long prosea;

    @JSONField(name = "PROBAND")
    private Long proband;

    @JSONField(name = "PROMONTH")
    private Long promonth;

    @JSONField(name = "PROSOURCE")
    private Long prosource;

    @JSONField(name = "PROMOTIONTYPE")
    private Long promotiontype;

    @JSONField(name = "PROLINE")
    private Long proline;

    @JSONField(name = "POPULAR")
    private Long popular;

    @JSONField(name = "COMPOSITION")
    private Long composition;

    @JSONField(name = "STYLE")
    private Long style;

    @JSONField(name = "DETAILS")
    private Long details;

    @JSONField(name = "PRICEBAND")
    private Long priceband;

    @JSONField(name = "SERIES")
    private Long series;

    @JSONField(name = "PURNATURE")
    private Long purnature;

    @JSONField(name = "BUYER")
    private Long buyer;

    @JSONField(name = "FOLLOWER")
    private Long follower;

    @JSONField(name = "NEWPROER")
    private Long newproer;

    @JSONField(name = "MDLARGECLASS")
    private Long mdlargeclass;

    @JSONField(name = "MDMIDDLECLASS")
    private Long mdmiddleclass;

    @JSONField(name = "FABRIC")
    private Long fabric;

    @JSONField(name = "SAFETECHCLASS")
    private Long safetechclass;

    @JSONField(name = "PROSTANDARD")
    private Long prostandard;

    @JSONField(name = "PROSTANDARD1")
    private Long prostandard1;

    @JSONField(name = "PROSTANDARD2")
    private Long prostandard2;

    @JSONField(name = "BUYPATTERNER")
    private Long buypatterner;

    @JSONField(name = "DESIGNER")
    private Long designer;

    @JSONField(name = "THEMESTORY")
    private Long themestory;

    @JSONField(name = "DISCENTER")
    private Long discenter;

    @JSONField(name = "SHELFCODE")
    private Long shelfcode;

    @JSONField(name = "PRICECOSTLIST")
    private BigDecimal pricecostlist;

    @JSONField(name = "PRICELIST")
    private BigDecimal pricelist;

    @JSONField(name = "PRICESETTLE")
    private BigDecimal pricesettle;

    @JSONField(name = "PRICELOWER")
    private BigDecimal pricelower;

    @JSONField(name = "TRIALSTORENUM")
    private Long trialstorenum;

    @JSONField(name = "DISSTORENUM")
    private Long disstorenum;

    @JSONField(name = "REDISSTORENUM")
    private Long redisstorenum;

    @JSONField(name = "TRIALDAYS")
    private Long trialdays;

    @JSONField(name = "RECYCLE")
    private Long recycle;

    @JSONField(name = "DATEOFFSHELF")
    private Long dateoffshelf;

    @JSONField(name = "SALEPERIOD")
    private Long saleperiod;

    @JSONField(name = "RELIABILITY")
    private BigDecimal reliability;

    @JSONField(name = "CAPACITY")
    private Long capacity;

    @JSONField(name = "MINLOTSIZE")
    private BigDecimal minlotsize;

    @JSONField(name = "UNIT")
    private Long unit;

    @JSONField(name = "DATEENDRECYCLE")
    private Long dateendrecycle;

    @JSONField(name = "DATEENDRETURN")
    private Long dateendreturn;

    @JSONField(name = "SUPREMARK")
    private String supremark;


    @JSONField(name = "DATETIMEDIM1")
    private Date datetimedim1;

    @JSONField(name = "DATETIMEDIM2")
    private Date datetimedim2;

    @JSONField(name = "DATETIMEDIM3")
    private Date datetimedim3;

    @JSONField(name = "DATETIMEDIM4")
    private Date datetimedim4;

    @JSONField(name = "DATETIMEDIM5")
    private Date datetimedim5;

    @JSONField(name = "STATUS")
    private Long status;

    @JSONField(name = "STATUSERID")
    private Long statuserid;

    @JSONField(name = "STATUSTIME")
    private Date statustime;

    @JSONField(name = "PROMANGROUP")
    private String promangroup;

    @JSONField(name = "PS_C_SPEC1GROUP_ID")
    private Long psCSpec1groupId;

    @JSONField(name = "PS_C_SPEC2GROUP_ID")
    private Long psCSpec2groupId;

    @JSONField(name = "PS_C_SPEC3GROUP_ID")
    private Long psCSpec3groupId;

    @JSONField(name = "PS_C_SPEC4GROUP_ID")
    private Long psCSpec4groupId;

    @JSONField(name = "STATUSERNAME")
    private String statusername;

    @JSONField(name = "STATUSERENAME")
    private String statuserename;

    @JSONField(name = "SYNC_SUPPRICE")
    private Long syncSupprice;

    @JSONField(name = "SYNC_STOREPRICE")
    private Long syncStoreprice;

    @JSONField(name = "CLRS")
    private String clrs;

    @JSONField(name = "SIZES")
    private String sizes;

    @JSONField(name = "CLRSECODE")
    private String clrsEcode;

    @JSONField(name = "CLRSENAME")
    private String clrsEname;

    @JSONField(name = "SIZESECODE")
    private String sizesEcode;

    @JSONField(name = "SIZESENAME")
    private String sizesEname;

    @JSONField(name = "ATTENTION")
    private Integer attention;

    @JSONField(name = "BROWSE")
    private Integer browse;

    @JSONField(name = "EVALUATE")
    private Integer evaluate;

    @JSONField(name = "SALESVOLUME")
    private Integer salesvolume;

    @JSONField(name = "ISUPLOADIMG")
    private String isuploadimg;

    @JSONField(name = "ISWRIDESC")
    private String iswridesc;

    @JSONField(name = "PROMOTION_PRICE")
    private BigDecimal promotionPrice;

    @JSONField(name = "COLLOCATION")
    private String collocation;

    @JSONField(name = "ISUP")
    private String isup;

    @JSONField(name = "ISSELECTION")
    private String isselection;

    @JSONField(name = "ISPROCURED")
    private String isprocured;

    @JSONField(name = "DATEONSHELF")
    private Date dateonshelf;

    @JSONField(name = "GROUP_TYPE")
    private Integer groupType;

    //等同于GROUP_TYPE,供oms转单使用
    @JSONField(name = "SKU_TYPE")
    private Integer skuType;

    @JSONField(name = "BUSINESSRANGE")
    private String businessrange;

    @JSONField(name = "MATERIELTYPE")
    private String materieltype;

    @JSONField(name = "WARE_TYPE")
    private Integer wareType;

    @JSONField(name = "YEAR_SEASON")
    private String yearSeason;

    @JSONField(name = "BASICUNIT")
    private Long basicunit;

    @JSONField(name = "LARGESERIES")
    private Long largeseries;

    @JSONField(name = "SMALLSERIES")
    private Long smallseries;

    @JSONField(name = "SUBSERIES")
    private Long subseries;

    @JSONField(name = "BIGCATE")
    private Long bigcate;

    @JSONField(name = "SMALLCATE")
    private Long smallcate;

    @JSONField(name = "SOUTHTIME")
    private Date southtime;

    @JSONField(name = "NORTHTIME")
    private Date northtime;

    @JSONField(name = "ORDERNUM")
    private String ordernum;

    @JSONField(name = "EDITIONTYPE")
    private Long editiontype;

    @JSONField(name = "NEEDLEWOVEN")
    private Long needlewoven;

    @JSONField(name = "CLOTHLINING")
    private Long clothlining;

    @JSONField(name = "CLOTHFABRIC")
    private String clothfabric;

    @JSONField(name = "CLOTHCOLLOCATION")
    private Long clothcollocation;

    @JSONField(name = "COTTONFEATHER")
    private Long cottonfeather;

    @JSONField(name = "CLOTHINGSTYLE")
    private Long clothingstyle;

    @JSONField(name = "THICKNESS")
    private Long thickness;

    @JSONField(name = "COMPONENT")
    private String component;

    @JSONField(name = "SHOEUPPER")
    private Long shoeupper;

    @JSONField(name = "LEATHERMESH")
    private Long leathermesh;

    @JSONField(name = "SOLE")
    private Long sole;

    @JSONField(name = "SHOELABEL")
    private Long shoelabel;

    @JSONField(name = "SHOEEXTEND")
    private Long shoeextend;

    @JSONField(name = "PRODFUNCTION")
    private String prodfunction;

    @JSONField(name = "PROPERT")
    private String propert;

    @JSONField(name = "CONCEPT")
    private String concept;

    @JSONField(name = "EXTENSION")
    private String extension;

    @JSONField(name = "PACKAGETYPES")
    private String packagetypes;

    @JSONField(name = "IS_PARENTAGE")
    private String isParentage;

    @JSONField(name = "IS_LOVERS")
    private String isLovers;

    @JSONField(name = "IS_SUIT")
    private String isSuit;

    @JSONField(name = "IS_SPONSOR")
    private String isSponsor;

    @JSONField(name = "IS_CERTAINLY")
    private String isCertainly;

    @JSONField(name = "IS_PRINTING")
    private String isPrinting;

    @JSONField(name = "IS_PACKAGE")
    private String isPackage;

    @JSONField(name = "IS_POP")
    private String isPop;

    @JSONField(name = "IS_MAINPUSH")
    private String isMainpush;

    @JSONField(name = "IS_PROMOTION")
    private String isPromotion;

    @JSONField(name = "IS_GIFT")
    private String isGift;

    @JSONField(name = "DELIVERY_METHOD")
    private String deliveryMethod;

    @JSONField(name = "IS_CIRCULARS")
    private String isCirculars;

    @JSONField(name = "IS_AIRFORBIDDEN")
    private String isAirforbidden;

    @JSONField(name = "IMAGE")
    private String image;

    @JSONField(name = "PS_C_SPEC_IDS")
    private String psCSpecIds;

    @JSONField(name = "PS_C_SPECOBJ_IDS")
    private String psCSpecobjIds;

    @JSONField(name = "DETAILDESC")
    private String detaildesc;

    @JSONField(name = "IMAGE_SKU")
    private String imageSku;

    @JSONField(name = "VIDEO")
    private String video;

    @JSONField(name = "PS_C_PRO_WARETYPE")
    private Integer psCProWareType;

    @JSONField(name = "NUM")
    private BigDecimal num;

    @JSONField(name = "WAVE_BAND")
    private String waveBand;

    @JSONField(name = "BASICUNIT_ENAME")
    private String basicunitEname;

    @JSONField(name = "BASICUNIT_ECODE")
    private String basicunitEcode;

    @JSONField(name = "SEX_NAME")
    private String sexName;

    @JSONField(name = "DISCOUNT_TYPE")
    private Long discountType;

    @JSONField(name = "MATRIXCOLNO")
    private Long matrixcolno;

    @JSONField(name = "PS_C_SIZE_ID")
    private Long psCSizeId;

    // start merge code
    @JSONField(name = "PRICE_COST")
    private BigDecimal priceCost;
    /**
     * 是否为虚拟订单
     */
    @JSONField(name = "IS_VIRTUAL")
    private String isVirtual;

    /***
     * 供应类型 0 正常 1.代销轻供 2.寄售轻供
     */
    @JSONField(name = "SUPPLY_TYPE")
    private Long supplyType;

    /**
     * 天猫购物金
     */
    @JSONField(name = "TMALL_EXPAND_CARD")
    private String tmallExpandCard;
    // end
    /**
     * 可用库存 （界面查询）
     */
    @JSONField(name = "AVAILABLE_QTY")
    private BigDecimal availableQty;

    //国际码
    @JSONField(name = "FORCODE")
    private String forcode;

    //效期管理
    @JSONField(name = "IS_ENABLE_EXPIRY")
    private String isEnableExpiry;

    /**
     * 基价下限
     */
    @JSONField(name = "BASE_PRICE_DOWN")
    private BigDecimal basePriceDown;

    @JSONField(name = "M_DIM1_ID")
    private Long mDim1Id;

    @JSONField(name = "M_DIM2_ID")
    private Long mDim2Id;

    @JSONField(name = "M_DIM3_ID")
    private Long mDim3Id;

    @JSONField(name = "M_DIM4_ID")
    private Long mDim4Id;

    @JSONField(name = "M_DIM5_ID")
    private Long mDim5Id;
    /**
     * 三级
     */
    @JSONField(name = "M_DIM6_ID")
    private Long mDim6Id;

    /**
     * 零级
     */
    @JSONField(name = "M_DIM12_ID")
    private Long mDim12Id;

    /**
     * 重量单位
     */
    @JSONField(name = "WEIGHT_UNIT")
    private String weightUnit;

    /**
     * 商品属性
     */
    @JSONField(name = "PRO_ATTRIBUTE_MAP")
    private Map<String, ProAttributeInfo> proAttributeMap;

    /**
     * 是否可用
     */
    @JSONField(name = "ISACTIVE")
    private String isactive;


}