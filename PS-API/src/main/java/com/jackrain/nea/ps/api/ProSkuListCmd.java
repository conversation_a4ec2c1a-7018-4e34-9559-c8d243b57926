package com.jackrain.nea.ps.api;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.request.ProSkuListCmdRequest;
import com.jackrain.nea.ps.api.request.SkuInfoListRequest;
import com.jackrain.nea.ps.api.table.IpCStandplatProItem;
import com.jackrain.nea.ps.api.table.PsCPro;
import com.jackrain.nea.sys.Command;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 根据条码id获取商品档案和条码档案列表
 */

public interface ProSkuListCmd extends Command {

    ValueHolder execute(ProSkuListCmdRequest proSkuListCmdRequest) throws NDSException;

    ValueHolder querySkuInfoIgnoreActive(ProSkuListCmdRequest proSkuListCmdRequest) throws NDSException;

    ValueHolder querySkuInfoByProEcode(String proEcode) throws NDSException;

    ValueHolder querySkuInfoByProEcodeAndClrsize(List<SkuInfoListRequest> reqs) throws NDSException;

    List<IpCStandplatProItem> selectIpCStandProductItemBySkuId(String skuId);

    /**
     * 根据条码id 获取商品信息
     *
     * @param psCSkuIdList
     */
    ValueHolderV14<Map<Long, PsCPro>> queryProInfoBySkuIdList(List<Long> psCSkuIdList);

    ValueHolder queryJdSkuInfoByWareId(String wareId);

    /**
     * 根据编码查询商品信息
     *
     * @param ecode
     * @return
     */
    ValueHolder selectProSkuByEcodeWithOutActiveSql(String ecode);

    /**
     * 根据多个商品编码查询商品信息
     *
     * @param ecodes
     * @return
     */
    ValueHolder selectProSkuByEcodesWithOutActive(List<String> ecodes);
}
