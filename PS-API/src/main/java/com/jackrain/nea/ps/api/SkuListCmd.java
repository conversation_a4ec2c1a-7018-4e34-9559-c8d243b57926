package com.jackrain.nea.ps.api;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.request.SkuListCmdRequest;
import com.jackrain.nea.ps.api.table.ProSku;
import com.jackrain.nea.sys.Command;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;

import java.util.List;

/**
 * @author: zhu lin yu
 * @since: 2019/4/29
 * create at : 2019/4/29 17:54
 */
public interface SkuListCmd extends Command {

    ValueHolder execute(SkuListCmdRequest skuListCmdRequest) throws NDSException;


    /**
     * 复制以上方法  eCodeList 查询
     * @param skuListCmdRequest 入参
     * @return list
     * @throws NDSException nds异常
     */
    ValueHolder findByeCodes(SkuListCmdRequest skuListCmdRequest) throws NDSException;

    /**
     * 根据国际条码查询条码信息
     * @param skuListCmdRequest
     * @return
     * @throws NDSException
     */
    ValueHolder findByforCodes(SkuListCmdRequest skuListCmdRequest) throws NDSException;

    /**
     * 根据国际条码查询条码
     * @param forCode 国际码
     * @return ValueHolderV14<ProSku>
     * @throws NDSException NDSException
     */
    ValueHolderV14<ProSku> getSkuByForCode(String forCode) throws NDSException;

    /**
     * 根据国标码/国际条码批量查询条码
     * @param forCodes 国标码/国际条码
     * @return ValueHolderV14<ProSku>
     * @throws NDSException NDSException
     */
    ValueHolderV14<List<ProSku>> querySkuListByForCodes(List<String> forCodes) throws NDSException;

}
