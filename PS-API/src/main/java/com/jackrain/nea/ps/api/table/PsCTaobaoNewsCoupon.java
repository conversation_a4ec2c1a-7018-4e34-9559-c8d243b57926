package com.jackrain.nea.ps.api.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 天猫新品商品优惠券打标
 *
 * <AUTHOR>
 * @create 2022/3/17 14:39
 */
@TableName(value = "ps_c_taobao_news_coupon")
@Data
public class PsCTaobaoNewsCoupon {

    /**
     * 编号
     */
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
    * 店铺
    */
    @JSONField(name="cp_c_shop_id")
    private Long cpCShopId;

    /**
    * 天猫商品id
    */
    @JSONField(name="item_id")
    private Long itemId;

    /**
    * 天猫品牌id
    */
    @JSONField(name="barnd_id")
    private Long barndId;

    /**
    * 店铺优惠券新品保护期档次
    */
    @JSONField(name="protection_period")
    private String protectionPeriod;

    /**
    * 优惠券上传状态
    */
    @JSONField(name="coupon_upload_status")
    private String couponUploadStatus;

    /**
    * 优惠券上传时间
    */
    @JSONField(name="coupon_upload_time")
    private Date couponUploadTime;

    /**
    * 优惠券上传操作人
    */
    @JSONField(name="coupon_upload_name")
    private String couponUploadName;

    /**
    * 上传描述
    */
    @JSONField(name="remark")
    private String remark;

    /**
    * 所属组织
    */
    @JSONField(name="ad_org_id")
    private Long adOrgId;

    /**
    * 可用
    */
    @JSONField(name="isactive")
    private String isactive;

    /**
    * 公司编号
    */
    @JSONField(name="ad_client_id")
    private Long adClientId;

    /**
    * 创建人
    */
    @JSONField(name="ownerid")
    private Long ownerid;

    /**
    * 创建人姓名
    */
    @JSONField(name="ownerename")
    private String ownerename;

    /**
    * 创建人用户名
    */
    @JSONField(name="ownername")
    private String ownername;

    /**
    * 创建时间
    */
    @JSONField(name="creationdate")
    private Date creationdate;

    /**
    * 修改人
    */
    @JSONField(name="modifierid")
    private Long modifierid;

    /**
    * 修改人姓名
    */
    @JSONField(name="modifierename")
    private String modifierename;

    /**
    * 修改人用户名
    */
    @JSONField(name="modifiername")
    private String modifiername;

    /**
    * 修改时间
    */
    @JSONField(name="modifieddate")
    private Date modifieddate;

}
