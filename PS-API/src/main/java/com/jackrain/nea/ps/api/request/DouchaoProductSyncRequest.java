package com.jackrain.nea.ps.api.request;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 抖超商品同步创建请求
 *
 * <AUTHOR>
 */
@Data
public class DouchaoProductSyncRequest implements Serializable {

    private static final long serialVersionUID = -6897197670379759581L;

    private Long psCSkuId;

    private String psCSkuCode;

    private String psCSkuName;

    private BigDecimal qty;

}
