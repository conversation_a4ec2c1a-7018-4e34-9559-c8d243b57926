package com.jackrain.nea.ps.api;

import com.github.pagehelper.PageInfo;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.request.ProListCmdRequest;
import com.jackrain.nea.ps.api.request.ProSelectPageRequest;
import com.jackrain.nea.ps.api.table.PsCPro;
import com.jackrain.nea.sys.Command;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;

/**
 * @author: pankai
 * @since: 2019-03-14
 * create at : 2019-03-14 16:31
 * 根据商品id获取商品信息
 */
public interface ProListCmd extends Command {

    ValueHolder execute(ProListCmdRequest proListCmdRequest) throws NDSException;

    ValueHolderV14<PageInfo<PsCPro>> selectPage(ProSelectPageRequest request);
}
