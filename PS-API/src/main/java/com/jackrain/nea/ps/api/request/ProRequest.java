package com.jackrain.nea.ps.api.request;

import com.jackrain.nea.ps.api.table.PsCPro;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ProRequest implements Serializable {
    private Long objid;
    private PsCPro psCPro;
    private List<SkuGroupRequest> SkuGroupRequestList;
    private List<Long> proYearIdList;
    private List<Long> sexidList;
    private List<Long> proseaIdList;
    private List<Long> pronature;
    private List<Long> promotiontype;
}
