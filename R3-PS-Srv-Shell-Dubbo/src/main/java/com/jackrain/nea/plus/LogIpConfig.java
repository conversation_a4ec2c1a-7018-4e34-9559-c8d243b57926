package com.jackrain.nea.plus;

import ch.qos.logback.classic.pattern.ClassicConverter;
import ch.qos.logback.classic.spi.ILoggingEvent;
import lombok.extern.slf4j.Slf4j;

import java.net.InetAddress;

/**
 * @Description IP工具类
 * <AUTHOR>
 * @Date 2021/6/11 16:34
 **/
@Slf4j
public class LogIpConfig extends ClassicConverter {

    private static String webIP;

    static {
        try {
            webIP = InetAddress.getLocalHost().getHostAddress();
        } catch (Exception e) {
            log.error("获取日志Ip异常", e);
            webIP = null;
        }
    }

    @Override
    public String convert(ILoggingEvent iLoggingEvent) {
        return webIP;
    }
}
