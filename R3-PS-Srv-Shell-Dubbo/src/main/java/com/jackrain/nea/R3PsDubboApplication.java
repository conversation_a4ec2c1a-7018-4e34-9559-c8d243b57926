package com.jackrain.nea;

import cn.hutool.extra.spring.EnableSpringUtil;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.spring.context.annotation.DubboComponentScan;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.util.StopWatch;

/**
 * Created by jackrain on 16/8/6.
 * Notice:@Import 服务端引入DubboConfig.class，消费端引入DubboConfConsumer.class
 */
@Slf4j
@SpringBootApplication
@MapperScan(value = "com.jackrain.nea",annotationClass = org.apache.ibatis.annotations.Mapper.class)
@DubboComponentScan({"com.burgeon.r3", "com.jackrain.nea"})
@EnableAsync
@EnableSpringUtil
public class R3PsDubboApplication extends SpringBootServletInitializer {

    static {
        System.setProperty("dubbo.application.logger", "slf4j");
    }

    public static void main(String[] args) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        System.setProperty("spring.devtools.restart.enabled", "false");
        ApplicationContext context = SpringApplication.run(applicationClass, args);
        System.out.println("Start SprintBoot Success ContextId=" + context.getId());
        stopWatch.stop();

        log.info(LogUtil.format("R3PsDubboApplication startup complete:{}",
                "R3PsDubboApplication.main"), stopWatch.getTotalTimeSeconds());
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(applicationClass);
    }

    private static final Class<R3PsDubboApplication> applicationClass = R3PsDubboApplication.class;
}
