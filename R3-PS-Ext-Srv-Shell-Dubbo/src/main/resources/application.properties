spring.application.name=R3-PS-Ext
# \u591A\u8BED\u8A00\u9ED8\u8BA4\u8BBE\u7F6E
spring.locale.default=zh_CN
server.port=8083
spring.profiles.active=dev
tlog.pattern=[$preHost][$traceId][$spanId][$userId]
dubbo.application.name=R3-PS-EXT
model.version=ps-ext:15
spring.cloud.nacos.discovery.group=r3-cloud
nacos.config.type=properties
nacos.config.bootstrap.enable=true
nacos.config.bootstrap.log-enable=false
nacos.config.username=nacos
nacos.config.password=nacos
nacos.config.group=r3-oms
# nacos.config.data-ids=ps,redis,mysql,reloadschema,common,elasticsearch,oss,psext-lts,psext-mq-consumer,psext-kafka-producer
#nacos.config.data-ids=ps,redis,mysql,reloadschema,common,elasticsearch,oss,psext-mq-consumer,psext-kafka-producer,xxl-job-executor
nacos.config.data-ids=ps,redis,mysql,reloadschema,common,elasticsearch,oss,xxl-job-executor
nacos.config.auto-refresh=true