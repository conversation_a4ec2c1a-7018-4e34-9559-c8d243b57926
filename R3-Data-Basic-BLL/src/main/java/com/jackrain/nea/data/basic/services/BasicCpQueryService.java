package com.jackrain.nea.data.basic.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.common.ReferenceUtil;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.api.CUsersInfoQueryCmd;
import com.jackrain.nea.cp.api.CpCostCenterQueryCmd;
import com.jackrain.nea.cp.api.CpcStoreQueryByPhyIdCmd;
import com.jackrain.nea.cp.api.CpcStoreQueryCmd;
import com.jackrain.nea.cp.api.CpcStoreUserQueryCmd;
import com.jackrain.nea.cp.api.CsupplierGetCmd;
import com.jackrain.nea.cp.api.ReigonQueryCmd;
import com.jackrain.nea.cp.request.CUsersInfoQueryCmdRequest;
import com.jackrain.nea.cp.request.CpCostCenterQueryCmdRequest;
import com.jackrain.nea.cp.request.CpcStoreQueryCmdRequest;
import com.jackrain.nea.cp.request.CpcStoreUserQueryCmdRequest;
import com.jackrain.nea.cp.request.RegionQueryCmdRequest;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.cp.result.CpCSupplier;
import com.jackrain.nea.cp.result.CpCostCenter;
import com.jackrain.nea.cp.result.CpcStoreQueryResult;
import com.jackrain.nea.cp.result.ReginQueryResult;
import com.jackrain.nea.cpext.api.CpCCustomerGetCmd;
import com.jackrain.nea.cpext.api.CpLogisticsQueryCmd;
import com.jackrain.nea.cpext.api.CpShopQueryCmd;
import com.jackrain.nea.cpext.model.RedisKeyConstans;
import com.jackrain.nea.cpext.model.table.CpCustomer;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.cpext.model.table.CpLogisticsItem;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.data.basic.common.BasicConstants;
import com.jackrain.nea.data.basic.model.request.CpCostCenterRequest;
import com.jackrain.nea.data.basic.model.request.CpCustomerQueryRequest;
import com.jackrain.nea.data.basic.model.request.RegionInfoQueryRequest;
import com.jackrain.nea.data.basic.model.request.ShopQueryRequest;
import com.jackrain.nea.data.basic.model.request.StoreInfoQueryRequest;
import com.jackrain.nea.data.basic.model.request.SupplierInfoQueryRequest;
import com.jackrain.nea.data.basic.model.result.CpLogisticsAndItemResult;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;


@Component
@Slf4j
public class BasicCpQueryService {

    /**
     * 获取省市区redis服务
     *
     * @author: pankai
     * @since: 2019-03-19
     * create at : 2019-03-19 16:01
     */
    public Map getRegionInfo(RegionInfoQueryRequest regionInfoQueryRequest) {

        //初始化返回map
        Map map = new HashMap();

        // 第一步：记录日志信息（AOP效率比较低，暂时不采用切面记录）。
        // 按照以下规则进行记录日志信息：log.debug(LogUtil.format("start 类名.方法名. model名=" + ***** 参数值)
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start.BasicCpQueryService.getRegionInfowys：") + regionInfoQueryRequest);
        }

        //初始化cp部分的请求model
        RegionQueryCmdRequest regionQueryCmdRequest = new RegionQueryCmdRequest();

        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();

        //判断入参
        if (regionInfoQueryRequest == null || (regionInfoQueryRequest.getProId() == null && regionInfoQueryRequest.getCityId() == null && regionInfoQueryRequest.getRegionId() == null)) {
            return map;
        }

        String type = BasicConstants.CP_REG_KEY;

        try {
            String cd = "";
            if (regionInfoQueryRequest.getProId() != null) {
                if (regionInfoQueryRequest.getCityId() != null) {
                    if (regionInfoQueryRequest.getRegionId() != null) {//修改省市区
                        cd = type + regionInfoQueryRequest.getProId().toString() + ":" + regionInfoQueryRequest.getCityId().toString() + ":" + regionInfoQueryRequest.getRegionId().toString();
                    } else {//修改省市
                        cd = type + regionInfoQueryRequest.getProId().toString() + ":" + regionInfoQueryRequest.getCityId().toString();
                    }
                } else if (regionInfoQueryRequest.getRegionId() != null) {//修改省区
                    cd = type + regionInfoQueryRequest.getProId().toString() + ":" + regionInfoQueryRequest.getRegionId().toString();
                } else {//只修改省
                    cd = type + regionInfoQueryRequest.getProId().toString();
                }
            } else if (regionInfoQueryRequest.getCityId() != null) {
                if (regionInfoQueryRequest.getRegionId() != null) {//修改市区
                    cd = type + regionInfoQueryRequest.getCityId().toString() + ":" + regionInfoQueryRequest.getRegionId().toString();
                } else {//修改市
                    cd = type + regionInfoQueryRequest.getCityId().toString();
                }
            } else if (regionInfoQueryRequest.getRegionId() != null) {//修改区
                cd = type + regionInfoQueryRequest.getRegionId().toString();
            }
            log.debug(LogUtil.format("cd===============================>") + cd);
            //查询redis是否含有此类型的省市区数据
            Object region = redisTemplate.opsForValue().get(cd);
            if (null != region) {
                log.debug(LogUtil.format("==================redis"));
                ReginQueryResult reginQueryResult = JSONObject.parseObject(region.toString(), ReginQueryResult.class);
                map.put("data", reginQueryResult);
            } else {
                log.debug(LogUtil.format("------------------>rpc"));
                //调用查看省市区的服务
                ReigonQueryCmd reigonQueryCmd = (ReigonQueryCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(),
                        ReigonQueryCmd.class.getName(), "cp", "1.0");

                //参数传递
                String proId = regionInfoQueryRequest.getProId() == null ? null : regionInfoQueryRequest.getProId().toString();
                String cityId = regionInfoQueryRequest.getCityId() == null ? null : regionInfoQueryRequest.getCityId().toString();
                String regionId = regionInfoQueryRequest.getRegionId() == null ? null : regionInfoQueryRequest.getRegionId().toString();
                if (StringUtils.isNotEmpty(proId)) {
                    regionQueryCmdRequest.setProId(Long.valueOf(proId));
                }
                if (StringUtils.isNotEmpty(cityId)) {
                    regionQueryCmdRequest.setCityId(Long.valueOf(cityId));
                }
                if (StringUtils.isNotEmpty(regionId)) {
                    regionQueryCmdRequest.setRegionId(Long.valueOf(regionId));
                }
                ValueHolder valueHolder = reigonQueryCmd.execute(regionQueryCmdRequest);

                ReginQueryResult reginQueryResult = new ReginQueryResult();
                if (valueHolder != null && valueHolder.getData() != null && valueHolder.getData().get("data") != null) {
                    reginQueryResult = (ReginQueryResult) valueHolder.getData().get("data");

                    // 按照以下规则进行记录日志信息：log.debug(LogUtil.format("start 类名.方法名. model名=" + ***** 参数值)
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("Finish BasicCpQueryService.getRegionInfo.ReturnResult=") + reginQueryResult);
                    }

                    //省市区put到redis内
                    redisTemplate.opsForValue().set(cd, JSONObject.toJSONString(reginQueryResult), 1, TimeUnit.DAYS);
                    map.put("data", reginQueryResult);
                }
            }
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("map==========>") + map);
        }
        return map;

    }

    /**
     * 根据实体仓id获取虚拟仓列表
     *
     * @author: pankai
     * @since: 2019-03-19
     * create at : 2019-03-19 16:01
     */
    public HashMap<Long, List<CpCStore>> getStoreInfoByPhyId(StoreInfoQueryRequest storeInfoQueryRequest) {

        //初始化业务返回model
        HashMap<Long, List<CpCStore>> hashMap = new HashMap<>();
        List<CpCStore> cpCStores = new ArrayList<>();
        CpcStoreQueryResult cpcStoreQueryResult = new CpcStoreQueryResult();
        //初始化cp部分的请求model
        CpcStoreQueryCmdRequest cpcStoreQueryCmdRequest = new CpcStoreQueryCmdRequest();

        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();

        // 第一步：记录日志信息（AOP效率比较低，暂时不采用切面记录）。
        // 按照以下规则进行记录日志信息：log.debug(LogUtil.format("start 类名.方法名. model名=" + ***** 参数值)
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start.BasicCpQueryService.getStoreInfo：") + storeInfoQueryRequest);
        }

        //判断入参
        if (storeInfoQueryRequest == null || storeInfoQueryRequest.getPhyId() == null) {
            return hashMap;
        }

        String type = BasicConstants.CP_PHY_KEY;

        try {
            String cd = type + storeInfoQueryRequest.getPhyId();
            //查询redis是否含有该key的值
            Object cpcStore = redisTemplate.opsForValue().get(cd);
            if (null != cpcStore) {
                cpCStores = JSONObject.parseArray(cpcStore.toString(), CpCStore.class);
            } else {
                //调用查询虚拟仓信息
                CpcStoreQueryByPhyIdCmd cpcStoreQueryByPhyIdCmd = (CpcStoreQueryByPhyIdCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(),
                        CpcStoreQueryByPhyIdCmd.class.getName(), "cp", "1.0");

                cpcStoreQueryCmdRequest.setPhyId(storeInfoQueryRequest.getPhyId());
                ValueHolder valueHolder = cpcStoreQueryByPhyIdCmd.execute(cpcStoreQueryCmdRequest);

                if (valueHolder != null && valueHolder.getData() != null && valueHolder.getData().get("data") != null) {
                    cpcStoreQueryResult = (CpcStoreQueryResult) valueHolder.getData().get("data");

                    // 按照以下规则进行记录日志信息：log.debug(LogUtil.format("start 类名.方法名. model名=" + ***** 参数值)
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("Finish.BasicCpQueryService.getStoreInfo.ReturnResult：") + cpcStoreQueryResult);
                    }
                    if (!CollectionUtils.isEmpty(cpcStoreQueryResult.getCpCStoreList())) {

                        cpCStores.addAll(cpcStoreQueryResult.getCpCStoreList());
                        //将列表数据插入redis
                        redisTemplate.opsForValue().set(cd, JSONObject.toJSONString(cpcStoreQueryResult.getCpCStoreList()), 1, TimeUnit.DAYS);
                    }
                }
            }
            if (!CollectionUtils.isEmpty(cpCStores)) {
                hashMap.put(storeInfoQueryRequest.getPhyId(), cpCStores);
            }
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }

        return hashMap;

    }

    /**
     * 根据用户id获取店仓信息
     *
     * @author: pankai
     * @since: 2019-03-19
     * create at : 2019-03-19 16:01
     */
    public HashMap<Long, CpCStore> getStoreInfoByUserId(StoreInfoQueryRequest storeInfoQueryRequest) {

        //初始化返回model
        HashMap<Long, CpCStore> hashMap = new HashMap<>();
        CpCStore cpCStore = new CpCStore();
        //初始化cp部分的请求model
        CpcStoreUserQueryCmdRequest cpcStoreUserQueryCmdRequest = new CpcStoreUserQueryCmdRequest();

        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();

        // 第一步：记录日志信息（AOP效率比较低，暂时不采用切面记录）。
        // 按照以下规则进行记录日志信息：log.debug(LogUtil.format("start 类名.方法名. model名=" + ***** 参数值)
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start BasicCpQueryService.getStoreInfoByUserId：") + storeInfoQueryRequest);
        }

        //判断入参
        if (storeInfoQueryRequest == null || storeInfoQueryRequest.getUserId() == null) {
            return hashMap;
        }
        String type = BasicConstants.CP_STO_USERID;
        try {
            String cd = type + storeInfoQueryRequest.getUserId();
            //查询redis是否含有该key的值
            Object store = redisTemplate.opsForValue().get(cd);
            if (null != store) {
                cpCStore = JSONObject.parseObject(store.toString(), CpCStore.class);
            } else {
                //调用查询虚拟仓信息
                CpcStoreUserQueryCmd cpcStoreUserQueryCmd = (CpcStoreUserQueryCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(),
                        CpcStoreUserQueryCmd.class.getName(), "cp", "1.0");

                cpcStoreUserQueryCmdRequest.setId(storeInfoQueryRequest.getUserId());
                ValueHolder valueHolder = cpcStoreUserQueryCmd.execute(cpcStoreUserQueryCmdRequest);

                if (valueHolder != null && valueHolder.getData() != null && valueHolder.getData().get("data") != null) {
                    cpCStore = (CpCStore) valueHolder.getData().get("data");

                    // 按照以下规则进行记录日志信息：log.debug(LogUtil.format("start 类名.方法名. model名=" + ***** 参数值)
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("Finish BasicCpQueryService.getStoreInfoByUserId.ReturnResult：") + JSON.toJSONString(cpCStore));
                    }

                    //将列表数据插入redis
                    redisTemplate.opsForValue().set(cd, JSONObject.toJSONString(cpCStore), 1, TimeUnit.DAYS);
                }
            }
            //赋值于hasmap
            if (cpCStore != null) {
                hashMap.put(storeInfoQueryRequest.getUserId(), cpCStore);
            }
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        return hashMap;

    }

//    /**
//     * 根据用户id获取店仓信息
//     *
//     * @author: pankai
//     * @since: 2019-03-19
//     * create at : 2019-03-19 16:01
//     */
//    public CpCStore getStoreInfoByUser(StoreInfoQueryRequest storeInfoQueryRequest) {
//
//        //初始化返回model
//        CpCStore cpCStore = new CpCStore();
//        //初始化cp部分的请求model
//        CpcStoreUserQueryCmdRequest cpcStoreUserQueryCmdRequest = new CpcStoreUserQueryCmdRequest();
//
//        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
//
//        // 第一步：记录日志信息（AOP效率比较低，暂时不采用切面记录）。
//        // 按照以下规则进行记录日志信息：log.debug(LogUtil.format("start 类名.方法名. model名=" + ***** 参数值)
//        if (log.isDebugEnabled()) {
//            log.debug(LogUtil.format("Start BasicCpQueryService.getStoreInfoByUser=" + storeInfoQueryRequest + ";");
//        }
//
//        //判断入参
//        if (storeInfoQueryRequest == null || storeInfoQueryRequest.getUserId() == null) {
//            return cpCStore;
//        }
//        String type = BasicConstants.CP_STO_USERID;
//        try {
//            String cd = type + storeInfoQueryRequest.getUserId();
//            //查询redis是否含有该key的值
//            Object store = redisTemplate.opsForValue().get(cd);
//            if (null != store) {
//                cpCStore = JSONObject.parseObject(store.toString(), CpCStore.class);
//            } else {
//                //调用查询虚拟仓信息
//                CpcStoreUserQueryCmd cpcStoreUserQueryCmd = (CpcStoreUserQueryCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(),
//                        CpcStoreUserQueryCmd.class.getName(), "cp", "1.0");
//
//                cpcStoreUserQueryCmdRequest.setId(storeInfoQueryRequest.getUserId());
//                ValueHolder valueHolder = cpcStoreUserQueryCmd.execute(cpcStoreUserQueryCmdRequest);
//
//                if (valueHolder != null && valueHolder.getData() != null && valueHolder.getData().get("data") != null) {
//                    cpCStore = (CpCStore) valueHolder.getData().get("data");
//                    // 按照以下规则进行记录日志信息：log.debug(LogUtil.format("start 类名.方法名. model名=" + ***** 参数值)
//                    if (log.isDebugEnabled()) {
//                        log.debug(LogUtil.format("Finish BasicCpQueryService.getStoreInfo.ReturnResult=" + cpCStore + ";");
//                    }
//                    //将列表数据插入redis
//                    redisTemplate.opsForValue().set(cd, JSONObject.toJSONString(cpCStore), TimeUnit.DAYS.toDays(1));
//                }
//            }
//        } catch (Exception e) {
//            System.out.println(e.getMessage());
//        }
//        return cpCStore;
//    }

    /**
     * 获取供应商信息
     *
     * @author: pankai
     * @since: 2019-03-19
     * create at : 2019-03-19 16:01
     */
    public HashMap<Long, CpCSupplier> getSupplierInfo(SupplierInfoQueryRequest supplierInfoQueryRequest) {

        CpCSupplier cpCSupplier = new CpCSupplier();
        HashMap<Long, CpCSupplier> map = new HashMap<>();
        // 第一步：记录日志信息（AOP效率比较低，暂时不采用切面记录）。
        // 按照以下规则进行记录日志信息：log.debug(LogUtil.format("start 类名.方法名. model名=" + ***** 参数值)
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start.BasicCpQueryService.getSupplierInfo：") + supplierInfoQueryRequest);
        }

        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();

        //判断入参
        if (supplierInfoQueryRequest == null || supplierInfoQueryRequest.getId() == null) {
            return map;
        }

        String type = BasicConstants.CP_SUP_KEY;

        try {
            String cd = type + supplierInfoQueryRequest.getId();
            //查询redis是否含有该key的值
            Object supplier = redisTemplate.opsForValue().get(cd);
            if (null != supplier) {
                cpCSupplier = JSONObject.parseObject(supplier.toString(), CpCSupplier.class);
            } else {
                //调用查询供应商服务
                CsupplierGetCmd csupplierGetCmd = (CsupplierGetCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(),
                        CsupplierGetCmd.class.getName(), "cp", "1.0");
                //入参
                HashMap hashMap = new HashMap();
                hashMap.put("supplierId", supplierInfoQueryRequest.getId());

                ValueHolder valueHolder = csupplierGetCmd.execute(hashMap);
                if (valueHolder != null && valueHolder.getData() != null && valueHolder.getData().get("data") != null) {
                    cpCSupplier = (CpCSupplier) (valueHolder.getData().get("data"));
                    // 按照以下规则进行记录日志信息：log.debug(LogUtil.format("start 类名.方法名. model名=" + ***** 参数值)
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("Finish.BasicCpQueryService.getSupplierInfo.ReturnResult：") + JSON.toJSONString(cpCSupplier));
                    }
                    //将列表数据插入redis
                    redisTemplate.opsForValue().set(cd, JSONObject.toJSONString(cpCSupplier), 1, TimeUnit.DAYS);
                }
            }
            //判断返回值是否为空
            if (null != cpCSupplier) {
                map.put(supplierInfoQueryRequest.getId(), cpCSupplier);
            }
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }

        return map;

    }

    /**
     * 获取经销商信息
     *
     * @param cpCustomerQueryRequest
     * @return
     */
    public HashMap<Long, CpCustomer> getCpCustomerInfo(CpCustomerQueryRequest cpCustomerQueryRequest) {
        CpCustomer cpCustomer = new CpCustomer();
        HashMap<Long, CpCustomer> map = new HashMap<>(16);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start BasicCpQueryService.getCpCustomerInfo：{}"),
                    JSONObject.toJSONString(cpCustomerQueryRequest));
        }

        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();

        //判断入参
        if (cpCustomerQueryRequest == null || cpCustomerQueryRequest.getId() == null) {
            return map;
        }

        String type = BasicConstants.CP_CUSTOMER_ID_KEY;

        try {
            String cd = type + cpCustomerQueryRequest.getId();
            //查询redis是否含有该key的值
            Object supplier = redisTemplate.opsForValue().get(cd);
            if (null != supplier) {
                cpCustomer = JSONObject.parseObject(supplier.toString(), CpCustomer.class);
            } else {
                //调用查询供应商服务
                CpCCustomerGetCmd csupplierGetCmd = (CpCCustomerGetCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(),
                        CpCCustomerGetCmd.class.getName(), "cp-ext", "1.0");
                //入参
                HashMap hashMap = new HashMap(16);
                hashMap.put("customerId", cpCustomerQueryRequest.getId());

                ValueHolder valueHolder = csupplierGetCmd.execute(hashMap);
                if (valueHolder != null && valueHolder.getData() != null && valueHolder.getData().get("data") != null) {
                    cpCustomer = (CpCustomer) (valueHolder.getData().get("data"));
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("Finish.BasicCpQueryService.getCpCustomerInfo.ReturnResult" +
                                ".cpCustomer：{}"), JSONObject.toJSONString(cpCustomer));
                    }
                    //将列表数据插入redis
                    redisTemplate.opsForValue().set(cd, JSONObject.toJSONString(cpCustomer), 1, TimeUnit.DAYS);
                }
            }
            //判断返回值是否为空
            if (null != cpCustomer) {
                map.put(cpCustomerQueryRequest.getId(), cpCustomer);
            }
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        return map;
    }

    /**
     * 根据虚拟仓id获取虚拟仓信息
     *
     * @author: pankai
     * @since: 2019-03-19
     * create at : 2019-03-19 16:01
     */
    public HashMap<Long, CpCStore> getStoreInfo(StoreInfoQueryRequest storeInfoQueryRequest) {

        // 第一步：记录日志信息（AOP效率比较低，暂时不采用切面记录）。
        // 按照以下规则进行记录日志信息：log.debug(LogUtil.format("start 类名.方法名. model名=" + ***** 参数值)
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start.BasicCpQueryService.getWhsInfo：") + JSON.toJSONString(storeInfoQueryRequest));
        }

        //初始化返回model
        HashMap<Long, CpCStore> hashMap = new HashMap<>();
        List<CpCStore> cpCStores = new ArrayList<>();
        CpCStore cpCStore = new CpCStore();
        //初始化cp部分的请求model
        CpcStoreQueryCmdRequest cpcStoreQueryCmdRequest = new CpcStoreQueryCmdRequest();
        //用于id或者ecode作为第二条件的传入
        List<Long> cpcStoreIds = new ArrayList<>();

        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();

        //判断入参
        if (storeInfoQueryRequest == null || CollectionUtils.isEmpty(storeInfoQueryRequest.getIds())) {
            return hashMap;
        }

        String type = BasicConstants.CP_STO_KEY;

        try {
            if (!CollectionUtils.isEmpty(storeInfoQueryRequest.getIds())) {
                for (Long id : storeInfoQueryRequest.getIds()) {
                    String cd = type + id;
                    //查询redis是否含有该key的值
                    Object cpcStore = redisTemplate.opsForValue().get(cd);
                    if (null != cpcStore) {
                        cpCStore = JSONObject.parseObject(cpcStore.toString(), CpCStore.class);
                        cpCStores.add(cpCStore);
                    } else {
                        cpcStoreIds.add(id);
                    }
                }
            }
            //判断没查到结果的id集合是否为空
            if (!CollectionUtils.isEmpty(cpcStoreIds)) {
                //调用查询虚拟仓信息
                CpcStoreQueryCmd cpcStoreQueryCmd = (CpcStoreQueryCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(),
                        CpcStoreQueryCmd.class.getName(), "cp", "1.0");
                cpcStoreQueryCmdRequest.setIds(cpcStoreIds);
                ValueHolder valueHolder = cpcStoreQueryCmd.execute(cpcStoreQueryCmdRequest);

                //判断结果是否为空
                if (valueHolder != null && valueHolder.getData() != null && valueHolder.getData().get("data") != null) {
                    CpcStoreQueryResult cpcStoreQueryResult = (CpcStoreQueryResult) valueHolder.getData().get("data");
                    List<CpCStore> cpCStoreList = cpcStoreQueryResult.getCpCStoreList();
                    //判断list是否为空
                    if (!CollectionUtils.isEmpty(cpCStoreList)) {
                        cpCStores.addAll(cpCStoreList);
                        for (CpCStore cpCSto : cpCStoreList) {
                            //将列表数据插入redis
                            redisTemplate.opsForValue().set(type + cpCSto.getId(), JSONObject.toJSONString(cpCSto), 1, TimeUnit.DAYS);
                        }
                    }
                }
            }
            //判断结果集是否为空
            if (!CollectionUtils.isEmpty(cpCStores)) {
                for (CpCStore cStore : cpCStores) {
                    hashMap.put(cStore.getId(), cStore);
                }
            }
        } catch (
                Exception e) {
            System.out.println(e.getMessage());
        }

        return hashMap;

    }

    /**
     * 根据虚拟仓ecode获取虚拟仓信息
     *
     * @author: pankai
     * @since: 2019-03-19
     * create at : 2019-03-19 16:01
     */
    public HashMap<String, CpCStore> getStoreInfoByEcode(StoreInfoQueryRequest storeInfoQueryRequest) {

        // 第一步：记录日志信息（AOP效率比较低，暂时不采用切面记录）。
        // 按照以下规则进行记录日志信息：log.debug(LogUtil.format("start 类名.方法名. model名=" + ***** 参数值)
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start.BasicCpQueryService.getWhsInfo：") + JSON.toJSONString(storeInfoQueryRequest));
        }

        //初始化返回model
        HashMap<String, CpCStore> hashMap = new HashMap<>();
        List<CpCStore> cpCStores = new ArrayList<>();
        CpCStore cpCStore = new CpCStore();
        //初始化cp部分的请求model
        CpcStoreQueryCmdRequest cpcStoreQueryCmdRequest = new CpcStoreQueryCmdRequest();
        //用于id或者ecode作为第二条件的传入
        List<String> cpcStoreEcodes = new ArrayList<>();

        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();

        //判断入参
        if (storeInfoQueryRequest == null || (CollectionUtils.isEmpty(storeInfoQueryRequest.getIds()) && CollectionUtils.isEmpty(storeInfoQueryRequest.getEcodes()))) {
            return hashMap;
        }

        String ecodeType = BasicConstants.CP_STO_ECODE_KEY;

        try {
            if (!CollectionUtils.isEmpty(storeInfoQueryRequest.getEcodes())) {
                for (String ecode : storeInfoQueryRequest.getEcodes()) {
                    String cd = ecodeType + ecode;
                    //查询redis是否含有该key的值
                    Object cpcStore = redisTemplate.opsForValue().get(cd);
                    if (null != cpcStore) {
                        cpCStore = JSONObject.parseObject(cpcStore.toString(), CpCStore.class);
                        cpCStores.add(cpCStore);
                    } else {
                        cpcStoreEcodes.add(ecode);
                    }
                }
            }
            //判断没查到结果的id集合是否为空
            if (!CollectionUtils.isEmpty(cpcStoreEcodes)) {
                //调用查询虚拟仓信息
                CpcStoreQueryCmd cpcStoreQueryCmd = (CpcStoreQueryCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(),
                        CpcStoreQueryCmd.class.getName(), "cp", "1.0");
                cpcStoreQueryCmdRequest.setEcodes(cpcStoreEcodes);
                ValueHolder valueHolder = cpcStoreQueryCmd.execute(cpcStoreQueryCmdRequest);

                //判断结果是否为空
                if (valueHolder != null && valueHolder.getData() != null && valueHolder.getData().get("data") != null) {
                    CpcStoreQueryResult cpcStoreQueryResult = (CpcStoreQueryResult) valueHolder.getData().get("data");
                    List<CpCStore> cpCStoreList = cpcStoreQueryResult.getCpCStoreList();
                    //判断list是否为空
                    if (!CollectionUtils.isEmpty(cpCStoreList)) {
                        cpCStores.addAll(cpCStoreList);
                        for (CpCStore cpCSto : cpCStoreList) {
                            //将列表数据插入redis
                            redisTemplate.opsForValue().set(ecodeType + cpCSto.getEcode(), JSONObject.toJSONString(cpCSto), 1, TimeUnit.DAYS);
                        }
                    }
                }
            }
            //判断返回集是否为空
            if (!CollectionUtils.isEmpty(cpCStores)) {
                for (CpCStore cStore : cpCStores) {
                    hashMap.put(cStore.getEcode(), cStore);
                }
            }
        } catch (
                Exception e) {
            System.out.println(e.getMessage());
        }

        return hashMap;
    }

    /**
     * 根据渠道id查询渠道信息
     *
     * @param request
     * @return
     * @throws Exception
     */
    public HashMap<Long, CpShop> getShopInfo(ShopQueryRequest request) {

        long beginTime = System.currentTimeMillis();

        //初始化返回值
        HashMap<Long, CpShop> shopInfoMap = new HashMap<>();

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start BasicPsQueryService.getShopInfo,request:{}"),
                    JSONObject.toJSONString(request));
        }

        List<CpShop> cpShopList = new ArrayList<>();
        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
        List<Long> rpcShopQueryList = new ArrayList<Long>();

        //检查入参
        if (request == null || CollectionUtils.isEmpty(request.getShopIds())) {
            return shopInfoMap;
        }

        try {
            //判断条码信息在redis中是否存在
            for (Long shopId : request.getShopIds()) {
                Object shopInfo = redisTemplate.opsForValue().get(BasicConstants.CP_SHOP_ID_KEY + shopId);
                if (shopInfo != null) {
                    CpShop cpShop = JSONObject.parseObject(shopInfo.toString(), CpShop.class);
                    cpShopList.add(cpShop);
                } else {
                    rpcShopQueryList.add(shopId);
                }
            }

            if (!CollectionUtils.isEmpty(rpcShopQueryList)) {
                CpShopQueryCmd cpShopQueryCmd = (CpShopQueryCmd) ReferenceUtil.refer(
                        ApplicationContextHandle.getApplicationContext(), CpShopQueryCmd.class.getName(),
                        "cp-ext", "1.0");

                //redis中不存在的条码信息，调用RPC接口获得条码信息
                List<CpShop> cpShops = cpShopQueryCmd.queryShopByIds(rpcShopQueryList);
                if (CollectionUtils.isNotEmpty(cpShops)) {
                    cpShopList.addAll(cpShops);
                }
            }

            if (!CollectionUtils.isEmpty(cpShopList)) {
                //RPC接口获得条码信息保存到redis中
                for (CpShop cpShop : cpShopList) {
                    redisTemplate.opsForValue().set(BasicConstants.CP_SHOP_ID_KEY + cpShop.getId(),
                            JSONObject.toJSONString(cpShop), 1, TimeUnit.DAYS);
                    shopInfoMap.put(cpShop.getId(), cpShop);
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format(" 获取店铺信息出错,店铺id为：{}"), JSONObject.toJSONString(request));
            throw new NDSException(e);
        }

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Finish.BasicPsQueryService.getShopInfo,ReturnResult：{},spend{}ms")
                    , JSONObject.toJSONString(shopInfoMap), System.currentTimeMillis() - beginTime);
        }

        return shopInfoMap;
    }

    /**
     * 根据用户名称获取用户信息
     *
     * @Author: chenb
     * @Date: 2019/11/14 14:35
     */
    public HashMap<String, User> getUserInfoByName(CUsersInfoQueryCmdRequest request) {

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start.BasicCpQueryService.getUserInfoByName.CUsersInfoQueryCmdRequest：{}"),
                    JSONObject.toJSONString(request));
        }

        //初始化返回值
        HashMap<String, User> resultMap = new HashMap<>();
        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
        List<String> rpcUserInfoQueryList = new ArrayList<String>();

        //检查入参
        if (request == null || CollectionUtils.isEmpty(request.getNameList())) {
            return resultMap;
        }

        try {

            //判断条码信息在redis中是否存在
            for (String name : request.getNameList()) {

                Object userInfo = redisTemplate.opsForValue().get(BasicConstants.CP_USER_NAME_KEY + name);

                if (userInfo != null) {
                    User user = JSONObject.parseObject(userInfo.toString(), UserImpl.class);
                    resultMap.put(user.getName(), user);
                } else {
                    rpcUserInfoQueryList.add(name);
                }

            }

            if (!CollectionUtils.isEmpty(rpcUserInfoQueryList)) {

                CUsersInfoQueryCmd cUsersInfoQueryCmd = (CUsersInfoQueryCmd) ReferenceUtil.refer(
                        ApplicationContextHandle.getApplicationContext(), CUsersInfoQueryCmd.class.getName(),
                        "cp", "1.0");

                CUsersInfoQueryCmdRequest cUsersInfoQueryCmdRequest = new CUsersInfoQueryCmdRequest();

                //redis中不存在的条码信息，调用RPC接口获得条码信息
                cUsersInfoQueryCmdRequest.setNameList(rpcUserInfoQueryList);

                ValueHolder valueHolder = cUsersInfoQueryCmd.execute(cUsersInfoQueryCmdRequest);

                if (valueHolder != null && valueHolder.getData() != null &&
                        valueHolder.getData().get("data") != null) {

                    Map<String, User> rpcResultMap = (HashMap) valueHolder.getData().get("data");

                    //判断rpc调用返回的值是否在redis内
                    if (rpcResultMap != null && rpcResultMap.size() > 0) {
                        //RPC接口获得条码信息保存到redis中
                        for (User userItem : rpcResultMap.values()) {
                            redisTemplate.opsForValue().set(BasicConstants.CP_USER_NAME_KEY + userItem.getName(),
                                    JSONObject.toJSONString(userItem), 1, TimeUnit.DAYS);
                        }
                        resultMap.putAll(rpcResultMap);
                    }
                }
            }

        } catch (Exception e) {
            log.error(LogUtil.format("BasicCpQueryService.getUserInfoByName.error：{}"), Throwables.getStackTraceAsString(e));
        }

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Finish BasicCpQueryService.getUserInfoByName.ReturnResult：{}"),
                    JSONObject.toJSONString(resultMap.size()));
        }

        return resultMap;

    }

    /**
     * 根据TMS物流公司编码获取物流档案
     *
     * @param cpLogisticsEcode 物流公司编码
     * @return CpLogistics
     */
    public CpLogistics queryByLogisticsEcode(String cpLogisticsEcode, Boolean isTms) {
        if (log.isDebugEnabled()) {
            log.debug("BasicCpQueryService.queryByTmsLogisticsEcode.tmsLogisticsEcode:{},isTms:{}", cpLogisticsEcode, isTms);
        }
        String redisKey = null;
        if (isTms) {
            redisKey = String.format("%sTMS:%s", RedisKeyConstans.CP_LOGISTICS_ID_KEY, cpLogisticsEcode);
        } else {
            redisKey = String.format("%sEcode:%s", RedisKeyConstans.CP_LOGISTICS_ID_KEY, cpLogisticsEcode);
        }
        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
        CpLogistics cpLogistics = null;
        try {
            String s = redisTemplate.opsForValue().get(redisKey);
            if (org.springframework.util.StringUtils.isEmpty(s)) {
                CpLogisticsQueryCmd refer = (CpLogisticsQueryCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(),
                        CpLogisticsQueryCmd.class.getName(), "cp-ext", "1.0");
                List<String> requestList = new ArrayList<>();
                requestList.add(cpLogisticsEcode);
                ValueHolderV14<Map<String, CpLogistics>> mapValueHolderV14 = null;
                if (isTms) {
                    mapValueHolderV14 = refer.queryLogisticsByTmsEcodes(requestList);
                } else {
                    mapValueHolderV14 = refer.queryLogisticsByEcodes(requestList);
                }
                if (mapValueHolderV14 != null && mapValueHolderV14.getData() != null) {
                    cpLogistics = mapValueHolderV14.getData().get(cpLogisticsEcode);
                    if (null != cpLogistics) {
                        if (log.isDebugEnabled()) {
                            log.debug(cpLogisticsEcode + "插入Redis:" + JSONObject.toJSONString(cpLogistics));
                        }
                        redisTemplate.opsForValue().set(redisKey, JSONObject.toJSONString(cpLogistics));
                    }
                    if (log.isDebugEnabled()) {
                        log.debug(cpLogisticsEcode + "查询物流公司数据返回:" + JSONObject.toJSONString(cpLogistics));
                    }
                }
            } else {
                cpLogistics = JSONObject.parseObject(s, CpLogistics.class);
            }
        } catch (Exception e) {
            log.error("查询物流公司信息失败！error:{}", e.getMessage());
        }
        return cpLogistics;
    }

    /**
     * 根据TMS物流公司编码获取物流档案
     *
     * @param cpLogisticsEcode 物流公司编码
     * @return CpLogistics
     */
    public ValueHolderV14<CpLogisticsAndItemResult> queryCpLogisticsAndItemByLogisticsEcode(String cpLogisticsEcode) {
        if (log.isDebugEnabled()) {
            log.debug("BasicCpQueryService.queryCpLogisticsAndItemByLogisticsEcode.tmsLogisticsEcode:{}", cpLogisticsEcode);
        }
        String redisKey = null;
        redisKey = String.format("%sEcode:%s", RedisKeyConstans.CP_LOGISTICS_ID_KEY, cpLogisticsEcode);
        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
        CpLogistics cpLogistics = null;

        CpLogisticsQueryCmd refer = (CpLogisticsQueryCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(),
                CpLogisticsQueryCmd.class.getName(), "cp-ext", "1.0");

        try {
            String s = redisTemplate.opsForValue().get(redisKey);
            if (org.springframework.util.StringUtils.isEmpty(s)) {
                List<String> requestList = new ArrayList<>();
                requestList.add(cpLogisticsEcode);
                ValueHolderV14<Map<String, CpLogistics>> mapValueHolderV14 = null;

                mapValueHolderV14 = refer.queryLogisticsByEcodes(requestList);
                if (mapValueHolderV14 != null && mapValueHolderV14.getData() != null) {
                    cpLogistics = mapValueHolderV14.getData().get(cpLogisticsEcode);
                    if (null != cpLogistics) {
                        if (log.isDebugEnabled()) {
                            log.debug(cpLogisticsEcode + "插入Redis:" + JSONObject.toJSONString(cpLogistics));
                        }
                        redisTemplate.opsForValue().set(redisKey, JSONObject.toJSONString(cpLogistics));
                    }
                    if (log.isDebugEnabled()) {
                        log.debug(cpLogisticsEcode + "查询物流公司数据返回:" + JSONObject.toJSONString(cpLogistics));
                    }
                }
            } else {
                cpLogistics = JSONObject.parseObject(s, CpLogistics.class);
            }
        } catch (Exception e) {
            log.error("查询物流公司信息失败！error:{}", e.getMessage());
        }

        if (cpLogistics == null) {
            return new ValueHolderV14<>(ResultCode.FAIL, "查询失败");
        }
        ValueHolderV14<List<CpLogisticsItem>> itemVH = refer.queryLogisticsItemByIds(cpLogistics.getId());
        if (!itemVH.isOK()) {
            return new ValueHolderV14<>(ResultCode.FAIL, "查询明细失败");
        }

        CpLogisticsAndItemResult data = new CpLogisticsAndItemResult();
        data.setCpLogistics(cpLogistics);
        data.setCpLogisticsItemList(itemVH.getData());

        if (log.isDebugEnabled()) {
            log.debug("BasicCpQueryService.queryCpLogisticsAndItemByLogisticsEcode.result:{}", JSONObject.toJSONString(data));
        }
        return new ValueHolderV14<>(data, ResultCode.SUCCESS, "success");
    }

    /**
     * 根据成本中心的ID查询成本中心
     *
     * @param request
     * @return
     */
    public HashMap<Long, CpCostCenter> getCpCostCenterInfo(CpCostCenterRequest request) {
        HashMap<Long, CpCostCenter> costCenterMap = new HashMap(16);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start.BasicPsQueryService.getCpCostCenterInfo.request：{}"),
                    JSONObject.toJSONString(request));
        }
        //检查入参
        if (null == request || null == request.getCpCostCenterId()) {
            return costCenterMap;
        }
        try {
            CpCostCenter cpCostCenter = new CpCostCenter();
            CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
            Object costCenter = redisTemplate.opsForValue().get(BasicConstants.CP_COST_CENTER_ID_KEY + request.getCpCostCenterId());
            if (null != costCenter) {
                cpCostCenter = JSONObject.parseObject(costCenter.toString(), CpCostCenter.class);
            } else {
                // RPC 查询
                CpCostCenterQueryCmd cpcStoreUserQueryCmd = (CpCostCenterQueryCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(),
                        CpCostCenterQueryCmd.class.getName(), "cp", "1.0");
                CpCostCenterQueryCmdRequest cpCostCenterQueryCmdRequest = new CpCostCenterQueryCmdRequest();
                cpCostCenterQueryCmdRequest.setId(request.getCpCostCenterId());
                ValueHolder valueHolder = cpcStoreUserQueryCmd.execute(cpCostCenterQueryCmdRequest);
                if (valueHolder != null && valueHolder.getData() != null && valueHolder.getData().get("data") != null) {
                    cpCostCenter = (CpCostCenter) valueHolder.getData().get("data");
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("Finish.BasicCpQueryService.getCpCostCenterInfo.ReturnResult：")
                                + JSONObject.toJSONString(cpCostCenter));
                    }
                    //将数据插入redis
                    redisTemplate.opsForValue().set(BasicConstants.CP_COST_CENTER_ID_KEY + request.getCpCostCenterId(), JSONObject.toJSONString(cpCostCenter), 1, TimeUnit.DAYS);
                }
            }
            if (cpCostCenter != null) {
                costCenterMap.put(request.getCpCostCenterId(), cpCostCenter);
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("BasicCpQueryService.getCpCostCenterInfo.error：{}"),
                    Throwables.getStackTraceAsString(ex));
        }
        return costCenterMap;
    }

}
