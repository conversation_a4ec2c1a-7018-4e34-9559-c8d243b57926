package com.jackrain.nea.data.basic.services;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.common.ReferenceUtil;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.data.basic.common.BasicConstants;
import com.jackrain.nea.data.basic.model.request.EwayBillQueryRequest;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.api.StrategyCenterCmd;
import com.jackrain.nea.st.model.result.EwayBillResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;


@Component
@Slf4j
public class BasicStQueryService {


    /**
     * 根据店铺id及物流公司id获取电子面单信息接口
     *
     * @param request
     * @return
     */
    public Map<String, EwayBillResult> getEwayBillByShopId(List<EwayBillQueryRequest> request) {


        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start BasicStQueryService.getEwayBillByShopId. request={};"),
                    JSONObject.toJSONString(request));
        }

        //初始化返回值
        Map<String, EwayBillResult> ewayBillMap = new HashMap<>();
        Map<String, EwayBillQueryRequest> ewayBillQueryMap = new HashMap<>();
        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
        ValueHolderV14<EwayBillResult> resultHolder;
        EwayBillResult ewayBillResult;
        String subKey = "";

        //检查入参
        if (CollectionUtils.isEmpty(request)) {
            return ewayBillMap;
        }

        try {

            for (EwayBillQueryRequest requestItem : request) {

                subKey = requestItem.getShopId() + "_" + requestItem.getLogiscId();

                Object ewayInfo = redisTemplate.opsForHash().get(BasicConstants.ST_C_EWAYBILL_SHOP_ID_KEY,
                        subKey);

                if (ewayInfo != null) {
                    ewayBillMap.put(subKey, JSONObject.parseObject(ewayInfo.toString(), EwayBillResult.class));
                } else {
                    ewayBillQueryMap.put(subKey, requestItem);
                }
            }

            if (ewayBillQueryMap.size() > 0) {

                StrategyCenterCmd strategyCenterCmd = (StrategyCenterCmd) ReferenceUtil.refer(
                        ApplicationContextHandle.getApplicationContext(), StrategyCenterCmd.class.getName(),
                        "st", "1.0");

                for (EwayBillQueryRequest item : ewayBillQueryMap.values()) {

                    resultHolder = strategyCenterCmd.queryEwayBillByshopId(item.getShopId(), item.getLogiscId());

                    if (resultHolder != null && ResultCode.SUCCESS == resultHolder.getCode()) {

                        ewayBillResult = resultHolder.getData();

                        if (ewayBillResult != null) {

                            subKey = item.getShopId() + "_" + item.getLogiscId();

                            redisTemplate.opsForHash().put(BasicConstants.ST_C_EWAYBILL_SHOP_ID_KEY,
                                    subKey,
                                    JSONObject.toJSONString(ewayBillResult));

                            redisTemplate.expire(BasicConstants.ST_C_EWAYBILL_SHOP_ID_KEY, 1, TimeUnit.DAYS);

                            ewayBillMap.put(subKey, ewayBillResult);
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error(LogUtil.format("BasicStQueryService.getEwayBillByShopId.Exception：{}"),
                    Throwables.getStackTraceAsString(e));
        }

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Finish BasicStQueryService.getEwayBillByShopId. result size ={};") + ewayBillMap.size());
        }

        return ewayBillMap;

    }

}
