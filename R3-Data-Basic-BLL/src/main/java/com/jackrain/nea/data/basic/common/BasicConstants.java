package com.jackrain.nea.data.basic.common;

/**
 * @Description:
 * @Author: chenb
 * @Date: 2019/3/25 16:21
 */
public interface BasicConstants {

    String CP_PRO_ID_KEY = "PS:PS_C_PRO_ID:";

    String CP_PRO_ECODE_KEY = "PS:PS_C_PRO_ECODE:";

    String CP_SKU_ID_KEY = "PS:PS_C_SKU_ID:";

    String CP_SKU_ECODE_KEY = "PS:PS_C_SKU_ECODE:";

    String CP_REG_KEY = "CP:CP_C_REGION:";

    String CP_STO_KEY = "CP:CP_C_STORE:";

    String CP_PHY_KEY = "CP:CP_C_STORE_PHYID:";

    String CP_STO_ECODE_KEY = "CP:CP_C_STORE_ECODE:";

    String CP_SUP_KEY = "CP:CP_C_SUPPLIER:";

    String CP_CUSTOMER_ID_KEY = "CP:CP_C_CUSTOMER_ID:";

    String CP_STO_USERID = "CP:CP_C_STORE_USERID:";

    String CP_C_TOWMS_ITEMID_KEY = "PS:PS_C_TOWMS:";

    String CP_SHOP_ID_KEY = "CP:CP_C_SHOP_ID:";

    String CP_COST_CENTER_ID_KEY = "CP:CP_COST_CENTER_ID:";

    String PS_C_PRODIM_ITEM = "PS:PRODIMITEM:";

    String ST_C_EWAYBILL_SHOP_ID_KEY = "ST:ST_C_EWAYBILL_SHOP_ID";

    String CP_USER_NAME_KEY = "CP:USER_NAME:";


    String PS_C_SHAPEGROUP_KEY = "PS:PS_C_SHAPEGROUP_PROECODE:";

    String PS_C_MATCHSIZE_ITEM_KEY = "PS:PS_C_MATCHSIZE_ITEM_MATCHSIZEID:";

    String CP_C_LOGISTICS_WITH_ITEMS_ID_KEY = "CP:CP_C_LOGISTICS_WITH_ITEMS_ID:";

    String LIMITED_NUMBER_OF_LUCKY_BAGS_KEY = "business_system:limited_number_of_lucky_bags";

    // @2021 BUG编号 31021
    String REDIS_PS_PRODUCT_SKU = "ps:product:sku:";

    String REDIS_PS_PRODUCT_SKUID = "ps:product:skuId:";

    /**
     * 平台条码与系统条码映射
     */
    String REDIS_PS_PRODUCT_SKUFORCODE = "ps:product:skuforcode:";

    // 第三方条码ducode维度
    String PS_C_THIRD_ITEM_DUCODE_KEY = "PS:PS_C_THIRD_ITEM_DUCODE:";
}
