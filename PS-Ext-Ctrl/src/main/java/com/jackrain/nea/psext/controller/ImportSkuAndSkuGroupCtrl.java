package com.jackrain.nea.psext.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.core.schema.Column;
import com.jackrain.nea.core.schema.Table;
import com.jackrain.nea.core.schema.TableManager;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.psext.request.RealSkuImportRequest;
import com.jackrain.nea.psext.services.SkuGroupDownloadTempCmdImpl;
import com.jackrain.nea.psext.services.SkuGroupQueryService;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import com.jackrain.nea.web.query.QuerySession;
import com.jackrain.nea.web.query.QueryUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Api(value = "组合商品导入", description = "组合商品导入")
@RestController
@Slf4j
public class ImportSkuAndSkuGroupCtrl {
    @Autowired
    private SkuGroupDownloadTempCmdImpl skuGroupDownloadTempCmd;
    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;
    public static final String cellStr = "cell_";
    public static final String rowStr = "row_";

    //CellType
    public static final int _NONE = -1;
    public static final int NUMERIC = 0;
    public static final int STRING = 1;
    public static final int FORMULA = 2;
    public static final int BLANK = 3;
    public static final int BOOLEAN = 4;
    public static final int ERROR = 5;

    private static NumberFormat nf = NumberFormat.getInstance();
    @Autowired
    private SkuGroupQueryService queryService;

    /**
     * 下载模板
     *
     * @return string str
     * @throws Exception ex
     */
    @ApiOperation(value = "实际条码导入下载模板")
    @RequestMapping(path = "/api/cs/ps-ext/product/downloadRealSkuGroupTemp", method = RequestMethod.POST)
    public ValueHolderV14 downloadRealSkuTemp(HttpServletRequest request, @RequestBody JSONObject jsonObject)
            throws Exception {
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        QuerySession querySession = QueryUtils.createQuerySession(user);
//        User user = getRootUser();
        Long type = Long.valueOf(jsonObject.get("type").toString());
        //处理返回数据
        ValueHolderV14 holderV14 = skuGroupDownloadTempCmd.downloadRealSkuTemp(type, user);
        return holderV14;
    }

    @ApiOperation(value = "实际条码导入")
    @RequestMapping(path = "/api/cs/ps-ext/product/realSkuGroupImport", method = RequestMethod.POST)
    public ValueHolderV14 importRealSku(HttpServletRequest request, @RequestParam(value = "file", required = true) MultipartFile file, @RequestParam(value = "type", required = true) String type) throws IOException {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
//        User user = getRootUser();
        log.info("##ImportSkuAndSkuGroupCtrl##importRealSku##fileName：{}", file.getOriginalFilename());
        Long groupType = Long.valueOf(type);
        //1.传入数据校验
        if (file == null) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!"));
        }
        InputStream inputStream = null;
        Map<String, InputStream> inputStreamMap = new HashMap<>();
        try {
            inputStream = file.getInputStream();
            inputStreamMap.put("inputStream", inputStream);
            log.info(this.getClass().getName() + " File Conversion InputStream：" + inputStream);
        } catch (Exception e) {
            log.error("File Conversion InputStream Fail：" + e.getMessage());
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("文件转换成流失败!");
            return holderV14;
        }
        //2.解析Excel
        Workbook hssfWorkbook = null;
        try {
            hssfWorkbook = getWorkbookForImportFile(inputStream, file);
        } catch (IOException e) {
            log.error("条码导入解析Excel失败！错误信息为：" + e.getMessage());
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("文件解析Excel失败!");
            return holderV14;
        } finally {
            inputStream.close();
        }

        List<Map<String, String>> execlList = null;
        try {
            execlList = readExcel(0, hssfWorkbook);
            if (execlList == null) {
                throw new NDSException("读取Eexel文件失败");
            }
        } catch (Exception e) {
            log.error("##ImportSkuAndSkuGroupCtrl##importRealSku##条码导入Excel文件失败！Error：", e);
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage(e.getMessage());
            return holderV14;
        }
        //获取sheet页数
        log.info(this.getClass().getName() + " =========>>>>>>[llf]获取sheet页数！");
        int sheetNum = hssfWorkbook.getNumberOfSheets();
        if (sheetNum != 1) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("商品导入模板不正确!");
            return holderV14;
        }

        List<RealSkuImportRequest> realSkuImportRequests = getSkuGroupList(hssfWorkbook, groupType);
        log.info("##ImportSkuAndSkuGroupCtrl##importRealSku##realSkuImportRequests:{}", realSkuImportRequests);
        return skuGroupDownloadTempCmd.importRealSku(realSkuImportRequests, user, groupType);
    }

    public List<RealSkuImportRequest> getSkuGroupList(Workbook hssfWorkbook, Long groupType) {
        log.info("##ImportSkuAndSkuGroupCtrl##importRealSku##getSkuGroupList");
        List<RealSkuImportRequest> realSkuImportRequests = Lists.newArrayList();
        List<Map<String, String>> execlList = Lists.newArrayList();
        try {
            execlList = readExcel(0, hssfWorkbook);
        } catch (Exception e) {
            log.error("##ImportSkuAndSkuGroupCtrl##importRealSku##getSkuGroupList Error：", e);
        }
        int index = 0;
        for (Map<String, String> columnMap : execlList) {
            RealSkuImportRequest realSkuImportRequest = new RealSkuImportRequest();
            if (index == 0) {
                // 校验excel字段
                if (groupType == 1) {
                    if (columnMap.size() != 9
                            || !"条码".equals(columnMap.get(rowStr + index + cellStr + 0))
                            || !"吊牌价".equals(columnMap.get(rowStr + index + cellStr + 1))
                            || !"商品编码".equals(columnMap.get(rowStr + index + cellStr + 2))
                            || !"商品名称".equals(columnMap.get(rowStr + index + cellStr + 3))
                            || !"规格".equals(columnMap.get(rowStr + index + cellStr + 4))
                            || !"是否赠品".equals(columnMap.get(rowStr + index + cellStr + 5))
                            || !"组合数量".equals(columnMap.get(rowStr + index + cellStr + 6))
                            || !"占比".equals(columnMap.get(rowStr + index + cellStr + 7))
                            || !"分组".equals(columnMap.get(rowStr + index + cellStr + 8))
                    ) {
                        return Lists.newArrayList();
                    }
                } else if (groupType == 2) {
                    if (columnMap.size() != 8
                            || !"条码".equals(columnMap.get(rowStr + index + cellStr + 0))
                            || !"吊牌价".equals(columnMap.get(rowStr + index + cellStr + 1))
                            || !"商品编码".equals(columnMap.get(rowStr + index + cellStr + 2))
                            || !"商品名称".equals(columnMap.get(rowStr + index + cellStr + 3))
                            || !"规格".equals(columnMap.get(rowStr + index + cellStr + 4))
                            || !"是否赠品".equals(columnMap.get(rowStr + index + cellStr + 5))
                            || !"组合数量".equals(columnMap.get(rowStr + index + cellStr + 6))
                            || !"占比".equals(columnMap.get(rowStr + index + cellStr + 7))
                    ) {
                        return Lists.newArrayList();
                    }
                }
            } else {
                realSkuImportRequests.add(importCreate(index, realSkuImportRequest, columnMap, groupType));
            }
            index++;
        }
        return realSkuImportRequests;
    }

    /**
     * 导入生成模型
     *
     * @return
     */
    public static RealSkuImportRequest importCreate(int index, RealSkuImportRequest realSkuImportRequest, Map<String, String> columnMap, Long groupType) {
        log.info("##ImportSkuAndSkuGroupCtrl##importRealSku##importCreate");
        //条码
        realSkuImportRequest.setPsCSkuEcode(columnMap.get(rowStr + index + cellStr + 0));
        //吊牌价
        realSkuImportRequest.setPricelist(new BigDecimal(columnMap.get(rowStr + index + cellStr + 1)));
        //商品编码
        realSkuImportRequest.setPsCProEcode(columnMap.get(rowStr + index + cellStr + 2));
        //商品名称
        realSkuImportRequest.setPsCProEname(columnMap.get(rowStr + index + cellStr + 3));
        //规格
        realSkuImportRequest.setSpec(columnMap.get(rowStr + index + cellStr + 4));
        //是否赠品
        realSkuImportRequest.setIsGift(columnMap.get(rowStr + index + cellStr + 5));
        //组合数量
        realSkuImportRequest.setNum(new BigDecimal(columnMap.get(rowStr + index + cellStr + 6)));
        //占比
        realSkuImportRequest.setPsCClrEname(columnMap.get(rowStr + index + cellStr + 7));
        if (groupType == 1) {
            //分组
            if (columnMap.get(rowStr + index + cellStr + 8) == null) {
                realSkuImportRequest.setGroupNum(null);
            } else {
                realSkuImportRequest.setGroupNum(Integer.valueOf(columnMap.get(rowStr + index + cellStr + 8)));
            }
        } else {
            realSkuImportRequest.setGroupNum(null);
        }
        return realSkuImportRequest;
    }

    /**
     * 导入方法，
     *
     * @param hssfWorkbook
     * @return
     * @throws Exception
     */
    public List<Map<String, String>> readExcel(Integer sheetIndex, Workbook hssfWorkbook) throws Exception {
        List list = Lists.newArrayList();
        Sheet hssfSheet = hssfWorkbook.getSheetAt(sheetIndex);
        if (Objects.isNull(hssfSheet)) {
            return Lists.newArrayList();
        }

        for (int rowNum = 0; rowNum <= hssfSheet.getLastRowNum(); rowNum++) {
            Map<String, String> map = Maps.newTreeMap();
            Row hssfRow = hssfSheet.getRow(rowNum);
            if (hssfRow != null) {
                Iterator<Cell> cellItr = hssfRow.cellIterator();
                while (cellItr.hasNext()) {
                    Cell cell = cellItr.next();
                    map.put(rowStr + cell.getRowIndex() + cellStr + cell.getColumnIndex(), getCellValue(cell));
                }
            }
            list.add(map);
        }
        return list;
    }

    private static String getCellValue(Cell cell) {
        int cellType = cell.getCellType();
        String cellValue;
        switch (cellType) {
            case NUMERIC:
                cellValue = nf.format(cell.getNumericCellValue());
                if (cellValue.indexOf(",") >= 0) {
                    cellValue = cellValue.replace(",", "");
                }
                break;
            case FORMULA:
                try {
                    cellValue = cell.getStringCellValue();
                } catch (IllegalStateException e) {
                    cellValue = String.valueOf(cell.getNumericCellValue());
                }
                break;

            default:
                cellValue = cell.getStringCellValue();
        }

        return cellValue.trim();
    }

    /**
     * @param inputStream
     * @param file
     * @return org.apache.poi.ss.usermodel.Workbook
     * @Description 分版本处理Excel数据
     * <AUTHOR>
     * @date 2019/9/9 15:05
     */
    private Workbook getWorkbookForImportFile(InputStream inputStream, MultipartFile file) throws IOException {
        Workbook workbook = null;
        String fileName = file.getName();
        if (fileName.toLowerCase().endsWith("xls")) {
            workbook = new HSSFWorkbook(inputStream);
        } else {
            workbook = new XSSFWorkbook(inputStream);
        }
        return workbook;
    }

    private User getRootUser() {
        UserImpl user = new UserImpl();
        user.setId(893);
        user.setName("admin");
        user.setEname("Pokemon-mapper");
        user.setActive(true);
        user.setClientId(37);
        user.setOrgId(27);
        user.setIsAdmin(2);
        user.setIsDev(2);
        return user;
    }

    /**
     * 组合商品导出
     *
     * @return string str
     * @throws Exception ex
     */
    @ApiOperation(value = "组合商品导出")
    @RequestMapping(path = "/api/cs/ps-ext/product/exportSkuGroup", method = RequestMethod.POST)
    public ValueHolderV14 exportSkuGroup(HttpServletRequest request,
                                         @RequestParam(value = "ids", required = true) String ids) {


//        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "组合商品导出成功！");

//        if (StringUtils.isBlank(ids)) {
//            v14.setCode(ResultCode.FAIL);
//            v14.setMessage("请选择需要导出的数据");
//            return v14;
//        }

        return skuGroupDownloadTempCmd.exportSkuGroup(ids);

//        return v14;
    }


    /**
     * 组合商品查询
     *
     * @return string obj
     * @throws Exception ex
     */
    @ApiOperation(value = "组合商品查询")
    @RequestMapping(path = "/api/cs/ps-ext/QueryList", method = RequestMethod.POST)
    public JSONObject queryList(HttpServletRequest request,
                                @RequestParam(value = "searchdata") String obj) {

        log.info(LogUtil.format("组合商品查找开始", "queryList"));

        JSONObject jo = JSON.parseObject(obj);
        User userWeb = (User) request.getSession().getAttribute("user");
        if (userWeb == null) {
            userWeb = SystemUserResource.getRootUser();
        }
        QuerySession qsession = QueryUtils.createQuerySession(userWeb);
        TableManager tm = qsession.getTableManager();

        Table table = null;

        int tableId = jo.getIntValue("tableid");
        int refColumnId = jo.getIntValue("refcolid");

        if (refColumnId > 0) {
            Column refColumn = tm.getColumn(refColumnId);
            if (refColumn != null) {
                table = refColumn.getReferenceTable();
            }
        }

        if (tableId > 0 && table == null) {
            table = tm.getTable(tableId);
        }

        if (jo.getString("table") != null && table == null) {
            table = tm.getTable(jo.getString("table"));
        }

        if (table == null) {
            throw new NDSException("Cannot get Table. Table is Null!");
        }

        String center = table.getCategory().getSubSystem().getCenter();
        String[] gv = center.split(":");
        if (gv.length != 2) {
            throw new NDSException("SubSystem.Center Is Error! Center.Value=" + center);
        }
        ValueHolder tableQuery = queryService.getTableQuery(userWeb, jo);

        return tableQuery.toJSONObject();
    }

}