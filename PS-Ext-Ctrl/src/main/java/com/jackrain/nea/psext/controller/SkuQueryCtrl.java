package com.jackrain.nea.psext.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.ps.api.table.PsCProdimItem;
import com.jackrain.nea.psext.api.PsCSkuPushWmsMidQueryCmd;
import com.jackrain.nea.psext.api.SkuLikeQueryCmd;
import com.jackrain.nea.psext.request.PsToWmsRequest;
import com.jackrain.nea.psext.request.SkuQueryRequest;
import com.jackrain.nea.psext.result.PsCSkuPushWmsMidResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;

/**
 * @program: r3-ps
 * @author: Lijp
 * @create: 2019-03-15 16:27
 */
@Api(value = "商品信息查询", description = "商品信息查询")
@RestController
@Slf4j
public class SkuQueryCtrl {

    @Autowired
    private SkuLikeQueryCmd skuLikeQueryCmd;
    @Autowired
    private PsCSkuPushWmsMidQueryCmd psCSkuPushWmsMidQueryCmd;

    @RequestMapping(path = "/api/cs/ps-ext/skuQuery", method = RequestMethod.POST)
    public JSONObject skuQuery(HttpServletRequest request, @RequestBody JSONObject jsonObject) throws Exception {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format(" skuQueryReceive：") + jsonObject);
        }
        ValueHolderV14<HashMap> result = new ValueHolderV14<>();
        result.setCode(ResultCode.SUCCESS);
        result.setMessage("获取成功！");
        SkuQueryRequest skuQueryRequest = jsonObject.toJavaObject(SkuQueryRequest.class);
        //  Map map2 = skuLikeQueryCmd.queryCount(skuQueryRequest.getPsCSku().getEcode());
        HashMap map = skuLikeQueryCmd.querySku(skuQueryRequest);
        result.setData(map);
        return JSONObject.parseObject(JSONObject.toJSONString(result.toJSONObject(), SerializerFeature.WriteMapNullValue));
    }

    @RequestMapping(path = "/api/cs/ps-ext/skuQuer11", method = RequestMethod.POST)
    public JSONObject skuQuery11(HttpServletRequest request, @RequestBody JSONObject jsonObject) throws Exception {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format(" skuQueryReceive：") + jsonObject);
        }
        PsToWmsRequest request1 = new PsToWmsRequest();
        request1.setCustomerId("C1639991137308");
        request1.setSku("8975XS");
        ValueHolderV14<PsCSkuPushWmsMidResult> psCSkuPushWmsMidResultValueHolderV14 = psCSkuPushWmsMidQueryCmd.querySkuPushWmsMidByPsCSKuECode(request1);
        System.out.printf(JSON.toJSONString(psCSkuPushWmsMidResultValueHolderV14));
        return JSONObject.parseObject(JSONObject.toJSONString(psCSkuPushWmsMidResultValueHolderV14.toJSONObject(), SerializerFeature.WriteMapNullValue));
    }

    @RequestMapping(path = "/api/cs/ps-ext/queryProdimCtrl", method = RequestMethod.POST)
    public JSONObject queryProdimCtrl(@RequestParam(value = "id", required = true) Long id) {
        ValueHolderV14<PsCProdimItem> result = skuLikeQueryCmd.queryProdimItem(id);
        return JSONObject.parseObject(JSONObject.toJSONString(result.toJSONObject(), SerializerFeature.WriteMapNullValue));
    }
}
