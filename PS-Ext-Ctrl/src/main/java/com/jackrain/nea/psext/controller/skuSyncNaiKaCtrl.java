package com.jackrain.nea.psext.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.psext.api.PsCSkuUploadNaiKaCmd;
import com.jackrain.nea.psext.services.PsCSkuPushToMidServiceTask;
import com.jackrain.nea.psext.services.PsCSkuUploadNaiKaService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySessionImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @Date 2022/7/6 14:49
 * @Description
 */
@Api(value = "商品信息推送奶卡", description = "商品信息推送奶卡")
@RestController
@Slf4j
public class skuSyncNaiKaCtrl {

    @Reference(group = "ps-ext", version = "1.0")
    private PsCSkuUploadNaiKaCmd psCSkuUploadNaiKaCmd;

    @Autowired
    private PsCSkuUploadNaiKaService psCSkuUploadNaiKaService;

    @Autowired
    private PsCSkuPushToMidServiceTask psCSkuPushToMidServiceTask;


    @ApiOperation(value = "商品同步奶卡系统状态重置")
    @RequestMapping(path = "/api/cs/update/status", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder updateStatus(HttpServletRequest request, @RequestBody JSONObject param) {
        User user = getRootUser();
//        User user = (UserImpl) request.getSession().getAttribute("user");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", param);
        querySession.setEvent(event);
        return psCSkuUploadNaiKaCmd.execute(querySession);
    }

    @ApiOperation(value = "定时任务测试")
    @RequestMapping(path = "/p/cs/push/naika", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolderV14 pushNaiKa() {
        return psCSkuUploadNaiKaService.pushSkuToNaiKa();
    }

    @RequestMapping(path = "/p/cs/push/mid", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolderV14 pushSkuToMid() {
        return psCSkuPushToMidServiceTask.pushItemToMidTask();
    }

    /**
     * 测试用户
     */
    private User getRootUser() {
        UserImpl user = new UserImpl();
        user.setId(893);
        user.setName("测试用户");
        user.setEname("test");
        user.setActive(true);
        user.setClientId(37);
        user.setOrgId(27);
        user.setIsAdmin(2);
        user.setIsDev(2);
        return user;
    }
}
