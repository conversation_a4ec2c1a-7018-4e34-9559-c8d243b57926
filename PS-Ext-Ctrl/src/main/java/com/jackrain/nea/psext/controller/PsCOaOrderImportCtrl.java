package com.jackrain.nea.psext.controller;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSSClient;
import com.google.common.collect.Lists;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.psext.common.PsExtConstantsIF;
import com.jackrain.nea.psext.mapper.CproSkuGroupMapper;
import com.jackrain.nea.psext.mapper.PsCProGroupMapper;
import com.jackrain.nea.psext.model.table.PsCOaOrderFileImport;
import com.jackrain.nea.psext.model.table.PsCProGroup;
import com.jackrain.nea.psext.model.table.PsCSkugroup;
import com.jackrain.nea.psext.utils.ExportUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

import static org.apache.poi.ss.usermodel.Cell.CELL_TYPE_NUMERIC;
import static org.apache.poi.ss.usermodel.Cell.CELL_TYPE_STRING;

/**
 * @ClassName PsCOaOrderImportCtrl
 * @Description OA订单导入控制器
 * <AUTHOR>
 * @Date 2025/5/27 15:27
 * @Version 1.0
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/cs/ps-ext")
public class PsCOaOrderImportCtrl {

    private static final NumberFormat nf = NumberFormat.getInstance();
    // Z开头的物流编码正则表达式（大小写不敏感）
    private static final Pattern Z_PATTERN = Pattern.compile("^[Zz].*");
    @Autowired
    private CproSkuGroupMapper cproSkuGroupMapper;
    @Autowired
    private PsCProGroupMapper psCProGroupMapper;
    @Autowired
    private ExportUtil exportUtil;
    @Autowired
    private PropertiesConf pconf;
    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    /**
     * 下载模板
     *
     * @return string str
     * @throws Exception ex
     */
    @ApiOperation(value = "下载模板")
    @RequestMapping(path = "/oa/order/downloadOrderTemp", method = RequestMethod.GET)
    public ValueHolderV14 downloadTemp(HttpServletRequest request)
            throws Exception {
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        ValueHolderV14 holderV14 = new ValueHolderV14(ResultCode.SUCCESS, "OA订单导入模板下载成功！");
        // 配置OSS参数
        String endpoint = pconf.getProperty(PsExtConstantsIF.ENDPOINT_KEY);
        String accessKeyId = pconf.getProperty(PsExtConstantsIF.ACCESS_KEY);
        String accessKeySecret = pconf.getProperty(PsExtConstantsIF.SECRET_KEY);
        String bucketName = pconf.getProperty(PsExtConstantsIF.BUCKET_NAME);
        String timeout = pconf.getProperty(PsExtConstantsIF.TIMEOUT);

        exportUtil.setEndpoint(endpoint);
        exportUtil.setAccessKeyId(accessKeyId);
        exportUtil.setAccessKeySecret(accessKeySecret);
        exportUtil.setBucketName(bucketName);

        if (StringUtils.isEmpty(timeout)) {
            //如果获取不到配置参数，设置默认过期时间为30分钟
            timeout = "1800000";
        }
        exportUtil.setTimeout(timeout);

        /**
         * 拼接Excel表头字段：OA订单导入模板
         */
        String orderNames[] = {"序号", "订单编号-区分送货##bh", "客户名称##KUNAG", "SAP客户编码##KUNWE", "电话##TEL_NUMBER",
                "联系人##NAME_CO", "综合地址信息##zhdzxx", "省代码##REGION", "省##s", "市##CITY", "区##DISTRICT",
                "地址信息##STREET", "物料号##MATNR", "物料名称##TXZ01", "数量##KWMENG", "成本价##KBETR", "合计金额##hjje",
                "销售单位##VRKME", "备注（不要写数字）##TEXT", "工厂##bukrs", "指定效期上限（早）##zdxqsx", "指定效期下限（晚）##zdxqxx",
                "品相##px", "成本中心##cbzx", "补发原始单号##bfysdh", "补发原因##bfyy", "价格单位##jgdw"};

        List orderN = Lists.newArrayList(orderNames);

        //生成Excel (xls格式)
        HSSFWorkbook hssfWorkbook = createXlsTemplate(orderN);
        String sdd = saveXlsFileAndPutOss(hssfWorkbook, "OA订单导入模板", user, "OSS-Bucket/EXPORT/OA_ORDER/");

        holderV14.setData(sdd);
        return holderV14;
    }

    @RequestMapping(path = "/oa/order/import", method = RequestMethod.POST)
    public JSONObject orderImport(HttpServletRequest request,
                                 @RequestParam(value = "table", required = true) String table,
                                 @RequestParam(value = "file", required = true) MultipartFile file) {
        JSONObject result = new JSONObject();

        try {
            // 获取当前用户信息
            User currentUser = r3PrimWebAuthService.getLoginPrimWebUser(request);

            // 1. 解析Excel文件
            List<Map<String, Object>> excelDataList = parseExcelFile(file);
            if (CollectionUtils.isEmpty(excelDataList)) {
                result.put("success", false);
                result.put("message", "Excel文件为空或格式不正确");
                return result;
            }

            // 2. 检查是否有Z开头的物流编码
            List<String> zMaterialCodes = extractZMaterialCodes(excelDataList);
            if (CollectionUtils.isEmpty(zMaterialCodes)) {
                // 没有Z开头的数据，直接转换为导出文件
                String exportFileUrl = convertToExportFile(excelDataList, currentUser, "无Z开头物流编码");

                // 记录导入信息
                recordImportInfo(file, exportFileUrl, "解析完成，无Z开头物流编码", currentUser);

                result.put("success", true);
                result.put("message", "处理完成，无Z开头物流编码需要处理");
                result.put("exportFileUrl", exportFileUrl);
                return result;
            }

            // 3. 查询组合商品信息
            Map<String, List<PsCSkugroup>> groupProductMap = queryGroupProducts(zMaterialCodes);

            // 4. 检查是否所有组合商品都存在
            List<String> missingProducts = findMissingProducts(zMaterialCodes, groupProductMap);
            if (CollectionUtils.isNotEmpty(missingProducts)) {
                // 生成错误文件
                String errorFileUrl = generateErrorFile(excelDataList, missingProducts, currentUser);

                // 记录导入信息
                recordImportInfo(file, errorFileUrl, "存在不存在的组合商品：" + String.join(",", missingProducts), currentUser);

                result.put("success", false);
                result.put("message", "存在不存在的组合商品");
                result.put("errorFileUrl", errorFileUrl);
                result.put("missingProducts", missingProducts);
                return result;
            }

            // 5. 展开组合商品为多行数据
            List<Map<String, Object>> expandedDataList = expandGroupProducts(excelDataList, groupProductMap);

            // 6. 生成导出文件
            String exportFileUrl = convertToExportFile(expandedDataList, currentUser, "组合商品展开成功");

            // 7. 记录导入信息
            recordImportInfo(file, exportFileUrl, "处理成功，共展开" + expandedDataList.size() + "行数据", currentUser);

            result.put("success", true);
            result.put("message", "处理成功");
            result.put("exportFileUrl", exportFileUrl);
            result.put("originalCount", excelDataList.size());
            result.put("expandedCount", expandedDataList.size());

        } catch (Exception e) {
            log.error("订单导入处理失败", e);
            result.put("success", false);
            result.put("message", "处理失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 解析Excel文件
     */
    private List<Map<String, Object>> parseExcelFile(MultipartFile file) throws Exception {
        List<Map<String, Object>> dataList = new ArrayList<>();

        try (InputStream inputStream = file.getInputStream()) {
            Workbook workbook = new XSSFWorkbook(inputStream);
            Sheet sheet = workbook.getSheetAt(0);

            if (sheet.getLastRowNum() < 1) {
                return dataList;
            }

            // 读取表头
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) {
                return dataList;
            }

            Map<String, Integer> headerMap = new HashMap<>();
            for (Cell cell : headerRow) {
                String headerValue = getCellValue(cell);
                if (StringUtils.isNotBlank(headerValue)) {
                    headerMap.put(headerValue.trim(), cell.getColumnIndex());
                }
            }

            // 读取数据行
            for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row == null) {
                    continue;
                }

                Map<String, Object> rowData = new HashMap<>();
                rowData.put("rowIndex", rowIndex + 1); // 行号从1开始

                for (Map.Entry<String, Integer> entry : headerMap.entrySet()) {
                    String columnName = entry.getKey();
                    Integer columnIndex = entry.getValue();

                    Cell cell = row.getCell(columnIndex);
                    String cellValue = cell != null ? getCellValue(cell) : "";
                    rowData.put(columnName, cellValue);
                }

                dataList.add(rowData);
            }
        }

        return dataList;
    }

    /**
     * 获取单元格值
     */
    private String getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case CELL_TYPE_STRING:
                return cell.getStringCellValue();
            case CELL_TYPE_NUMERIC:
                return String.valueOf(cell.getNumericCellValue());
            default:
                return cell.getStringCellValue().trim();
        }
    }

    /**
     * 提取Z开头的物流编码
     */
    private List<String> extractZMaterialCodes(List<Map<String, Object>> excelDataList) {
        Set<String> zMaterialCodes = new HashSet<>();

        for (Map<String, Object> rowData : excelDataList) {
            String materialCode = (String) rowData.get("物料号##MATNR");
            if (StringUtils.isNotBlank(materialCode) && Z_PATTERN.matcher(materialCode.trim()).matches()) {
                zMaterialCodes.add(materialCode.trim());
            }
        }

        return new ArrayList<>(zMaterialCodes);
    }

    /**
     * 查询组合商品信息
     */
    private Map<String, List<PsCSkugroup>> queryGroupProducts(List<String> zMaterialCodes) {
        Map<String, List<PsCSkugroup>> groupProductMap = new HashMap<>();

        if (CollectionUtils.isEmpty(zMaterialCodes)) {
            log.warn("没有需要处理的Z开头物流编码");
            return groupProductMap;
        }

        // 去重物料编码
        Set<String> uniqueMaterialCodes = new HashSet<>(zMaterialCodes);

        // 查询组合商品信息
        List<PsCProGroup> psCProGroupList = psCProGroupMapper.selectProGroupInfoByEcodes(new ArrayList<>(uniqueMaterialCodes));

        // 检查组合商品数量是否匹配
        if (CollectionUtils.isEmpty(psCProGroupList)) {
            log.warn("未找到任何组合商品信息，物料编码：{}", uniqueMaterialCodes);
            return groupProductMap;
        }

        Set<String> foundGroupCodes = psCProGroupList.stream()
                .map(PsCProGroup::getEcode)
                .collect(java.util.stream.Collectors.toSet());

        // 检查是否所有编码都找到了对应的组合商品
        if (foundGroupCodes.size() != uniqueMaterialCodes.size()) {
            Set<String> missingCodes = new HashSet<>(uniqueMaterialCodes);
            missingCodes.removeAll(foundGroupCodes);
            log.warn("部分物料编码未找到对应的组合商品信息：{}", missingCodes);
        }

        // 查询并组装组合商品明细
        for (PsCProGroup proGroup : psCProGroupList) {
            String materialCode = proGroup.getEcode();
            try {
                List<PsCSkugroup> groupProducts = cproSkuGroupMapper.selectGroupByProId(proGroup.getId());
                if (CollectionUtils.isNotEmpty(groupProducts)) {
                    groupProductMap.put(materialCode, groupProducts);
                    log.debug("物料编码 {} 找到 {} 个组合商品明细", materialCode, groupProducts.size());
                } else {
                    log.warn("物料编码 {} 未找到组合商品明细", materialCode);
                }
            } catch (NumberFormatException e) {
                log.error("物料编码 {} 无法转换为Long类型", materialCode, e);
            }
        }

        return groupProductMap;
    }

    /**
     * 查找不存在的组合商品
     */
    private List<String> findMissingProducts(List<String> zMaterialCodes, Map<String, List<PsCSkugroup>> groupProductMap) {
        List<String> missingProducts = new ArrayList<>();

        for (String materialCode : zMaterialCodes) {
            if (!groupProductMap.containsKey(materialCode) || CollectionUtils.isEmpty(groupProductMap.get(materialCode))) {
                missingProducts.add(materialCode);
            }
        }

        return missingProducts;
    }

    /**
     * 展开组合商品为多行数据
     */
    private List<Map<String, Object>> expandGroupProducts(List<Map<String, Object>> excelDataList,
                                                         Map<String, List<PsCSkugroup>> groupProductMap) {
        List<Map<String, Object>> expandedDataList = new ArrayList<>();

        for (Map<String, Object> originalRow : excelDataList) {
            String materialCode = (String) originalRow.get("物料号##MATNR");
            String quantityStr = (String) originalRow.get("数量##KWMENG");

            // 如果不是Z开头的物流编码，直接添加原行（保留所有原始字段）
            if (StringUtils.isBlank(materialCode) || !Z_PATTERN.matcher(materialCode.trim()).matches()) {
                Map<String, Object> newRow = new HashMap<>();
                // 复制所有字段，确保完整保留原始数据
                for (Map.Entry<String, Object> entry : originalRow.entrySet()) {
                    if (!"rowIndex".equals(entry.getKey())) { // 排除内部字段
                        newRow.put(entry.getKey(), entry.getValue());
                    }
                }
                expandedDataList.add(newRow);
                continue;
            }

            // 获取组合商品信息
            List<PsCSkugroup> groupProducts = groupProductMap.get(materialCode.trim());
            if (CollectionUtils.isEmpty(groupProducts)) {
                // 如果没有找到组合商品，保留原行（保留所有原始字段）
                Map<String, Object> newRow = new HashMap<>();
                for (Map.Entry<String, Object> entry : originalRow.entrySet()) {
                    if (!"rowIndex".equals(entry.getKey())) {
                        newRow.put(entry.getKey(), entry.getValue());
                    }
                }
                expandedDataList.add(newRow);
                continue;
            }

            // 解析数量
            BigDecimal originalQuantity = parseQuantity(quantityStr);
            if (originalQuantity == null || originalQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                // 数量无效，保留原行（保留所有原始字段）
                Map<String, Object> newRow = new HashMap<>();
                for (Map.Entry<String, Object> entry : originalRow.entrySet()) {
                    if (!"rowIndex".equals(entry.getKey())) {
                        newRow.put(entry.getKey(), entry.getValue());
                    }
                }
                expandedDataList.add(newRow);
                continue;
            }

            // 展开组合商品：一个组合商品生成多行，每行对应一个子商品
            for (PsCSkugroup groupProduct : groupProducts) {
                Map<String, Object> expandedRow = new HashMap<>();

                // 复制所有原始字段作为基础
                for (Map.Entry<String, Object> entry : originalRow.entrySet()) {
                    if (!"rowIndex".equals(entry.getKey())) {
                        expandedRow.put(entry.getKey(), entry.getValue());
                    }
                }

                // 更新关键字段：物料号、物料名称、数量
                // 1. 更新物料号为子商品编码
                expandedRow.put("物料号##MATNR", groupProduct.getPsCSkuEcode());

                // 2. 更新物料名称为子商品名称
                if (StringUtils.isNotBlank(groupProduct.getTitle())) {
                    expandedRow.put("物料名称##TXZ01", groupProduct.getTitle());
                } else if (StringUtils.isNotBlank(groupProduct.getPsCProEname())) {
                    expandedRow.put("物料名称##TXZ01", groupProduct.getPsCProEname());
                }

                // 3. 计算实际数量：原数量 * 组合中该子商品的数量
                BigDecimal groupNum = groupProduct.getNum() != null ? groupProduct.getNum() : BigDecimal.ONE;
                BigDecimal actualQuantity = originalQuantity.multiply(groupNum);
                expandedRow.put("数量##KWMENG", actualQuantity.toString());

                // 4. 更新成本价（如果组合商品中有定义）
                if (groupProduct.getSellprice() != null) {
                    expandedRow.put("成本价##KBETR", groupProduct.getSellprice().toString());

                    // 重新计算合计金额：实际数量 * 成本价
                    BigDecimal totalAmount = actualQuantity.multiply(groupProduct.getSellprice());
                    expandedRow.put("合计金额##hjje", totalAmount.toString());
                } else {
                    // 如果没有成本价，尝试重新计算合计金额（如果原来有成本价的话）
                    String originalPriceStr = (String) originalRow.get("成本价##KBETR");
                    if (StringUtils.isNotBlank(originalPriceStr)) {
                        try {
                            BigDecimal originalPrice = new BigDecimal(originalPriceStr.trim());
                            BigDecimal totalAmount = actualQuantity.multiply(originalPrice);
                            expandedRow.put("合计金额##hjje", totalAmount.toString());
                        } catch (NumberFormatException e) {
                            log.warn("无法解析原始成本价: {}", originalPriceStr);
                        }
                    }
                }

                // 5. 更新销售单位（如果需要的话，可以从组合商品信息中获取）
                // expandedRow.put("销售单位##VRKME", "PCS"); // 根据实际业务需求设置

                expandedDataList.add(expandedRow);
            }
        }

        return expandedDataList;
    }

    /**
     * 解析数量字符串为BigDecimal
     */
    private BigDecimal parseQuantity(String quantityStr) {
        if (StringUtils.isBlank(quantityStr)) {
            return null;
        }

        try {
            return new BigDecimal(quantityStr.trim());
        } catch (NumberFormatException e) {
            log.warn("无法解析数量: {}", quantityStr);
            return null;
        }
    }

    /**
     * 生成错误文件
     */
    private String generateErrorFile(List<Map<String, Object>> excelDataList,
                                   List<String> missingProducts, User currentUser) throws Exception {
        List<Map<String, Object>> errorDataList = new ArrayList<>();

        for (Map<String, Object> rowData : excelDataList) {
            String materialCode = (String) rowData.get("物料号##MATNR");
            if (StringUtils.isNotBlank(materialCode) && missingProducts.contains(materialCode.trim())) {
                Map<String, Object> errorRow = new HashMap<>();

                // 复制所有原始字段
                for (Map.Entry<String, Object> entry : rowData.entrySet()) {
                    if (!"rowIndex".equals(entry.getKey())) { // 排除内部字段
                        errorRow.put(entry.getKey(), entry.getValue());
                    }
                }

                // 添加错误信息字段
                errorRow.put("错误信息", "第" + rowData.get("rowIndex") + "行组合商品不存在！");
                errorDataList.add(errorRow);
            }
        }

        return createExcelFile(errorDataList, currentUser, "错误文件");
    }

    /**
     * 转换为导出文件
     */
    private String convertToExportFile(List<Map<String, Object>> dataList, User currentUser, String description) throws Exception {
        return createExcelFile(dataList, currentUser, description);
    }

    /**
     * 创建Excel文件并上传到OSS
     */
    private String createExcelFile(List<Map<String, Object>> dataList, User currentUser, String description) throws Exception {
        if (CollectionUtils.isEmpty(dataList)) {
            return null;
        }

        // 创建工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();

        // 获取表头
        Set<String> allColumns = new LinkedHashSet<>();
        for (Map<String, Object> rowData : dataList) {
            allColumns.addAll(rowData.keySet());
        }

        // 移除内部字段
        allColumns.remove("rowIndex");

        List<String> columnNames = new ArrayList<>(allColumns);
        List<String> keys = new ArrayList<>(allColumns);

        // 使用ExportUtil创建Excel
        XSSFWorkbook resultWorkbook = exportUtil.execute("订单数据", description, columnNames, keys, dataList);

        // 上传到OSS并返回URL
        return exportUtil.saveFileAndPutOss(resultWorkbook, "订单导入结果", currentUser, "order-import/");
    }

    /**
     * 记录导入信息到数据库
     */
    private void recordImportInfo(MultipartFile file, String exportFileUrl, String parseResult, User currentUser) {
        try {
            PsCOaOrderFileImport fileImport = new PsCOaOrderFileImport();
            fileImport.setImportFile(file.getOriginalFilename());
            fileImport.setExportFile(exportFileUrl);
            fileImport.setParseResult(parseResult);
            fileImport.setImportUserId(Long.valueOf(currentUser.getId()));
            fileImport.setImportUserName(currentUser.getName());
            fileImport.setImportTime(new Date());
            fileImport.setFileSize(file.getSize());
            fileImport.setFileType(getFileExtension(file.getOriginalFilename()));
            fileImport.setProcessStatus(2); // 处理成功

            // 设置BaseModel字段
            fileImport.setAdClientId(Long.valueOf(currentUser.getClientId()));
            fileImport.setAdOrgId(Long.valueOf(currentUser.getOrgId()));
            fileImport.setOwnerid(Long.valueOf(currentUser.getId()));
            fileImport.setOwnername(currentUser.getName());
            fileImport.setCreationdate(new Date());
            fileImport.setModifierid(Long.valueOf(currentUser.getId()));
            fileImport.setModifiername(currentUser.getName());
            fileImport.setModifieddate(new Date());
            fileImport.setIsactive("Y");

            // TODO: 保存到数据库 - 需要注入对应的Mapper
            // psCFileImportMapper.insert(fileImport);

        } catch (Exception e) {
            log.error("记录导入信息失败", e);
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return "";
        }

        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex + 1).toLowerCase();
        }

        return "";
    }

    /**
     * 创建 xls 格式的模板
     */
    private HSSFWorkbook createXlsTemplate(List<String> columnNames) {
        // 创建工作簿
        HSSFWorkbook workbook = new HSSFWorkbook();

        // 创建工作表
        HSSFSheet sheet = workbook.createSheet("OA订单导入模板");

        // 创建表头行
        HSSFRow headerRow = sheet.createRow(0);
        headerRow.setHeightInPoints(15); // 设置表头高度

        // 创建表头样式
        HSSFCellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        headerStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);

        HSSFFont headerFont = workbook.createFont();
        headerFont.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
        headerStyle.setFont(headerFont);

        // 填充表头
        for (int i = 0; i < columnNames.size(); i++) {
            HSSFCell cell = headerRow.createCell(i);
            cell.setCellValue(columnNames.get(i));
            cell.setCellStyle(headerStyle);

            // 设置列宽
            sheet.setColumnWidth(i, 4000);
        }

        return workbook;
    }

    /**
     * 保存 xls 文件并上传到 OSS
     */
    private String saveXlsFileAndPutOss(HSSFWorkbook workbook, String fileName, User user, String fileAddress) {
        try {
            // 上传文件
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            workbook.write(baos);
            ByteArrayInputStream swapStream = new ByteArrayInputStream(baos.toByteArray());
            return updateXlsSheet(swapStream, fileName, user, fileAddress);
        } catch (Exception e) {
            log.error("保存xls文件失败", e);
            return null;
        }
    }

    /**
     * 上传 xls 文件到 OSS (专门处理 .xls 格式)
     */
    private String updateXlsSheet(ByteArrayInputStream file, String fileName, User loginUser, String fileAddress) {
        String prefixKey = fileAddress;
        // 上传文件
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        String name = fileName + loginUser.getName() + sdf.format(new Date()); // 文件名称
        prefixKey = prefixKey + name + ".xls"; // 使用 .xls 扩展名

        // 获取OSS配置
        String endpoint = pconf.getProperty(PsExtConstantsIF.ENDPOINT_KEY);
        String accessKeyId = pconf.getProperty(PsExtConstantsIF.ACCESS_KEY);
        String accessKeySecret = pconf.getProperty(PsExtConstantsIF.SECRET_KEY);
        String bucketName = pconf.getProperty(PsExtConstantsIF.BUCKET_NAME);
        String timeout = pconf.getProperty(PsExtConstantsIF.TIMEOUT);

        if (StringUtils.isEmpty(timeout)) {
            timeout = "1800000"; // 默认30分钟
        }

        // 创建OSSClient实例
        OSSClient ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);

        // 判断文件是否存在
        boolean found = ossClient.doesObjectExist(bucketName, prefixKey);
        if (found) {
            try {
                Thread.sleep(10);
            } catch (Exception e) {
                // ignore
            }
            String newfileName = fileName + loginUser.getName() + sdf.format(new Date());
            prefixKey = fileAddress + newfileName + ".xls";
            return getXlsUrl(file, prefixKey, ossClient, timeout);
        } else {
            return getXlsUrl(file, prefixKey, ossClient, timeout);
        }
    }

    /**
     * 获取 xls 文件的 OSS URL
     */
    private String getXlsUrl(ByteArrayInputStream file, String prefixKey, OSSClient ossClient, String timeout) {
        // 设置URL过期时间
        Date expiration = new Date(System.currentTimeMillis() + Long.valueOf(timeout));
        // 生成以GET方法访问的签名URL
        URL url = ossClient.generatePresignedUrl(pconf.getProperty(PsExtConstantsIF.BUCKET_NAME), prefixKey, expiration);
        try {
            ossClient.putObject(pconf.getProperty(PsExtConstantsIF.BUCKET_NAME), prefixKey, file);
        } finally {
            // 关闭OSSClient
            ossClient.shutdown();
        }
        return url.toString();
    }
}
