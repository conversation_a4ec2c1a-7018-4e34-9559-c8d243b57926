package com.jackrain.nea.psext.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.psext.common.PsExtConstantsIF;
import com.jackrain.nea.psext.mapper.CproSkuGroupMapper;
import com.jackrain.nea.psext.mapper.PsCOaOrderFileImportMapper;
import com.jackrain.nea.psext.mapper.PsCProGroupMapper;
import com.jackrain.nea.psext.model.table.PsCOaOrderFileImport;
import com.jackrain.nea.psext.model.table.PsCProGroup;
import com.jackrain.nea.psext.model.table.PsCSkugroup;
import com.jackrain.nea.psext.utils.ExportUtil;
import com.jackrain.nea.psext.utils.PsUtils;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

import static com.jackrain.nea.psext.controller.ImportSkuAndSkuGroupCtrl.FORMULA;
import static com.jackrain.nea.psext.controller.ImportSkuAndSkuGroupCtrl.NUMERIC;

/**
 * @ClassName PsCOaOrderImportCtrl
 * @Description OA订单导入控制器
 * <AUTHOR>
 * @Date 2025/5/27 15:27
 * @Version 1.0
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/cs/ps-ext")
public class PsCOaOrderImportCtrl {

    @Autowired
    private CproSkuGroupMapper cproSkuGroupMapper;

    @Autowired
    private PsCProGroupMapper psCProGroupMapper;

    @Autowired
    private PsCOaOrderFileImportMapper psCOaOrderFileImportMapper;

    @Autowired
    private ExportUtil exportUtil;

    @Autowired
    private PropertiesConf pconf;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    private static final NumberFormat nf = NumberFormat.getInstance();

    // Z开头的物料编码正则表达式（大小写不敏感）
    private static final Pattern Z_PATTERN = Pattern.compile("^[Zz].*");

    // 定义表头顺序常量，确保导入和导出的表头顺序一致
    private static final String[] TEMPLATE_HEADERS = {
            "订单编号-区分送货##bh", "客户名称##KUNAG", "SAP客户编码##KUNWE", "电话##TEL_NUMBER",
            "联系人##NAME_CO", "综合地址信息##zhdzxx", "省代码##REGION", "省##s", "市##CITY", "区##DISTRICT",
            "地址信息##STREET", "物料号##MATNR", "物料名称##TXZ01", "数量##KWMENG", "销售单位##VRKME",
            "备注（不要写数字）##TEXT", "工厂##bukrs", "指定效期上限（早）##zdxqsx", "指定效期下限（晚）##zdxqxx",
            "品相##px", "成本中心##cbzx", "补发原始单号##bfysdh", "补发原因##bfyy"
    };

    private static String getCellValue(Cell cell) {
        try {
            int cellType = cell.getCellType();
            String cellValue;
            switch (cellType) {
                case NUMERIC:
                    cellValue = nf.format(cell.getNumericCellValue());
                    if (cellValue.indexOf(",") >= 0) {
                        cellValue = cellValue.replace(",", "");
                    }
                    break;
                case FORMULA:
                    try {
                        cellValue = cell.getStringCellValue();
                    } catch (IllegalStateException e) {
                        cellValue = String.valueOf(cell.getNumericCellValue());
                    }
                    break;

                default:
                    cellValue = cell.getStringCellValue();
            }

            return cellValue.trim();
        } catch (Exception e) {
            log.error("##getSkuImportList##readExcel##getCellValue##Error:{}", e);
        }
        return null;
    }

    /**
     * 下载模板
     *
     * @return string str
     * @throws Exception ex
     */
    @ApiOperation(value = "下载模板")
    @RequestMapping(path = "/oa/order/downloadOrderTemp", method = RequestMethod.GET)
    public ValueHolderV14 downloadTemp(HttpServletRequest request)
            throws Exception {
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);

        ValueHolderV14 holderV14 = new ValueHolderV14(ResultCode.SUCCESS, "OA订单导入模板下载成功！");

        // 配置OSS参数
        String endpoint = pconf.getProperty(PsExtConstantsIF.ENDPOINT_KEY);
        String accessKeyId = pconf.getProperty(PsExtConstantsIF.ACCESS_KEY);
        String accessKeySecret = pconf.getProperty(PsExtConstantsIF.SECRET_KEY);
        String bucketName = pconf.getProperty(PsExtConstantsIF.BUCKET_NAME);
        String timeout = pconf.getProperty(PsExtConstantsIF.TIMEOUT);

        exportUtil.setEndpoint(endpoint);
        exportUtil.setAccessKeyId(accessKeyId);
        exportUtil.setAccessKeySecret(accessKeySecret);
        exportUtil.setBucketName(bucketName);

        if (StringUtils.isEmpty(timeout)) {
            //如果获取不到配置参数，设置默认过期时间为30分钟
            timeout = "1800000";
        }
        exportUtil.setTimeout(timeout);

        /**
         * 使用统一的表头顺序常量：OA订单导入模板
         */
        List orderN = Lists.newArrayList(TEMPLATE_HEADERS);

        //生成Excel
        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        String sdd = "";
        hssfWorkbook = exportUtil.executeSheet(hssfWorkbook, "OA组合商品免费订单导入模板", "", orderN, Lists.newArrayList(), Lists.newArrayList(), true);
        sdd = exportUtil.saveFileAndPutOss(hssfWorkbook, "OA组合商品免费订单导入模板", user, "OSS-Bucket/EXPORT/OA_ORDER/", ".xls");

        holderV14.setData(sdd);
        return holderV14;
    }

    /**
     * 解析Excel文件
     */
    private List<Map<String, Object>> parseExcelFile(MultipartFile file) throws Exception {
        List<Map<String, Object>> dataList = new ArrayList<>();

        try (InputStream inputStream = file.getInputStream()) {
            Workbook workbook = new XSSFWorkbook(inputStream);
            Sheet sheet = workbook.getSheetAt(0);

            if (sheet.getLastRowNum() < 1) {
                return dataList;
            }

            // 读取表头
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) {
                return dataList;
            }

            Map<String, Integer> headerMap = new HashMap<>();
            for (Cell cell : headerRow) {
                String headerValue = getCellValue(cell);
                if (StringUtils.isNotBlank(headerValue)) {
                    headerMap.put(headerValue.trim(), cell.getColumnIndex());
                }
            }

            // 读取数据行
            for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row == null) {
                    continue;
                }

                Map<String, Object> rowData = new HashMap<>();
                rowData.put("rowIndex", rowIndex + 1); // 行号从1开始

                for (Map.Entry<String, Integer> entry : headerMap.entrySet()) {
                    String columnName = entry.getKey();
                    Integer columnIndex = entry.getValue();

                    Cell cell = row.getCell(columnIndex);
                    String cellValue = cell != null ? getCellValue(cell) : "";
                    rowData.put(columnName, cellValue);
                }

                dataList.add(rowData);
            }
        }

        return dataList;
    }

    @RequestMapping(path = "/oa/order/import", method = RequestMethod.POST)
    public ValueHolderV14 orderImport(HttpServletRequest request,
                                 @RequestParam(value = "file", required = true) MultipartFile file) {
        ValueHolderV14 result = new ValueHolderV14();

        try {
            // 获取当前用户信息
            User user = r3PrimWebAuthService.getLoginPrimWebUser(request);

            // 1. 解析Excel文件
            List<Map<String, Object>> excelDataList = parseExcelFile(file);
            if (CollectionUtils.isEmpty(excelDataList)) {
                result.setCode(ResultCode.FAIL);
                result.setMessage("Excel文件为空或格式不正确");
                return result;
            }

            // 2. 提取Z开头的物料编码进行处理
            List<String> zMaterialCodes = extractZMaterialCodes(excelDataList);

            // 统计非Z开头的物料数量（仅用于日志记录）
            long nonZCount = excelDataList.stream()
                .map(row -> (String) row.get("物料号##MATNR"))
                .filter(StringUtils::isNotBlank)
                .filter(code -> !Z_PATTERN.matcher(code.trim()).matches())
                .count();

            log.info("文件解析完成，总行数：{}，Z开头物料：{}行，非Z开头物料：{}行",
                    excelDataList.size(), zMaterialCodes.size(), nonZCount);

            if (CollectionUtils.isEmpty(zMaterialCodes)) {
                // 没有Z开头的数据，直接进行分组合并后转换为导出文件
                List<Map<String, Object>> mergedDataList = groupAndMergeData(excelDataList);
                String exportFileUrl = convertToExportFile(mergedDataList, user, "无Z开头物料编码，已分组合并");

                // 记录导入信息
                recordImportInfo(file, "无Z开头物料编码处理结果.xlsx", exportFileUrl, 1, user);

                result.setCode(ResultCode.SUCCESS);
                result.setMessage("处理完成，无Z开头物料编码需要处理，已完成分组合并");
                result.setData(exportFileUrl);
                return result;
            }

            // 3. 查询组合商品信息（只查询Z开头的物料）
            Map<String, List<PsCSkugroup>> groupProductMap = queryGroupProducts(zMaterialCodes);

            // 4. 检查Z开头的物料是否都存在对应的组合商品
            Map<String, List<Integer>> missingProductRowMap = findMissingProductsWithRows(excelDataList, zMaterialCodes, groupProductMap);
            if (!missingProductRowMap.isEmpty()) {
                // 生成错误文件
                String errorFileUrl = generateSimpleErrorFile(missingProductRowMap, "在组合商品中不存在", user);

                // 记录导入信息
                List<String> missingProducts = new ArrayList<>(missingProductRowMap.keySet());
                recordImportInfo(file, "组合商品不存在错误文件.xlsx", errorFileUrl, 2, user);
                result.setCode(ResultCode.FAIL);
                result.setMessage("存在不存在的组合商品");
                result.setData(errorFileUrl);
                return result;
            }

            // 5. 分别处理组合商品和单品
            List<Map<String, Object>> expandedGroupProducts = expandGroupProducts(excelDataList, groupProductMap);
            List<Map<String, Object>> singleProducts = extractSingleProducts(excelDataList);

            // 6. 合并组合商品展开结果和单品
            List<Map<String, Object>> allProducts = new ArrayList<>();
            allProducts.addAll(expandedGroupProducts);
            allProducts.addAll(singleProducts);

            log.info("数据合并完成，组合商品展开：{}行，单品：{}行，总计：{}行",
                    expandedGroupProducts.size(), singleProducts.size(), allProducts.size());

            // 7. 按照指定字段分组并合并相同物料号的行（这里会将展开的子商品与原有单品聚合）
            List<Map<String, Object>> mergedDataList = groupAndMergeData(allProducts);

            // 8. 生成导出文件
            String exportFileUrl = convertToExportFile(mergedDataList, user, "组合商品展开并与单品合并完成");

            // 9. 记录导入信息
            recordImportInfo(file, "OA订单处理结果.xlsx", exportFileUrl, 1, user);


            result.setCode(ResultCode.SUCCESS);
            result.setMessage("处理成功");

        } catch (Exception e) {
            log.error("订单导入处理失败", e);

            result.setCode(ResultCode.FAIL);
            result.setMessage("处理失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 提取Z开头的物料编码
     */
    private List<String> extractZMaterialCodes(List<Map<String, Object>> excelDataList) {
        Set<String> zMaterialCodes = new HashSet<>();

        for (Map<String, Object> rowData : excelDataList) {
            String materialCode = (String) rowData.get("物料号##MATNR");
            if (StringUtils.isNotBlank(materialCode) && Z_PATTERN.matcher(materialCode.trim()).matches()) {
                zMaterialCodes.add(materialCode.trim());
            }
        }

        return new ArrayList<>(zMaterialCodes);
    }

    /**
     * 查询组合商品信息
     */
    private Map<String, List<PsCSkugroup>> queryGroupProducts(List<String> zMaterialCodes) {
        Map<String, List<PsCSkugroup>> groupProductMap = new HashMap<>();

        if (CollectionUtils.isEmpty(zMaterialCodes)) {
            log.warn("没有需要处理的Z开头物料编码");
            return groupProductMap;
        }

        // 去重物料编码
        Set<String> uniqueMaterialCodes = new HashSet<>(zMaterialCodes);

        // 查询组合商品信息
        List<PsCProGroup> psCProGroupList = psCProGroupMapper.selectProGroupInfoByEcodes(new ArrayList<>(uniqueMaterialCodes));

        // 检查组合商品数量是否匹配
        if (CollectionUtils.isEmpty(psCProGroupList)) {
            log.warn("未找到任何组合商品信息，物料编码：{}", uniqueMaterialCodes);
            return groupProductMap;
        }

        Set<String> foundGroupCodes = psCProGroupList.stream()
                .map(PsCProGroup::getEcode)
                .collect(java.util.stream.Collectors.toSet());

        // 检查是否所有编码都找到了对应的组合商品
        if (foundGroupCodes.size() != uniqueMaterialCodes.size()) {
            Set<String> missingCodes = new HashSet<>(uniqueMaterialCodes);
            missingCodes.removeAll(foundGroupCodes);
            log.warn("部分物料编码未找到对应的组合商品信息：{}", missingCodes);
        }

        // 查询并组装组合商品明细
        for (PsCProGroup proGroup : psCProGroupList) {
            String materialCode = proGroup.getEcode();
            try {
                List<PsCSkugroup> groupProducts = cproSkuGroupMapper.selectGroupByProId(proGroup.getId());
                if (CollectionUtils.isNotEmpty(groupProducts)) {
                    groupProductMap.put(materialCode, groupProducts);
                    log.debug("物料编码 {} 找到 {} 个组合商品明细", materialCode, groupProducts.size());
                } else {
                    log.warn("物料编码 {} 未找到组合商品明细", materialCode);
                }
            } catch (NumberFormatException e) {
                log.error("物料编码 {} 无法转换为Long类型", materialCode, e);
            }
        }

        return groupProductMap;
    }

    /**
     * 查找不存在的组合商品
     */
    private List<String> findMissingProducts(List<String> zMaterialCodes, Map<String, List<PsCSkugroup>> groupProductMap) {
        List<String> missingProducts = new ArrayList<>();

        for (String materialCode : zMaterialCodes) {
            if (!groupProductMap.containsKey(materialCode) || CollectionUtils.isEmpty(groupProductMap.get(materialCode))) {
                missingProducts.add(materialCode);
            }
        }

        return missingProducts;
    }

    /**
     * 查找不存在的组合商品及其对应的行号
     */
    private Map<String, List<Integer>> findMissingProductsWithRows(List<Map<String, Object>> excelDataList,
                                                                  List<String> zMaterialCodes,
                                                                  Map<String, List<PsCSkugroup>> groupProductMap) {
        Map<String, List<Integer>> missingProductRowMap = new HashMap<>();

        for (Map<String, Object> rowData : excelDataList) {
            String materialCode = (String) rowData.get("物料号##MATNR");
            if (StringUtils.isNotBlank(materialCode) && Z_PATTERN.matcher(materialCode.trim()).matches()) {
                String trimmedCode = materialCode.trim();
                if (!groupProductMap.containsKey(trimmedCode) || CollectionUtils.isEmpty(groupProductMap.get(trimmedCode))) {
                    missingProductRowMap.computeIfAbsent(trimmedCode, k -> new ArrayList<>())
                        .add((Integer) rowData.get("rowIndex"));
                }
            }
        }

        return missingProductRowMap;
    }

    /**
     * 展开组合商品为多行数据
     */
    private List<Map<String, Object>> expandGroupProducts(List<Map<String, Object>> excelDataList,
                                                         Map<String, List<PsCSkugroup>> groupProductMap) {
        List<Map<String, Object>> expandedDataList = new ArrayList<>();

        for (Map<String, Object> originalRow : excelDataList) {
            String materialCode = (String) originalRow.get("物料号##MATNR");
            String quantityStr = (String) originalRow.get("数量##KWMENG");

            // 只处理Z开头的物料编码，非Z开头的跳过（由extractSingleProducts方法处理）
            if (StringUtils.isBlank(materialCode) || !Z_PATTERN.matcher(materialCode.trim()).matches()) {
                continue; // 跳过非Z开头的物料
            }

            // 获取组合商品信息
            List<PsCSkugroup> groupProducts = groupProductMap.get(materialCode.trim());
            if (CollectionUtils.isEmpty(groupProducts)) {
                // 如果没有找到组合商品，保留原行（保留所有原始字段）
                Map<String, Object> newRow = new HashMap<>();
                for (Map.Entry<String, Object> entry : originalRow.entrySet()) {
                    if (!"rowIndex".equals(entry.getKey())) {
                        newRow.put(entry.getKey(), entry.getValue());
                    }
                }
                expandedDataList.add(newRow);
                continue;
            }

            // 解析数量
            BigDecimal originalQuantity = parseQuantity(quantityStr);
            if (originalQuantity == null || originalQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                // 数量无效，保留原行（保留所有原始字段）
                Map<String, Object> newRow = new HashMap<>();
                for (Map.Entry<String, Object> entry : originalRow.entrySet()) {
                    if (!"rowIndex".equals(entry.getKey())) {
                        newRow.put(entry.getKey(), entry.getValue());
                    }
                }
                expandedDataList.add(newRow);
                continue;
            }

            // 展开组合商品：一个组合商品生成多行，每行对应一个子商品
            for (PsCSkugroup groupProduct : groupProducts) {
                Map<String, Object> expandedRow = new HashMap<>();

                // 复制所有原始字段作为基础
                for (Map.Entry<String, Object> entry : originalRow.entrySet()) {
                    if (!"rowIndex".equals(entry.getKey())) {
                        expandedRow.put(entry.getKey(), entry.getValue());
                    }
                }

                // 更新关键字段：物料号、物料名称、数量
                // 1. 更新物料号为子商品编码
                expandedRow.put("物料号##MATNR", groupProduct.getPsCSkuEcode());

                // 2. 更新物料名称为子商品名称
                if (StringUtils.isNotBlank(groupProduct.getTitle())) {
                    expandedRow.put("物料名称##TXZ01", groupProduct.getTitle());
                } else if (StringUtils.isNotBlank(groupProduct.getPsCProEname())) {
                    expandedRow.put("物料名称##TXZ01", groupProduct.getPsCProEname());
                }

                // 3. 计算实际数量：原数量 * 组合中该子商品的数量
                BigDecimal groupNum = groupProduct.getNum() != null ? groupProduct.getNum() : BigDecimal.ONE;
                BigDecimal actualQuantity = originalQuantity.multiply(groupNum);
                expandedRow.put("数量##KWMENG", actualQuantity.toString());

                // 4. 更新成本价（如果组合商品中有定义）
                if (groupProduct.getSellprice() != null) {
                    expandedRow.put("成本价##KBETR", groupProduct.getSellprice().toString());

                    // 重新计算合计金额：实际数量 * 成本价
                    BigDecimal totalAmount = actualQuantity.multiply(groupProduct.getSellprice());
                    expandedRow.put("合计金额##hjje", totalAmount.toString());
                } else {
                    // 如果没有成本价，尝试重新计算合计金额（如果原来有成本价的话）
                    String originalPriceStr = (String) originalRow.get("成本价##KBETR");
                    if (StringUtils.isNotBlank(originalPriceStr)) {
                        try {
                            BigDecimal originalPrice = new BigDecimal(originalPriceStr.trim());
                            BigDecimal totalAmount = actualQuantity.multiply(originalPrice);
                            expandedRow.put("合计金额##hjje", totalAmount.toString());
                        } catch (NumberFormatException e) {
                            log.warn("无法解析原始成本价: {}", originalPriceStr);
                        }
                    }
                }

                // 5. 更新销售单位（如果需要的话，可以从组合商品信息中获取）
                // expandedRow.put("销售单位##VRKME", "PCS"); // 根据实际业务需求设置

                expandedDataList.add(expandedRow);
            }
        }

        return expandedDataList;
    }

    /**
     * 提取单品（非Z开头的物料）
     */
    private List<Map<String, Object>> extractSingleProducts(List<Map<String, Object>> excelDataList) {
        List<Map<String, Object>> singleProducts = new ArrayList<>();

        for (Map<String, Object> originalRow : excelDataList) {
            String materialCode = (String) originalRow.get("物料号##MATNR");

            // 只处理非Z开头的物料编码
            if (StringUtils.isNotBlank(materialCode) && !Z_PATTERN.matcher(materialCode.trim()).matches()) {
                Map<String, Object> newRow = new HashMap<>();
                // 复制所有字段，确保完整保留原始数据
                for (Map.Entry<String, Object> entry : originalRow.entrySet()) {
                    if (!"rowIndex".equals(entry.getKey())) { // 排除内部字段
                        newRow.put(entry.getKey(), entry.getValue());
                    }
                }
                singleProducts.add(newRow);
            }
        }

        log.info("提取单品完成，共{}行", singleProducts.size());
        return singleProducts;
    }

    /**
     * 按照指定字段分组并合并相同物料号的行
     * 分组字段：SAP客户编码+电话+联系人+省+市+区+地址信息+工厂+成本中心
     * 合并规则：相同物料号的行进行合并，数量累加，其余信息不变
     */
    private List<Map<String, Object>> groupAndMergeData(List<Map<String, Object>> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return dataList;
        }

        // 定义分组字段
        String[] groupFields = {
            "SAP客户编码##KUNWE", "电话##TEL_NUMBER", "联系人##NAME_CO",
            "省##s", "市##CITY", "区##DISTRICT", "地址信息##STREET",
            "工厂##bukrs", "成本中心##cbzx"
        };

        // 使用Map来存储分组后的数据，key为分组标识，value为该分组下的数据列表
        Map<String, List<Map<String, Object>>> groupedData = new LinkedHashMap<>();

        // 第一步：按照分组字段进行分组
        for (Map<String, Object> rowData : dataList) {
            // 构建分组key
            StringBuilder groupKeyBuilder = new StringBuilder();
            for (String field : groupFields) {
                String value = (String) rowData.get(field);
                groupKeyBuilder.append(value != null ? value.trim() : "").append("|");
            }
            String groupKey = groupKeyBuilder.toString();

            // 将数据添加到对应的分组中
            groupedData.computeIfAbsent(groupKey, k -> new ArrayList<>()).add(rowData);
        }

        // 第二步：对每个分组内的数据按物料号进行合并
        List<Map<String, Object>> mergedDataList = new ArrayList<>();

        for (List<Map<String, Object>> groupDataList : groupedData.values()) {
            // 在每个分组内，按物料号进行二次分组
            Map<String, List<Map<String, Object>>> materialGroupedData = new LinkedHashMap<>();

            for (Map<String, Object> rowData : groupDataList) {
                String materialCode = (String) rowData.get("物料号##MATNR");
                String materialKey = materialCode != null ? materialCode.trim() : "";
                materialGroupedData.computeIfAbsent(materialKey, k -> new ArrayList<>()).add(rowData);
            }

            // 对每个物料号的数据进行合并
            for (List<Map<String, Object>> materialDataList : materialGroupedData.values()) {
                Map<String, Object> mergedRow = mergeMaterialRows(materialDataList);
                if (mergedRow != null) {
                    mergedDataList.add(mergedRow);
                }
            }
        }

        log.info("数据分组合并完成，原始数据{}行，合并后{}行", dataList.size(), mergedDataList.size());
        return mergedDataList;
    }

    /**
     * 合并相同物料号的行数据
     * 数量累加，其余信息取第一行的数据
     */
    private Map<String, Object> mergeMaterialRows(List<Map<String, Object>> materialDataList) {
        if (CollectionUtils.isEmpty(materialDataList)) {
            return null;
        }

        // 如果只有一行数据，直接返回
        if (materialDataList.size() == 1) {
            return new HashMap<>(materialDataList.get(0));
        }

        // 取第一行作为基础数据
        Map<String, Object> mergedRow = new HashMap<>(materialDataList.get(0));

        // 累加数量和合计金额
        BigDecimal totalQuantity = BigDecimal.ZERO;
        BigDecimal totalAmount = BigDecimal.ZERO;

        for (Map<String, Object> rowData : materialDataList) {
            // 累加数量
            String quantityStr = (String) rowData.get("数量##KWMENG");
            BigDecimal quantity = parseQuantity(quantityStr);
            if (quantity != null) {
                totalQuantity = totalQuantity.add(quantity);
            }

            // 累加合计金额
            String amountStr = (String) rowData.get("合计金额##hjje");
            BigDecimal amount = parseQuantity(amountStr);
            if (amount != null) {
                totalAmount = totalAmount.add(amount);
            }
        }

        // 更新合并后的数量和金额
        mergedRow.put("数量##KWMENG", totalQuantity.toString());
        mergedRow.put("合计金额##hjje", totalAmount.toString());

        log.debug("物料号 {} 合并了 {} 行数据，总数量：{}，总金额：{}",
                 mergedRow.get("物料号##MATNR"), materialDataList.size(), totalQuantity, totalAmount);

        return mergedRow;
    }

    /**
     * 解析数量字符串为BigDecimal
     */
    private BigDecimal parseQuantity(String quantityStr) {
        if (StringUtils.isBlank(quantityStr)) {
            return null;
        }

        try {
            return new BigDecimal(quantityStr.trim());
        } catch (NumberFormatException e) {
            log.warn("无法解析数量: {}", quantityStr);
            return null;
        }
    }

    /**
     * 生成错误文件
     */
    private String generateErrorFile(List<Map<String, Object>> excelDataList,
                                   List<String> missingProducts, User currentUser) throws Exception {
        List<Map<String, Object>> errorDataList = new ArrayList<>();

        for (Map<String, Object> rowData : excelDataList) {
            String materialCode = (String) rowData.get("物料号##MATNR");
            if (StringUtils.isNotBlank(materialCode) && missingProducts.contains(materialCode.trim())) {
                Map<String, Object> errorRow = new HashMap<>();

                // 复制所有原始字段
                for (Map.Entry<String, Object> entry : rowData.entrySet()) {
                    if (!"rowIndex".equals(entry.getKey())) { // 排除内部字段
                        errorRow.put(entry.getKey(), entry.getValue());
                    }
                }

                // 添加错误信息字段
                errorRow.put("错误信息", "第" + rowData.get("rowIndex") + "行组合商品不存在！");
                errorDataList.add(errorRow);
            }
        }

        return createExcelFile(errorDataList, currentUser, "错误文件");
    }

    /**
     * 生成简单格式的错误文件
     * 格式：行数 错误信息
     * 例如：3 Z001在组合商品中不存在！
     */
    private String generateSimpleErrorFile(Map<String, List<Integer>> errorRowMap, String errorType, User currentUser) throws Exception {
        if (errorRowMap.isEmpty()) {
            return null;
        }

        // 创建工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();

        // 创建简单的错误数据列表
        List<Map<String, Object>> errorDataList = new ArrayList<>();

        for (Map.Entry<String, List<Integer>> entry : errorRowMap.entrySet()) {
            String materialCode = entry.getKey();
            List<Integer> rowNumbers = entry.getValue();

            for (Integer rowNumber : rowNumbers) {
                Map<String, Object> errorRow = new HashMap<>();
                errorRow.put("行数", rowNumber.toString());
                errorRow.put("错误信息", materialCode + errorType + "！");
                errorDataList.add(errorRow);
            }
        }

        // 按行数排序
        errorDataList.sort((a, b) -> {
            Integer rowA = Integer.parseInt((String) a.get("行数"));
            Integer rowB = Integer.parseInt((String) b.get("行数"));
            return rowA.compareTo(rowB);
        });

        List<String> columnNames = Lists.newArrayList("行数", "错误信息");
        List<String> keys = Lists.newArrayList("行数", "错误信息");

        // 配置OSS参数
        String endpoint = pconf.getProperty(PsExtConstantsIF.ENDPOINT_KEY);
        String accessKeyId = pconf.getProperty(PsExtConstantsIF.ACCESS_KEY);
        String accessKeySecret = pconf.getProperty(PsExtConstantsIF.SECRET_KEY);
        String bucketName = pconf.getProperty(PsExtConstantsIF.BUCKET_NAME);
        String timeOut = pconf.getProperty(PsExtConstantsIF.TIMEOUT);

        exportUtil.setEndpoint(endpoint);
        exportUtil.setAccessKeyId(accessKeyId);
        exportUtil.setAccessKeySecret(accessKeySecret);
        exportUtil.setBucketName(bucketName);
        exportUtil.setTimeout("86400000");

        // 使用ExportUtil创建Excel
        XSSFWorkbook resultWorkbook = exportUtil.execute("错误信息", "错误文件", columnNames, keys, errorDataList);

        // 上传到OSS并返回URL
        return exportUtil.saveFileAndPutOss(resultWorkbook, "OA订单导入错误文件", currentUser, "oa-order-import/error/");
    }

    /**
     * 转换为导出文件
     */
    private String convertToExportFile(List<Map<String, Object>> dataList, User currentUser, String description) throws Exception {
        return createExcelFile(dataList, currentUser, description);
    }

    /**
     * 创建Excel文件并上传到OSS
     */
    private String createExcelFile(List<Map<String, Object>> dataList, User currentUser, String description) throws Exception {
        if (CollectionUtils.isEmpty(dataList)) {
            return null;
        }

        // 创建工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();

        // 按照模板表头顺序生成列名
        List<String> columnNames = new ArrayList<>();
        List<String> keys = new ArrayList<>();

        // 首先收集数据中实际存在的所有列
        Set<String> allDataColumns = new LinkedHashSet<>();
        for (Map<String, Object> rowData : dataList) {
            allDataColumns.addAll(rowData.keySet());
        }
        // 移除内部字段
        allDataColumns.remove("rowIndex");

        // 按照模板表头顺序添加存在的列
        for (String templateHeader : TEMPLATE_HEADERS) {
            if (allDataColumns.contains(templateHeader)) {
                columnNames.add(templateHeader);
                keys.add(templateHeader);
            }
        }

        // 添加其他不在模板中但存在于数据中的列（如错误信息等）
        for (String dataColumn : allDataColumns) {
            if (!columnNames.contains(dataColumn)) {
                columnNames.add(dataColumn);
                keys.add(dataColumn);
            }
        }

        String endpoint = pconf.getProperty(PsExtConstantsIF.ENDPOINT_KEY);
        String accessKeyId = pconf.getProperty(PsExtConstantsIF.ACCESS_KEY);
        String accessKeySecret = pconf.getProperty(PsExtConstantsIF.SECRET_KEY);
        String bucketName = pconf.getProperty(PsExtConstantsIF.BUCKET_NAME);
        String timeOut = pconf.getProperty(PsExtConstantsIF.TIMEOUT);

        exportUtil.setEndpoint(endpoint);
        exportUtil.setAccessKeyId(accessKeyId);
        exportUtil.setAccessKeySecret(accessKeySecret);
        exportUtil.setBucketName(bucketName);
        exportUtil.setTimeout("86400000");
        // 使用ExportUtil创建Excel
        XSSFWorkbook resultWorkbook = exportUtil.execute("订单数据", description, columnNames, keys, dataList);

        // 上传到OSS并返回URL
        return exportUtil.saveFileAndPutOss(resultWorkbook, "解析OA免费订单组合商品", currentUser, "oa-order-import/");
    }

    /**
     * 记录导入信息到数据库
     */
    private void recordImportInfo(MultipartFile file, String exportFileName, String exportFileUrl, Integer parseResult, User currentUser) {
        try {
            PsCOaOrderFileImport fileImport = new PsCOaOrderFileImport();
            fileImport.setId(ModelUtil.getSequence("ps_c_oa_order_file_import"));

            // 上传导入文件到OSS并构建JSON格式的导入文件信息
            String importFileUrl = uploadImportFileToOss(file, currentUser);
            JSONArray importJsonArray = new JSONArray();
            JSONObject importFileJSON = new JSONObject();
            importFileJSON.put("name", file.getOriginalFilename());
            importFileJSON.put("url", importFileUrl);
            importJsonArray.add(importFileJSON);
            fileImport.setImportFile(importJsonArray.toJSONString());

            // 构建JSON格式的导出文件信息
            JSONArray exportJsonArray = new JSONArray();
            JSONObject exportFileJSON = new JSONObject();
            exportFileJSON.put("name", exportFileName);
            exportFileJSON.put("url", exportFileUrl);
            exportJsonArray.add(exportFileJSON);
            fileImport.setExportFile(exportJsonArray.toJSONString());

            fileImport.setParseResult(parseResult);
            fileImport.setImportUserId(Long.valueOf(currentUser.getId()));
            fileImport.setImportUserName(currentUser.getName());
            fileImport.setImportTime(new Date());
            fileImport.setFileSize(file.getSize());
            fileImport.setFileType(getFileExtension(file.getOriginalFilename()));
            fileImport.setProcessStatus(2); // 处理成功

            PsUtils.setBModelDefalutData(fileImport, currentUser);
            psCOaOrderFileImportMapper.insert(fileImport);

        } catch (Exception e) {
            log.error("记录导入信息失败", e);
        }
    }

    /**
     * 上传导入文件到OSS
     */
    private String uploadImportFileToOss(MultipartFile file, User currentUser) {
        try {
            // 直接使用ExportUtil的updateSheet方法上传文件
            return exportUtil.updateSheet(
                new ByteArrayInputStream(file.getBytes()),
                "OA订单导入文件_" + currentUser.getName() + "_" + System.currentTimeMillis(),
                currentUser,
                "oa-order-import/source/",
                "." + getFileExtension(file.getOriginalFilename())
            );

        } catch (Exception e) {
            log.error("上传导入文件到OSS失败", e);
            return null;
        }
    }



    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return "";
        }

        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex + 1).toLowerCase();
        }

        return "";
    }
}
