package com.jackrain.nea.psext.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.common.ReferenceUtil;
import com.jackrain.nea.psext.api.TestSendSkuDatasToDemogicCmd;
import com.jackrain.nea.psext.services.SendSkuToWeiMengCmdImpl;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySessionImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@Api(value = "上传微盟", description = "上传微盟")
@RestController
@Slf4j
public class SendToWeiMengCtrl {
    @Autowired
    private SendSkuToWeiMengCmdImpl sendSkuDatasToWeiMengCmd;

    @ApiOperation(value = "条码信息上传微盟")
    @RequestMapping(path = "/api/cs/ps-ext/sendSkuDatasToWeiMeng", method = RequestMethod.POST)
    public JSONObject sendSkuDatasToWeiMeng(HttpServletRequest request, @RequestBody JSONObject jsonObject) throws Exception {
        User user1 = getRootUser();//r3PrimWebAuthService.getLoginPrimWebUser(request);
        QuerySessionImpl querySession = new QuerySessionImpl(user1);
        DefaultWebEvent event = new DefaultWebEvent("sendSkuDatasToWeiMeng", request, false);
        event.put("param", jsonObject);
        querySession.setEvent(event);
        ValueHolder execute = sendSkuDatasToWeiMengCmd.execute(querySession);
        return JSONObject.parseObject(JSONObject.toJSONString(execute.toJSONObject(),
                SerializerFeature.WriteMapNullValue));
    }

    @ApiOperation(value = "商品信息上传达摩")
    @RequestMapping(path = "/api/cs/ps-ext/sendSkuDatasToDemogic", method = RequestMethod.POST)
    public JSONObject sendSkuDatasToDemogic(HttpServletRequest request, @RequestBody JSONObject jsonObject) throws Exception {
        TestSendSkuDatasToDemogicCmd pushToDemogicCmd = (TestSendSkuDatasToDemogicCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(),
                TestSendSkuDatasToDemogicCmd.class.getName(), "ps-ext", "1.0");
        JSONObject params = new JSONObject();
        params.put("range",50);
        ValueHolderV14 execute = pushToDemogicCmd.PushSkuToDemogicInterface(params);
        return JSONObject.parseObject(JSONObject.toJSONString(execute.toJSONObject(),
                SerializerFeature.WriteMapNullValue));
    }
    @ApiOperation(value = "商品信息上传达摩中间表")
    @RequestMapping(path = "/api/cs/ps-ext/pushSkuToDemogicMediateTable", method = RequestMethod.POST)
    public JSONObject pushSkuToDemogicMediateTable(HttpServletRequest request, @RequestBody JSONObject jsonObject) throws Exception {
        TestSendSkuDatasToDemogicCmd pushToDemogicCmd = (TestSendSkuDatasToDemogicCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(),
                TestSendSkuDatasToDemogicCmd.class.getName(), "ps-ext", "1.0");
        JSONObject params = new JSONObject();
        params.put("range",10);
        ValueHolderV14 execute = pushToDemogicCmd.pushSkuToDemogicMediateTable(params);
        return JSONObject.parseObject(JSONObject.toJSONString(execute.toJSONObject(),
                SerializerFeature.WriteMapNullValue));
    }



    private User getRootUser() {
        UserImpl user = new UserImpl();
        user.setId(893);
        user.setName("admin");
        user.setEname("Pokemon-mapper");
        user.setActive(true);
        user.setClientId(37);
        user.setOrgId(27);
        user.setIsAdmin(2);
        user.setIsDev(2);
        return user;
    }
}