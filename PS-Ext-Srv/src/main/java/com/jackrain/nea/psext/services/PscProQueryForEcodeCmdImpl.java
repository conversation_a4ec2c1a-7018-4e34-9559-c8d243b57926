package com.jackrain.nea.psext.services;

import com.jackrain.nea.psext.api.PscProQueryForEcodeCmd;
import com.jackrain.nea.psext.model.table.PsCPro;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: chen<PERSON>
 * @since: 2020/05/09
 * create at : 2020/05/09 11:39
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps-ext")
public class PscProQueryForEcodeCmdImpl implements PscProQueryForEcodeCmd {
    @Autowired
    PscProQueryForEcodeService queryForEcodeService;

    @Override
    public PsCPro pscProQueryForEcode(String ecode) {
        return queryForEcodeService.queryForEcode(ecode);
    }
}
