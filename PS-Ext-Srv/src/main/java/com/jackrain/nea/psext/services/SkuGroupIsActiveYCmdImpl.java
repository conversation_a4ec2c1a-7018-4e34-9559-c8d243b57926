package com.jackrain.nea.psext.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.psext.api.SkuGroupIsActiveYCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * @author: 汪聿森
 * @Date: Created in 2019-07-15 9:09
 * @Description : 组合商品启用
 */

@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps-ext")
public class SkuGroupIsActiveYCmdImpl extends CommandAdapter implements SkuGroupIsActiveYCmd {

    @Autowired
    private SkuGroupIsActiveYService skuGroupIsActiveYService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return skuGroupIsActiveYService.execute(session);
    }
}

