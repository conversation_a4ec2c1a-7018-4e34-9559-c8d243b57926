package com.jackrain.nea.psext.services.sap;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.psext.api.sap.SapUnitChangeApiCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps-ext")
public class SapUnitChangeCmdImpl implements SapUnitChangeApiCmd {

    @Autowired
    private SapUnitChangeApiService sapUnitChangeApiService;

    @Override
    public ValueHolderV14 unitChangeCreateApi(JSONObject param) {
        
        return sapUnitChangeApiService.unitChangeCreateApi(param);
    }
}
