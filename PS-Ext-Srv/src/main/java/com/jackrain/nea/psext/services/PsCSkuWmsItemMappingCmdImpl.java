package com.jackrain.nea.psext.services;

import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.psext.api.PsCSkuWmsItemMappingCmd;
import com.jackrain.nea.psext.model.table.PsCSkuWmsItemMapping;
import com.jackrain.nea.psext.request.PsCSkuWmsItemMappingSaveRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * wms商品映射
 *
 * <AUTHOR>
 * @since 2023-04-04 09:57
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps-ext")
public class PsCSkuWmsItemMappingCmdImpl implements PsCSkuWmsItemMappingCmd {
    @Resource
    private PsCSkuWmsItemMappingService psCSkuWmsItemMappingService;

    @Override
    public ValueHolderV14<PsCSkuWmsItemMapping> queryByWmsItem(String customerId, String itemId) {
        try {
            PsCSkuWmsItemMapping itemMapping = psCSkuWmsItemMappingService.queryByWmsItem(customerId, itemId);
            return new ValueHolderV14<>(itemMapping, ResultCode.SUCCESS, "success");
        } catch (Exception e) {
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        }
    }

    @Override
    public ValueHolderV14<PsCSkuWmsItemMapping> queryByECode(String customerId, String ecode) {
        try {
            PsCSkuWmsItemMapping itemMapping = psCSkuWmsItemMappingService.queryByECode(customerId, ecode);
            return new ValueHolderV14<>(itemMapping, ResultCode.SUCCESS, "success");
        } catch (Exception e) {
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        }
    }

    @Override
    public ValueHolderV14<PsCSkuWmsItemMapping> save(PsCSkuWmsItemMappingSaveRequest saveReq) {
        try {
            return new ValueHolderV14<>(psCSkuWmsItemMappingService.save(saveReq), ResultCode.SUCCESS, "success");
        } catch (Exception e) {
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        }
    }
}
