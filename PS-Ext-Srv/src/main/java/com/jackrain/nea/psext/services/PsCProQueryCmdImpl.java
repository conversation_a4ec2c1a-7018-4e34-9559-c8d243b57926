package com.jackrain.nea.psext.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.psext.api.PsCProQueryCmd;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.psext.result.PdaQueryProResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;

/**
 * @ClassName : PsCProQueryCmdImpl
 * @Description :
 * <AUTHOR> CD
 * @Date: 2021-08-12 17:42
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps-ext")
public class PsCProQueryCmdImpl implements PsCProQueryCmd {

    @Autowired
    private PsCProQueryService psCProQueryService;

    /**
     * 提供给PDA的根据条件查询商品信息接口
     *
     * @param param
     * @return
     */
    @Override
    public ValueHolderV14<List<PdaQueryProResult>> pdaGetSkuList(JSONObject param) {
        return psCProQueryService.pdaGetSkuList(param);
    }

    @Override
    public ValueHolderV14<List<PsCPro>> selProByIds(List<Long> ids) {
        return psCProQueryService.selProByIds(ids);
    }

    @Override
    public ValueHolderV14<PsCPro> queryProById(Long id) {
        return psCProQueryService.queryProById(id);
    }

    @Override
    public ValueHolderV14<List<PsCPro>> queryProByIds(Collection ids) {
        return psCProQueryService.queryProByIds(ids);
    }

    @Override
    public ValueHolderV14<Boolean> exist(String ecode) {
        return psCProQueryService.exist(ecode);
    }

    @Override
    public ValueHolderV14<PsCPro> queryProByEcode(String ecode) {
        return psCProQueryService.queryProByEcode(ecode);
    }

    @Override
    public ValueHolderV14<List<PsCPro>> queryProByEcodes(List<String> ecodes) {
        return psCProQueryService.queryProByEcodes(ecodes);
    }

}
