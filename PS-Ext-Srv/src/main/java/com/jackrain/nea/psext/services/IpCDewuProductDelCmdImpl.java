package com.jackrain.nea.psext.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.psext.api.IpCDewuProductDelCmd;
import com.jackrain.nea.psext.mapper.IpCDewuProductMapper;
import com.jackrain.nea.psext.model.table.IpCDewuProduct;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utils.ValueHolderUtils;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName IpCDewuProductDelCmdImpl
 * @Description 删除
 * <AUTHOR>
 * @Date 2025/2/5 10:21
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps-ext")
public class IpCDewuProductDelCmdImpl extends CommandAdapter implements IpCDewuProductDelCmd {
    @Autowired
    private IpCDewuProductMapper ipCDewuProductMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        User user = querySession.getUser();
        log.info("IpCDewuProductDelCmdImpl execute param:{}", param.toJSONString());
        List<Long> idList = new ArrayList<>();
        Long id = param.getLong("objid");
        idList.add(id);
        List<IpCDewuProduct> ipCDewuProductList = ipCDewuProductMapper.selectBatchIds(idList);
        if (CollectionUtils.isEmpty(ipCDewuProductList)){
            return ValueHolderUtils.getFailValueHolder("记录已不存在");
        }
        // 判断ipCDewuProductList里面的qty字段是否有大于0的数据
        for (IpCDewuProduct ipCDewuProduct : ipCDewuProductList) {
            if (ipCDewuProduct.getQty() > 0) {
                return ValueHolderUtils.getFailValueHolder("存在库存大于0的记录，无法删除");
            }
        }
        // 批量删除
        ipCDewuProductMapper.deleteBatchIds(idList);
        return ValueHolderUtils.getSuccessValueHolder("删除成功！");
    }
}
