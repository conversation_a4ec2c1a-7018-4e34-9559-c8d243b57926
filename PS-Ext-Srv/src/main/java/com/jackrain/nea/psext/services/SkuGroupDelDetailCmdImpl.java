package com.jackrain.nea.psext.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.psext.api.SkuGroupDelDetailCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 郑立轩
 * @since: 2019/4/1
 * create at : 2019/4/1 13:33
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps-ext")
public class SkuGroupDelDetailCmdImpl extends CommandAdapter implements SkuGroupDelDetailCmd {
    @Autowired
    private SkuGroupDelDetailService service;

    @Override
    public ValueHolderV14 delDetail(JSONObject object, User user) {
        return service.delDetail(object, user);
    }
}
