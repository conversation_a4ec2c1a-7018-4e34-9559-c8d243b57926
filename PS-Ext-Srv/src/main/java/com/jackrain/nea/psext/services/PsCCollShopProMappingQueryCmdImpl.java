package com.jackrain.nea.psext.services;

import com.jackrain.nea.psext.api.PsCCollShopProMappingQueryCmd;
import com.jackrain.nea.psext.model.table.PsCCollShopProMapping;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/8/24
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps-ext")
public class PsCCollShopProMappingQueryCmdImpl implements PsCCollShopProMappingQueryCmd {

    @Autowired
    private PsCCollShopProMappingService psCCollShopProMappingService;

    @Override
    public ValueHolderV14<PsCCollShopProMapping> queryByShopIdAndSku(Long shopId, Long skuId) {
        return psCCollShopProMappingService.queryByShopIdAndSkuId(shopId, skuId);
    }

    @Override
    public ValueHolderV14<List<PsCCollShopProMapping>> queryByShopId(Long shopId) {
        return psCCollShopProMappingService.queryByShopId(shopId);
    }

    @Override
    public ValueHolderV14<List<PsCCollShopProMapping>> queryProMappingByShopIdAndSku(Long shopId,List<Long> skuId) {
        return psCCollShopProMappingService.queryProMappingByShopIdAndSku(shopId,skuId);
    }
}
