package com.jackrain.nea.psext.services.sap;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.psext.api.sap.SapProInfoApiCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utils.ValueHolderV14Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps-ext")
public class SapProInfoCmdImpl implements SapProInfoApiCmd {

//    @Autowired
//    private SapProInfoApiService sapProInfoApiService;

    @Override
    public ValueHolderV14 proInfoCreateApi(JSONObject param) {
        ValueHolderV14 v14 = ValueHolderV14Utils.getSuccessValueHolder("商品档案下推成功");
        try {
            SapProInfoApiService bean = ApplicationContextHandle.getBean(SapProInfoApiService.class);
            v14 = bean.proInfoCreateApi(param);
        } catch (Exception e) {
            v14.setCode(ResultCode.SUCCESS);
            v14.setMessage(Throwables.getStackTraceAsString(e));
        }
        return v14;
    }
}
