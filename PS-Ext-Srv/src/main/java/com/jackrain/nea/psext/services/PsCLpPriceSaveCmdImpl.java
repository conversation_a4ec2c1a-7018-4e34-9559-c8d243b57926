package com.jackrain.nea.psext.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.core.db.Tools;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.psext.api.PsCLpPriceSaveCmd;
import com.jackrain.nea.psext.common.PSConstants;
import com.jackrain.nea.psext.mapper.CpCHrorgMapper;
import com.jackrain.nea.psext.mapper.CpCSupplierMapper;
import com.jackrain.nea.psext.mapper.PsCLpPriceItemMapper;
import com.jackrain.nea.psext.mapper.PsCLpPriceMapper;
import com.jackrain.nea.psext.model.table.CpCHrorg;
import com.jackrain.nea.psext.model.table.CpCSupplier;
import com.jackrain.nea.psext.model.table.PsCLpPrice;
import com.jackrain.nea.psext.model.table.PsCLpPriceItem;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Objects;


/**
 * Created with IntelliJ IDEA.
 * @Author: jg.zhan
 * @Date:  2022/5/20 18:13
 * @Description:  lp价格主表保存接口
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps-ext")
public class PsCLpPriceSaveCmdImpl extends CommandAdapter implements PsCLpPriceSaveCmd {

    @Autowired
    private PsCLpPriceMapper psCLpPriceMapper;
    @Autowired
    private PsCLpPriceItemMapper psCLpPriceItemMapper;
    @Autowired
    private CpCSupplierMapper cpCSupplierMapper;
    @Autowired
    private CpCHrorgMapper cpCHrorgMapper;



    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        log.info("##PsCLpPriceSaveCmdImpl##execute##param:{}",param);
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        long objid = param.getLong("objid");
        JSONObject jo = fixColumn.getJSONObject("PS_C_LP_PRICE");
        JSONArray psCLpPriceItem = fixColumn.getJSONArray("PS_C_LP_PRICE_ITEM");
        try {
            if (Objects.nonNull(jo)){
                //实例化
                Timestamp timestamp = new Timestamp(System.currentTimeMillis());
                long oneId = Integer.valueOf(session.getUser().getId()).longValue();
                long adClientId = Integer.valueOf(session.getUser().getClientId()).longValue();
                long adOrgId = Integer.valueOf(session.getUser().getOrgId()).longValue();
                String mEname = String.valueOf(session.getUser().getEname());
                String mName = String.valueOf(session.getUser().getName());

                //判断增加或修改操作
                if (Objects.nonNull(objid) && objid > 0) {
                    //更新操作
                    checkUpdate(session, objid);
                    jo.put("ID", objid);
                    jo.put("MODIFIEDDATE", timestamp);
                    jo.put("MODIFIERID", oneId);
                    jo.put("MODIFIERNAME", mName);
                    jo.put("MODIFIERENAME", mEname);
                    PsCLpPrice psCLpPrice = JSONObject.parseObject(jo.toJSONString(), PsCLpPrice.class);
                    //执行更新操作
                    psCLpPriceMapper.updateById(psCLpPrice);

                } else {
                    checkParam(jo);
                    //增加操作
                    checkInsert(jo,session);
                    Long id = Tools.getSequence("PS_C_LP_PRICE");
                    objid = id;
                    jo.put("ID",id);
                    jo.put("AD_CLIENT_ID", adClientId);
                    jo.put("AD_ORG_ID", adOrgId);
                    jo.put("OWNERID", oneId);
                    jo.put("MODIFIERID", oneId);
                    jo.put("CREATIONDATE", timestamp);
                    jo.put("MODIFIEDDATE", timestamp);
                    jo.put("MODIFIERNAME", mName);
                    jo.put("OWNERNAME", mName);
                    jo.put("MODIFIERENAME", mEname);
                    jo.put("OWNERENAME", mEname);
                    PsCLpPrice insertPsCLpPrice = JSONObject.parseObject(jo.toJSONString(), PsCLpPrice.class);
                    //执行新增操作
                    psCLpPriceMapper.insert(insertPsCLpPrice);
                    //增加成功返回信息增加
                    JSONObject jsonObj = new JSONObject();
                    jsonObj.put("tablename", "PS_C_LP_PRICE");
                    jsonObj.put("objid", id);
                    vh.put("data", jsonObj);
                }
            }
            //保存明细表
            if (CollectionUtils.isNotEmpty(psCLpPriceItem)){
                saveItem(psCLpPriceItem,objid,session);
            }
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", Resources.getMessage("Success", session.getLocale()));
            return vh;
        } catch (NDSException ne){
            log.error("##BrandMatchSaveCmdImpl##execute##Error:{}",ne);
            vh.put("code", ResultCode.FAIL);
            vh.put("message", ne.getMessage());
            return vh;
        }catch (Exception e) {
            log.error("##BrandMatchSaveCmdImpl##execute##Error:{}",e);
            vh.put("code", ResultCode.FAIL);
            vh.put("message","系统异常！");
        }
        return vh;
    }

    /**
     * 保存明细表
     * @param psCLpPriceItem
     * @param masterId
     * @param session
     */
    private void saveItem(JSONArray psCLpPriceItem,Long masterId,QuerySession session){
        long oneId = Integer.valueOf(session.getUser().getId()).longValue();
        long adClientId = Integer.valueOf(session.getUser().getClientId()).longValue();
        long adOrgId = Integer.valueOf(session.getUser().getOrgId()).longValue();
        String mEname = String.valueOf(session.getUser().getEname());
        String mName = String.valueOf(session.getUser().getName());

        List<PsCLpPriceItem> psCLpPriceItemList = JSONObject.parseArray(psCLpPriceItem.toJSONString(), PsCLpPriceItem.class);
        for (PsCLpPriceItem item : psCLpPriceItemList) {
            //更新
            if (Objects.nonNull(item.getId()) && item.getId() > 0){
                if (Objects.nonNull(item.getCpCSupplierId())){
                    CpCSupplier cpCSupplierById = getCpCSupplierById(item.getCpCSupplierId());
                    if (Objects.isNull(cpCSupplierById)){
                        throw new NDSException("请关联到公司档案！");
                    }
                    item.setSalesOrg(cpCSupplierById.getEcode());
                }
                if (Objects.nonNull(item.getCpCHrorgId())){
                    CpCHrorg cpCHrorgById = getCpCHrorgById(item.getCpCHrorgId());
                    if (Objects.isNull(cpCHrorgById)){
                        throw new NDSException("请关联到内部组织！");
                    }
                    item.setCompanyCode(cpCHrorgById.getEcode());
                }
                item.setPsCLpPriceId(masterId);
                item.setModifieddate(new Date());
                item.setModifierename(mEname);
                item.setModifiername(mName);
                psCLpPriceItemMapper.updateById(item);
            }else {
                checkItemInsert(session,item.getDucode());
                //新增
                Long itemId = Tools.getSequence("PS_C_BRAND_MATCH");
                item.setId(itemId);
                item.setPsCLpPriceId(masterId);
                if (Objects.nonNull(item.getCpCSupplierId())){
                    CpCSupplier cpCSupplierById = getCpCSupplierById(item.getCpCSupplierId());
                    if (Objects.isNull(cpCSupplierById)){
                        throw new NDSException("请关联到公司档案！");
                    }
                    item.setSalesOrg(cpCSupplierById.getEcode());
                }
                if (Objects.nonNull(item.getCpCHrorgId())){
                    CpCHrorg cpCHrorgById = getCpCHrorgById(item.getCpCHrorgId());
                    if (Objects.isNull(cpCHrorgById)){
                        throw new NDSException("请关联到内部组织！");
                    }
                    item.setCompanyCode(cpCHrorgById.getEcode());
                }
                item.setAdClientId(adClientId);
                item.setAdOrgId(adOrgId);
                item.setCreationdate(new Date());
                item.setModifieddate(new Date());
                item.setModifierename(mEname);
                item.setModifiername(mName);
                item.setOwnerename(mEname);
                item.setOwnerid(oneId);
                item.setOwnername(mName);
                psCLpPriceItemMapper.insert(item);
            }
        }
    }
    /**
     * 校验新增数据
     * @param session
     */
    private void checkInsert(JSONObject jo ,QuerySession session) {
        List<PsCLpPrice> psCLpPriceList = psCLpPriceMapper.selectList(new QueryWrapper<PsCLpPrice>().lambda()
                .eq(PsCLpPrice::getIsactive, PSConstants.Y)
                .eq(PsCLpPrice::getLpBillNo,jo.getString("LP_BILL_NO"))
                .or()
                .eq(PsCLpPrice::getBillNo,jo.getString("BILL_NO")));
        if (CollectionUtils.isNotEmpty(psCLpPriceList)) {
            throw new NDSException(Resources.getMessage("单据编号已存在！", session.getLocale()));
        }
    }

    /**
     * 校验更新数据
     * @param session
     * @param objid
     */
    private void checkUpdate(QuerySession session, long objid) {
        //判断当前修改的记录是否存在
        PsCLpPrice psCLpPrice = psCLpPriceMapper.selectById(objid);
        if (Objects.isNull(psCLpPrice)) {
            throw new NDSException(Resources.getMessage("当前记录已不存在！", session.getLocale()));
        }
        if (PSConstants.N.equals(psCLpPrice.getIsactive())){
            throw new NDSException(Resources.getMessage("当前记录已作废，不允许保存！", session.getLocale()));
        }
    }

    private void checkParam(JSONObject jo) {
        if (Objects.isNull(jo)){
            throw new NDSException("请传入参数");
        }
    }

    /**
     * 根据id获取内部组织信息
     * @param id
     * @return
     */
    private CpCHrorg getCpCHrorgById(Long id){
        List<CpCHrorg> cpCHrorgList = cpCHrorgMapper.selectList(new QueryWrapper<CpCHrorg>().lambda()
                .eq(CpCHrorg::getId, id)
                .eq(CpCHrorg::getIsactive, PSConstants.Y));
        if (CollectionUtils.isNotEmpty(cpCHrorgList)){
            return cpCHrorgList.get(0);
        }
        return null;
    }

    /**
     * 根据ecode获取内部组织信息
     * @param ecode
     * @return
     */
    private CpCHrorg getCpCHrorgByEcode(String ecode){
        List<CpCHrorg> cpCHrorgList = cpCHrorgMapper.selectList(new QueryWrapper<CpCHrorg>().lambda()
                .eq(CpCHrorg::getEcode, ecode)
                .eq(CpCHrorg::getIsactive, PSConstants.Y));
        if (CollectionUtils.isNotEmpty(cpCHrorgList)){
            return cpCHrorgList.get(0);
        }
        return null;
    }
    /**
     * 根据id获取公司档案信息
     * @param id
     * @return
     */
    private CpCSupplier getCpCSupplierById(Long id){
        List<CpCSupplier> cpCSupplierList = cpCSupplierMapper.selectList(new QueryWrapper<CpCSupplier>().lambda()
                .eq(CpCSupplier::getId, id)
                .eq(CpCSupplier::getIsactive, PSConstants.Y));
        if (CollectionUtils.isNotEmpty(cpCSupplierList)){
            return cpCSupplierList.get(0);
        }
        return null;
    }

    /**
     * 根据ecode获取公司档案信息
     * @param ecode
     * @return
     */
    private CpCSupplier getCpCSupplierByEcode(String ecode){
        List<CpCSupplier> cpCSupplierList = cpCSupplierMapper.selectList(new QueryWrapper<CpCSupplier>().lambda()
                .eq(CpCSupplier::getEcode, ecode)
                .eq(CpCSupplier::getIsactive, PSConstants.Y));
        if (CollectionUtils.isNotEmpty(cpCSupplierList)){
            return cpCSupplierList.get(0);
        }
        return null;
    }
    /**
     * 校验新增数据
     * @param session
     */
    private void checkItemInsert(QuerySession session,String ducode) {
        List<PsCLpPriceItem> psCBrandMatchList = psCLpPriceItemMapper.selectList(new QueryWrapper<PsCLpPriceItem>().lambda()
                .eq(PsCLpPriceItem::getIsactive, PSConstants.Y)
                .eq(PsCLpPriceItem::getDucode,ducode));
        if (CollectionUtils.isNotEmpty(psCBrandMatchList)) {
            throw new NDSException(Resources.getMessage("当前记录已存在！", session.getLocale()));
        }
    }
}
