package com.jackrain.nea.psext.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.psext.api.ValidityDefinitionSaveCmd;
import com.jackrain.nea.psext.mapper.PsCSkuMapper;
import com.jackrain.nea.psext.mapper.PsCValidityDefinitionMapper;
import com.jackrain.nea.psext.model.table.PsCSku;
import com.jackrain.nea.psext.model.table.PsCValidityDefinition;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utils.ValueHolderUtils;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import utils.CommandAdapterUtil;

/**
 * 标准效期定义保存
 *
 * <AUTHOR>
 * @date 2022/06/17
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps-ext")
public class ValidityDefinitionSaveCmdImpl extends CommandAdapter implements ValidityDefinitionSaveCmd {

    @Autowired
    private PsCSkuMapper psCSkuMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        //获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        //获取参数
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        log.info("BansAreaStrategyService.save {}", JSONObject.toJSONString(param));
        Long id = param.getLong("objid");
        JSONObject data = param.getJSONObject("fixcolumn");
        String str = data.getString("PS_C_VALIDITY_DEFINITION");
        if (StringUtils.isEmpty(str)) {
            throw new NDSException("保存数据为空！");
        }
        //json转换成对象
        PsCValidityDefinition saveModel =
                JSONObject.parseObject(str, PsCValidityDefinition.class);
        User user = querySession.getUser();

        PsCValidityDefinitionMapper mapper = ApplicationContextHandle.getBean(PsCValidityDefinitionMapper.class);
        if (id == null || id < 1) {
            // 新增
            CommandAdapterUtil.defaultOperator(saveModel, user);
            id = ModelUtil.getSequence("PS_C_VALIDITY_DEFINITION");
            check(saveModel, new PsCValidityDefinition());
            if (saveModel.getPsCSkuId() != null) {
                PsCSku psCSku = psCSkuMapper.selectById(saveModel.getPsCSkuId());
                saveModel.setPsCProId(psCSku.getPsCProId());
                saveModel.setPsCProEcode(psCSku.getPsCProEcode());
                saveModel.setPsCProEcode(psCSku.getPsCProEname());
            }
            saveModel.setId(id);
            mapper.insert(saveModel);
        } else {
            PsCValidityDefinition beforModel = mapper.selectById(id);
            check(saveModel, beforModel);
            saveModel.setId(id);
            if (beforModel == null) {
                return ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
            }
            CommandAdapterUtil.defaultOperator(saveModel, user);
            mapper.updateById(saveModel);
        }
        return ValueHolderUtils.getSuccessValueHolder(id, "保存成功！");
    }

    private void check(PsCValidityDefinition saveModel, PsCValidityDefinition beforModel) {
        Integer beginParturitionNum = saveModel.getBeginParturitionNum() == null ?
                beforModel.getBeginParturitionNum() : saveModel.getBeginParturitionNum();
        Integer endParturitionNum = saveModel.getEndParturitionNum() == null ?
                beforModel.getEndParturitionNum() : saveModel.getEndParturitionNum();
        Long psCProdimId = saveModel.getPsCProdimItemId() == null ? beforModel.getPsCProdimItemId() : saveModel.getPsCProdimItemId();
        Long psCSkuId = saveModel.getPsCSkuId() == null ? beforModel.getPsCSkuId() : saveModel.getPsCSkuId();
        String validityType = saveModel.getValidityType() == null ? beforModel.getValidityType() : saveModel.getValidityType();

        if (psCProdimId != null && psCSkuId != null) {
            throw new NDSException("【一级】、【商品】只允许选择一项进行维护！");
        }

        if (psCProdimId == null && psCSkuId == null) {
            throw new NDSException("【一级】、【商品】请选择其中一项进行维护！");
        }

        if (beginParturitionNum == null || beginParturitionNum < 0 || endParturitionNum == null || endParturitionNum < 0) {
            throw new NDSException("录入的【开始数】或【结束数】不正确，不允许！");
        }
        if (endParturitionNum < beginParturitionNum) {
            throw new NDSException("结束数小于开始数，不允许！");
        }

        if (log.isDebugEnabled()) {
            log.debug("aaa:{}", JSONObject.toJSONString(beforModel));
        }

        if (beforModel.getId() == null || beforModel.getId() < 1L) {
            PsCValidityDefinitionMapper mapper = ApplicationContextHandle.getBean(PsCValidityDefinitionMapper.class);
            Integer count = mapper.selectCount(new LambdaQueryWrapper<PsCValidityDefinition>()
                    .eq(psCProdimId != null, PsCValidityDefinition::getPsCProdimItemId, psCProdimId)
                    .eq(psCSkuId != null, PsCValidityDefinition::getPsCSkuId, psCSkuId)
                    .eq(PsCValidityDefinition::getValidityType, validityType));
            if (count != null && count > 0) {
                if (psCSkuId == null) {
                    throw new NDSException("当前【一级】+【效期名称】已存在记录，不允许重复记录");
                }
                throw new NDSException("当前【商品】+【效期名称】已存在记录，不允许重复记录");
            }
        }
    }
}
