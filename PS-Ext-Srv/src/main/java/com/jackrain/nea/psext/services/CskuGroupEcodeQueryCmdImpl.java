package com.jackrain.nea.psext.services;

import com.jackrain.nea.psext.api.CskuGroupEcodeQueryCmd;
import com.jackrain.nea.psext.mapper.CproSkuGroupMapper;
import com.jackrain.nea.psext.mapper.ProSkuMapper;
import com.jackrain.nea.psext.mapper.PsCProMapper;
import com.jackrain.nea.psext.model.table.ExtPsCSku;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.psext.model.table.PsCSkugroup;
import com.jackrain.nea.psext.result.ProResult;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 获取提交状态的组合商品的信息：
 * 组合商品：组合商品id,组合商品名称,组合虚拟条码
 * 实际商品集合：条码id,条码编码
 *
 * @author: 郑立轩
 * @since: 2019/4/8
 * create at : 2019/4/8 15:50
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps")
public class CskuGroupEcodeQueryCmdImpl extends CommandAdapter implements CskuGroupEcodeQueryCmd {
    @Autowired
    private CproSkuGroupMapper skuGroupMapper;
    @Autowired
    private ProSkuMapper skuMapper;
    @Autowired
    private PsCProMapper proMapper;

    @Override
    public ValueHolderV14 groupEcodeQuery(Integer pageNum, Integer pageCount, Integer type) {
        ValueHolderV14 vh = new ValueHolderV14<>();
        vh.setCode(0);
        vh.setMessage("success");
        Integer status = 2;
        //查询符合条件的商品
        Integer startIndex = (pageNum - 1) * pageCount;
        List<PsCPro> psCPros = proMapper.selectProByStatus(status, startIndex, pageCount, type);
        vh.setData(getPsCProMap(psCPros));
        return vh;
    }

    @Override
    public ValueHolderV14 groupVoidEcodeQuery(Integer pageNum, Integer pageCount, Integer type, Long hour) {
        ValueHolderV14 vh = new ValueHolderV14<>();
        vh.setCode(0);
        vh.setMessage("success");
        String isActive = "N";
        Integer status = 2;
        //查询符合条件的商品
        Integer startIndex = (pageNum - 1) * pageCount;
        Timestamp timestamp = new Timestamp(System.currentTimeMillis() - hour * 60 * 60 * 1000);
        List<PsCPro> psCPros = proMapper.selectProVoid(isActive, startIndex, pageCount, timestamp, type, status);
        vh.setData(getPsCProMap(psCPros));
        return vh;
    }

    public List<HashMap<String, Object>> getPsCProMap(List<PsCPro> psCPros) {
        List<HashMap<String, Object>> result = new ArrayList<>();
        for (PsCPro psCPro : psCPros) {
            HashMap<String, Object> hashMap = new HashMap<>();
            hashMap.put("pro", psCPro);
            ProResult proResult = new ProResult();
            proResult.setPsCPro(psCPro);
            Long proId = psCPro.getId();
            List<ExtPsCSku> psCSkus = skuMapper.selectSkuByProId(proId);
            for (ExtPsCSku skus : psCSkus) {
                Long skusId = skus.getId();
                List<PsCSkugroup> psCSkugroups = skuGroupMapper.selectGroupBySkuId(skusId);
                skus.setPsCSkugroups(psCSkugroups);

            }
            hashMap.put("sku", psCSkus);
            result.add(hashMap);
        }
        return result;
    }
}
