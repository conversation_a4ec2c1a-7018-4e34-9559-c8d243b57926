package com.jackrain.nea.psext.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.core.db.Tools;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.psext.api.PsCBrandMatchSaveCmd;
import com.jackrain.nea.psext.common.PSConstants;
import com.jackrain.nea.psext.enums.BrandMatchRuleEnum;
import com.jackrain.nea.psext.mapper.PsCBrandMapper;
import com.jackrain.nea.psext.mapper.PsCBrandMatchItemMapper;
import com.jackrain.nea.psext.mapper.PsCBrandMatchMapper;
import com.jackrain.nea.psext.model.table.PsCBrand;
import com.jackrain.nea.psext.model.table.PsCBrandMatch;
import com.jackrain.nea.psext.model.table.PsCBrandMatchItem;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Objects;


/**
 * Created with IntelliJ IDEA.
 * @Author: jg.zhan
 * @Date:  2022/5/12 16:26
 * @Description:  品牌匹配主表保存接口
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps-ext")
public class PsCBrandMatchSaveCmdImpl extends CommandAdapter implements PsCBrandMatchSaveCmd {

    @Autowired
    private PsCBrandMatchMapper psCBrandMatchMapper;
    @Autowired
    private PsCBrandMatchItemMapper psCBrandMatchItemMapper;
    @Autowired
    private PsCBrandMapper psCBrandMapper;
    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        log.info("##BrandMatchSaveCmdImpl##execute##param:{}",param);
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        long objid = param.getLong("objid");
        JSONObject jo = fixColumn.getJSONObject("PS_C_BRAND_MATCH");
        JSONArray psCBrandMatchItem = fixColumn.getJSONArray("PS_C_BRAND_MATCH_ITEM");
        try {
            if (Objects.nonNull(jo)){
                //实例化
                Timestamp timestamp = new Timestamp(System.currentTimeMillis());
                long oneId = Integer.valueOf(session.getUser().getId()).longValue();
                long adClientId = Integer.valueOf(session.getUser().getClientId()).longValue();
                long adOrgId = Integer.valueOf(session.getUser().getOrgId()).longValue();
                String mEname = String.valueOf(session.getUser().getEname());
                String mName = String.valueOf(session.getUser().getName());

                //判断增加或修改操作
                if (Objects.nonNull(objid) && objid > 0) {
                    //更新操作
                    checkUpdate(session, objid);
                    jo.put("ID", objid);
                    jo.put("MODIFIEDDATE", timestamp);
                    jo.put("MODIFIERID", oneId);
                    jo.put("MODIFIERNAME", mName);
                    jo.put("MODIFIERENAME", mEname);
                    PsCBrandMatch brandMatch = JSONObject.parseObject(jo.toJSONString(), PsCBrandMatch.class);
                    //执行更新操作
                    psCBrandMatchMapper.updateById(brandMatch);

                } else {
                    checkParam(jo);
                    //增加操作
                    checkInsert(session);
                    Long id = Tools.getSequence("PS_C_BRAND_MATCH");
                    objid = id;
                    jo.put("ID",id);
                    jo.put("AD_CLIENT_ID", adClientId);
                    jo.put("AD_ORG_ID", adOrgId);
                    jo.put("OWNERID", oneId);
                    jo.put("MODIFIERID", oneId);
                    jo.put("CREATIONDATE", timestamp);
                    jo.put("MODIFIEDDATE", timestamp);
                    jo.put("MODIFIERNAME", mName);
                    jo.put("OWNERNAME", mName);
                    jo.put("MODIFIERENAME", mEname);
                    jo.put("OWNERENAME", mEname);
                    PsCBrandMatch insertBrandMatch = JSONObject.parseObject(jo.toJSONString(), PsCBrandMatch.class);
                    //执行新增操作
                    psCBrandMatchMapper.insert(insertBrandMatch);
                    //增加成功返回信息增加
                    JSONObject jsonObj = new JSONObject();
                    jsonObj.put("tablename", "PS_C_BRAND_MATCH");
                    jsonObj.put("objid", id);
                    vh.put("data", jsonObj);
                }
            }
            //保存明细表
            if (CollectionUtils.isNotEmpty(psCBrandMatchItem)){
                saveItem(psCBrandMatchItem,objid,session);
            }
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", Resources.getMessage("Success", session.getLocale()));
            return vh;
        } catch (NDSException ne){
            log.error("##BrandMatchSaveCmdImpl##execute##Error:{}",ne);
            vh.put("code", ResultCode.FAIL);
            vh.put("message", ne.getMessage());
            return vh;
        }catch (Exception e) {
            log.error("##BrandMatchSaveCmdImpl##execute##Error:{}",e);
            vh.put("code", ResultCode.FAIL);
            vh.put("message","系统异常！");
        }
        return vh;
    }

    /**
     * 保存明细表
     * @param psCBrandMatchItem
     * @param masterId
     * @param session
     */
    private void saveItem(JSONArray psCBrandMatchItem,Long masterId,QuerySession session){
        long oneId = Integer.valueOf(session.getUser().getId()).longValue();
        long adClientId = Integer.valueOf(session.getUser().getClientId()).longValue();
        long adOrgId = Integer.valueOf(session.getUser().getOrgId()).longValue();
        String mEname = String.valueOf(session.getUser().getEname());
        String mName = String.valueOf(session.getUser().getName());

        List<PsCBrandMatchItem> psCBrandMatchItemList = JSONObject.parseArray(psCBrandMatchItem.toJSONString(), PsCBrandMatchItem.class);
        for (PsCBrandMatchItem cBrandMatchItem : psCBrandMatchItemList) {
            PsCBrand psCBrand = psCBrandMapper.selectById(cBrandMatchItem.getPsCBrandId());
            //校验是否符合规则
            checkItemParam(masterId,psCBrand.getEcode());
            //更新
            if (Objects.nonNull(cBrandMatchItem.getId()) && cBrandMatchItem.getId() > 0){
                if (Objects.nonNull(psCBrand)){
                    cBrandMatchItem.setPsCBrandMatchId(masterId);
                    cBrandMatchItem.setPsCBrandEcode(psCBrand.getEcode());
                    cBrandMatchItem.setPsCBrandEname(psCBrand.getEname());
                    cBrandMatchItem.setModifieddate(new Date());
                    cBrandMatchItem.setModifierename(mEname);
                    cBrandMatchItem.setModifiername(mName);
                    psCBrandMatchItemMapper.updateById(cBrandMatchItem);
                }
            }else {
                checkItemInsert(session,psCBrand.getEcode());
                //新增
                if (Objects.nonNull(psCBrand)){
                    Long itemId = Tools.getSequence("PS_C_BRAND_MATCH");
                    cBrandMatchItem.setId(itemId);
                    cBrandMatchItem.setPsCBrandMatchId(masterId);
                    cBrandMatchItem.setPsCBrandEcode(psCBrand.getEcode());
                    cBrandMatchItem.setPsCBrandEname(psCBrand.getEname());
                    cBrandMatchItem.setAdClientId(adClientId);
                    cBrandMatchItem.setAdOrgId(adOrgId);
                    cBrandMatchItem.setCreationdate(new Date());
                    cBrandMatchItem.setModifieddate(new Date());
                    cBrandMatchItem.setModifierename(mEname);
                    cBrandMatchItem.setModifiername(mName);
                    cBrandMatchItem.setOwnerename(mEname);
                    cBrandMatchItem.setOwnerid(oneId);
                    cBrandMatchItem.setOwnername(mName);
                    psCBrandMatchItemMapper.insert(cBrandMatchItem);
                }
            }
        }
    }
    /**
     * 校验新增数据
     * @param session
     */
    private void checkInsert(QuerySession session) {
        List<PsCBrandMatch> psCBrandMatchList = psCBrandMatchMapper.selectList(new QueryWrapper<PsCBrandMatch>().eq(PsCBrandMatch.COL_ISACTIVE, PSConstants.Y));
        if (CollectionUtils.isNotEmpty(psCBrandMatchList)) {
            throw new NDSException(Resources.getMessage("当前已存在解析规则！", session.getLocale()));
        }
    }

    /**
     * 校验更新数据
     * @param session
     * @param objid
     */
    private void checkUpdate(QuerySession session, long objid) {
        //判断当前修改的记录是否存在
        PsCBrandMatch psCBrandMatch = psCBrandMatchMapper.selectById(objid);
        if (Objects.isNull(psCBrandMatch)) {
            throw new NDSException(Resources.getMessage("当前记录已不存在！", session.getLocale()));
        }
        List<PsCBrandMatchItem> psCBrandMatchItemList = psCBrandMatchItemMapper.selectList(new QueryWrapper<PsCBrandMatchItem>()
                .eq(PsCBrandMatchItem.COL_ISACTIVE, PSConstants.Y)
                .eq(PsCBrandMatchItem.COL_PS_C_BRAND_MATCH_ID, objid));
        if (CollectionUtils.isNotEmpty(psCBrandMatchItemList)){
            throw new NDSException(Resources.getMessage("明细记录已存在，不允许编辑！", session.getLocale()));
        }
        if (PSConstants.N.equals(psCBrandMatch.getIsactive())){
            throw new NDSException(Resources.getMessage("当前记录已作废，不允许保存！", session.getLocale()));
        }
    }

    private void checkParam(JSONObject jo) {
        if (Objects.isNull(jo)){
            throw new NDSException("请传入参数");
        }
        String parseFields = jo.getString("PARSE_FIELDS");
        String parseNumber = jo.getString("PARSE_NUMBER");
        String parseRule = jo.getString("PARSE_RULE");
        //校验参数
        if (StringUtils.isEmpty(parseFields)){
            throw new NDSException("请传入解析字段");
        }
        if (StringUtils.isEmpty(parseNumber)){
            throw new NDSException("请传入解析位数");
        }
        if (StringUtils.isEmpty(parseRule)){
            throw new NDSException("请传入解析规则");
        }
    }


    /**
     * 校验新增数据
     * @param session
     */
    private void checkItemInsert(QuerySession session,String brandEcode) {
        List<PsCBrandMatchItem> psCBrandMatchList = psCBrandMatchItemMapper.selectList(new QueryWrapper<PsCBrandMatchItem>()
                .eq(PsCBrandMatchItem.COL_ISACTIVE, PSConstants.Y)
                .eq(PsCBrandMatchItem.COL_PS_C_BRAND_ECODE,brandEcode));
        if (CollectionUtils.isNotEmpty(psCBrandMatchList)) {
            throw new NDSException(Resources.getMessage("当前记录已存在！", session.getLocale()));
        }
    }

    private void checkItemParam(Long psCBrandMatchId,String psCBrandEcode) {
        if (Objects.isNull(psCBrandMatchId)){
            throw new NDSException("请关联到主表信息！");
        }
        //若“品牌编码”的长度与“解析位数”长度不匹配时，则提示：“品牌编码与解析位数不符！”
        // （例：品牌编码只有3位，解析规则为“等于”，解析位数选择了第1位至第4位时；或品牌编码只有3位，解析规则为“包含”时，解析位数选择了第1至2位）
        PsCBrandMatch psCBrandMatch = psCBrandMatchMapper.selectById(psCBrandMatchId);
        if (Objects.isNull(psCBrandMatch)){
            throw new NDSException("请检查主表数据！");
        }
        String parseNumber = psCBrandMatch.getParseNumber();
        String parseRule = psCBrandMatch.getParseRule();
        int start = Integer.parseInt(parseNumber.substring(parseNumber.indexOf("第")+1, parseNumber.indexOf("第") + 2));
        int end = Integer.parseInt(parseNumber.substring(parseNumber.indexOf("至")+1, parseNumber.indexOf("至") + 2));
        if (BrandMatchRuleEnum.equals.getCode().equals(parseRule) || BrandMatchRuleEnum.include.getCode().equals(parseRule)){
            if ((end - start) < psCBrandEcode.length() ){
                throw new NDSException("品牌编码与解析位数不符！");
            }
        }
    }
}
