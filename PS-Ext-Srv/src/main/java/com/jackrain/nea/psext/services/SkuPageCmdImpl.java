package com.jackrain.nea.psext.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.psext.api.SkuPageCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 郑立轩
 * @since: 2019/4/1
 * create at : 2019/4/1 16:13
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps-ext")
public class SkuPageCmdImpl extends CommandAdapter implements SkuPageCmd {
    @Autowired
    private SkuPageService skuPageService;

    @Override
    public ValueHolderV14 skuPage(JSONObject object) {
        return skuPageService.skuPage(object);
    }
}
