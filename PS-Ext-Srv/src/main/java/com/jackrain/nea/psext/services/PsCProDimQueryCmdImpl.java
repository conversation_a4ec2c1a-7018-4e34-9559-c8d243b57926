package com.jackrain.nea.psext.services;

import com.jackrain.nea.psext.api.PsCProDimQueryCmd;
import com.jackrain.nea.psext.mapper.PsCProMapper;
import com.jackrain.nea.psext.mapper.PsCProdimItemMapper;
import com.jackrain.nea.psext.mapper.PsCProdimMapper;
import com.jackrain.nea.psext.model.table.PsCProdim;
import com.jackrain.nea.psext.model.table.PsCProdimItem;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps-ext")
public class PsCProDimQueryCmdImpl implements PsCProDimQueryCmd {

    @Autowired
    private PsCProdimMapper mapper;


    @Autowired
    private PsCProdimItemMapper itemMapper;

    @Autowired
    private PsCProMapper psCProMapper;

    @Override
    public PsCProdim getById(Long id) {
        return mapper.selectById(id);
    }

    @Override
    public List<PsCProdim> getByIds(List<Long> ids) {
        return mapper.selectBatchIds(ids);
    }

    @Override
    public boolean validate(Long proId, Long dmiItemId) {
        if (dmiItemId == null || proId == null) {
            return false;
        }

        PsCProdimItem psCProdimItem = itemMapper.selectById(dmiItemId);

        if (psCProdimItem == null) {
            return false;
        }

        PsCProdim psCProdim = mapper.selectById(psCProdimItem.getPsCProdimId());
        if (psCProdim == null) {
            return false;
        }

        String columnName = psCProdim.getAdProcolumnName();

        if (columnName == null) {
            return false;
        }
        return psCProMapper.queryByProdimItemCodeAndId(proId, columnName, dmiItemId) != null;
    }
}
