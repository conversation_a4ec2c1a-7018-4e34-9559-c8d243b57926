package com.jackrain.nea.psext.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.psext.api.PsCPriceStrategySaveCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps-ext")
public class PsCPriceStrategySaveCmdImpl extends CommandAdapter implements PsCPriceStrategySaveCmd {

    @Autowired
    PsCPriceStrategyService psCPriceStrategyService;


    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return psCPriceStrategyService.savePsCPriceStrategyInfo(session);
    }
}
