package com.jackrain.nea.psext.services;

import com.jackrain.nea.psext.api.PsCProGroupVoidCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/2/8 10:27
 * @Description
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps-ext")
public class PsCProGroupVoidCmdImpl extends CommandAdapter implements PsCProGroupVoidCmd {

    @Resource
    private SkuGroupSaveService skuGroupSaveService;


    @Override
    public ValueHolderV14 voidProGroup(List<String> codes) {
        return skuGroupSaveService.voidProGroup(codes);
    }
}
