package com.jackrain.nea.psext.services;

import com.jackrain.nea.psext.api.PushProductToWmsCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 杜胜辉
 * @since: 2019/6/3
 * create at : 2019/6/3 13:33
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps-ext")
public class PushProductToWmsCmdImpl implements PushProductToWmsCmd {

    @Autowired
    private PushProductToWmsService pushProductToWmsService;
    @Override
    public ValueHolderV14 pushProductToWms() {
        return pushProductToWmsService.pushProductToWMS();
    }
}
