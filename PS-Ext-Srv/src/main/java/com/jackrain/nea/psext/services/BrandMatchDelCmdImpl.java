package com.jackrain.nea.psext.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.psext.api.BrandMatchDelCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

/**
 * Created with IntelliJ IDEA.
 * @Author: jg.zhan
 * @Date:  2022/5/12 16:26
 * @Description:  品牌匹配主表删除接口
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps-ext")
public class BrandMatchDelCmdImpl extends CommandAdapter implements BrandMatchDelCmd {
    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return  null;
    }
}
