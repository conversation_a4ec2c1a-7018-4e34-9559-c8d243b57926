package com.jackrain.nea.psext.services;

import com.jackrain.nea.psext.api.SkuGroupSaveCmd;
import com.jackrain.nea.psext.request.ProGroupRequest;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * 组合商品保存接口【新增,修改】
 *
 * @author: hly
 * create by 2019-02-28
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps-ext")
public class SkuGroupSaveCmdImpl extends CommandAdapter implements SkuGroupSaveCmd {

    @Autowired
    private SkuGroupSaveService skuGroupSaveService;

    @Override
    public ValueHolderV14<HashMap<String, String>> groupSkuSave(ProGroupRequest proRequest, QuerySession querySession) {
        return skuGroupSaveService.excute(proRequest,querySession);
    }
}
