package com.jackrain.nea.test;

import com.google.common.collect.Lists;
import com.jackrain.nea.ip.model.qimen.QimenSingleitemSynchronizeModel;
import com.jackrain.nea.psext.mapper.PsCSkuMapper;
import com.jackrain.nea.psext.mapper.PsCTowmsMapper;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.psext.model.table.PsCSku;
import com.jackrain.nea.psext.model.table.PsCTowms;
import com.jackrain.nea.psext.services.PushProductToWmsService;
import com.jackrain.nea.psext.tableExtend.PsCProExtend;
import com.jackrain.nea.psext.tableExtend.PsCSkuExtend;
import com.jackrain.nea.psext.tableExtend.PsCTowmsExtend;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@RunWith(SpringRunner.class)
@EnableAutoConfiguration
@Transactional
@MapperScan({
        "com.jackrain.nea.psext.mapper",
        "com.jackrain.nea.core.webaction.mapper",
        "com.jackrain.nea.utility"

})

@SpringBootTest(classes = {

        PsCSku.class,
        PsCTowms.class,
        PsCPro.class,
        PsCSkuMapper.class,
        PsCTowmsMapper.class,
        PsCPro.class,
        PsCProExtend.class,
        PsCSkuExtend.class,
        PsCTowmsExtend.class,

        // 以下是你需要用到什么就加载什么
        //PsExtServiceApplication.class
})
public class SyncProductTest {

    @Autowired
    PushProductToWmsService pushProductToWms;
    @Autowired
    PushProductToWmsService pushProductToWmsService;
    /*@Autowired
    R3MqSendHelper r3MqSendHelper;*/




    @Test
    public void test1() {
        QimenSingleitemSynchronizeModel qimenSingleitemSynchronizeModel = new QimenSingleitemSynchronizeModel();
        qimenSingleitemSynchronizeModel.setSupplierName("商品测试");
        qimenSingleitemSynchronizeModel.setSupplierCode("productSync");
        qimenSingleitemSynchronizeModel.setWarehouseCode("test");

        qimenSingleitemSynchronizeModel.setCustomerId("c1470205802731");
        qimenSingleitemSynchronizeModel.setOwnerCode("qwe");
        qimenSingleitemSynchronizeModel.setActionType("add");
        QimenSingleitemSynchronizeModel.Item item = new QimenSingleitemSynchronizeModel.Item();
        item.setItemCode("itemcode");
        item.setItemName("itemname");
        item.setBarCode("itemBarCode");
        item.setItemType("itemType");
        Map<String,String> map = new HashMap();
        map.put("psCTowmsId","1");
        qimenSingleitemSynchronizeModel.setExtendProps(map);
        qimenSingleitemSynchronizeModel.setItem(item);

        pushProductToWms.productSyncWms(Lists.newArrayList(qimenSingleitemSynchronizeModel));
    }

    @Test
    @Rollback(false)
    public void test2() {
        ValueHolderV14 valueHolderV14 = pushProductToWms.pushProductToWMS();
        System.out.println(valueHolderV14);

    }

}
