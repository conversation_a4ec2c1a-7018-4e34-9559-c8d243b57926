package com.jackrain.nea.ps.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.common.ReferenceUtil;
import com.jackrain.nea.core.schema.Column;
import com.jackrain.nea.core.schema.Table;
import com.jackrain.nea.core.schema.TableManager;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.CproQueryCmd;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.Tools;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.async.AsyncTask;
import com.jackrain.nea.web.common.Handle;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import com.jackrain.nea.web.query.QuerySessionImpl;
import com.jackrain.nea.web.services.QueryAKCmd;
import com.jackrain.nea.web.utils.Importer;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

@Slf4j
@Api(value = "PS_C_PRO", description = "商品档案：商品属性导入")
@RestController
public class CproImportCtrl {
    @Autowired
    private Handle handle;

 /*   @Reference(version = "1.0", group = "ps", timeout = 1000 * 60 * 30)
    private CproQueryCmd cproQueryCmd;*/


    @ApiOperation(value = "取商品属性选项")
    @RequestMapping(path = "/api/cs/ps/proImport/getAttributes", method = RequestMethod.POST)
    public JSONObject proImport(HttpServletRequest request,
                                @RequestParam(value = "param", required = true) String param) throws Exception {
        //User user = (User) Security4Utils.getUser("root");
        QuerySession session = new QuerySessionImpl(request);
        TableManager tm = session.getTableManager();
        JSONObject jo = JSON.parseObject(param);
        Table table = null;
        if (jo.getString("table") != null && table == null) {
            table = tm.getTable(jo.getString("table"));
        }
        if (table == null) {
            throw new NDSException("tablenull");
        }
        ValueHolder result;
        try {
            result = getAttriValue(table, session);
        } catch (Exception e) {
            result = new ValueHolder();
            result.put("code", -1);
            result.put("message", e.getMessage());
            return result.toJSONObject();
        }
        return result.toJSONObject();
    }

    /**
     * 下载仓店属性导入模板
     *
     * @param request
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "下载导入模板", notes = "生成导入excel模板")
    @RequestMapping(path = "/api/cs/ps/proImport/download", method = RequestMethod.GET)
    public JSONObject downloadImpotTemplate(HttpServletRequest request,
                                            @RequestParam(value = "param", required = true) String param) throws Exception {
        ValueHolder valueHolder = new ValueHolder();
        QuerySession session = new QuerySessionImpl(request);
        User user = session.getUser();
        //User user = (User) Security4Utils.getUser("root");
        TableManager tm = session.getTableManager();
        JSONObject jo = JSON.parseObject(param, Feature.OrderedField);
        ValueHolder result;
        Table table = null;
        if (jo.containsKey("table") && table == null) {
            table = tm.getTable(jo.getString("table"));
        }
        if (table == null) {
            throw new NDSException("tablenull");
        }
        //生成excel模板
        Importer importer = new Importer();
        String[] columns = jo.getJSONArray("columns").toArray(new String[]{});
        StringBuilder st = new StringBuilder();
        for (String v : columns) {
            st.append("-").append(v);
        }
        if (columns.length <= 0) {
            valueHolder.put("code", -1);
            valueHolder.put("message", "参数不能为空");
        }

        String baseDir = File.separator + "import" + File.separator + user.getId() + File.separator;
        String virtualPath = importer.generateExcelTemplate(session, user, table, columns, baseDir, table.getDescription() + "导入" + ".xls");

        valueHolder.put("code", 0);
        valueHolder.put("data", virtualPath);
        return valueHolder.toJSONObject();
    }

    @ApiOperation(value = "导入商品属性", notes = "导入商品属性")
    @RequestMapping(path = "/api/cs/ps/proImport/importSave", method = RequestMethod.POST)
    public JSONObject importRecords(HttpServletRequest request
            , @RequestParam(value = "file", required = true) MultipartFile file
            , @RequestParam(value = "table", required = true) String tableName
            , @RequestParam(value = "attr", required = true) String attr) {
        ValueHolder result = new ValueHolder();

        QuerySession session = new QuerySessionImpl(request);
        DefaultWebEvent event = new DefaultWebEvent("importSave", request);
        session.setEvent(event);

        TableManager tm = session.getTableManager();
        Table table = tm.getTable(tableName);
        HSSFWorkbook workbook;
        try {
            workbook = getExcelWorkbook(file, tableName, table);
        } catch (Exception e) {
            result.put("code", -1);
            result.put("message", e.getMessage());
            return result.toJSONObject();
        }

        CproImportCtrl cproImportCtrl = ApplicationContextHandle.getBean(CproImportCtrl.class);
        JSONObject ret = cproImportCtrl.importExecute(session, workbook, tableName, attr);
        return ret;

    }

    @AsyncTask("导入")
    public JSONObject importExecute(QuerySession session, HSSFWorkbook workbook,
                                    String tableName, String attr) {
        User user = session.getUser();
        // User user = (User) Security4Utils.getUser("root");
        TableManager tm = session.getTableManager();
        ValueHolder result = new ValueHolder();
        Table table = tm.getTable(tableName);
        int rowCount = 0;
        int failedCount = 0;
        if (table == null || table.getDescription() == null) {
            result.put("code", -1);
            result.put("message", "后台表不存在或后台表描述异常！");
            return result.toJSONObject();
        }
        if (StringUtils.isEmpty(attr)) {
            result.put("code", -1);
            result.put("message", "请先选择属性！");
            return result.toJSONObject();
        }
        Callable<JSONObject> task = new Callable<JSONObject>() {
            @Override
            public JSONObject call() throws Exception {
                ValueHolder result = new ValueHolder();
                int rowCount = 0;
                int failedCount = 0;
                try {

                    SortedMap<Integer, JSONObject> errors = new TreeMap<>();
                    Sheet sheet = getImportSheet(workbook, table.getDescription());
                    Importer importer = new Importer();
                    ArrayList colummns = Lists.newArrayList();
                    Column ecodeColumn = table.getColumn("ECODE");
                    if (ecodeColumn == null) {
                        throw new NDSException(String.format("表%s中不存在列%s！", table.getDescription(), "ECODE"));
                    }
                    colummns.add(ecodeColumn);
                    Column attrColumn = table.getColumn(attr);
                    if (attrColumn == null) {
                        throw new NDSException(String.format("表%s中不存在列%s！", table.getDescription(), attr));
                    }
                    colummns.add(attrColumn);
                    HashMap<Column, Integer> titleRow = importer.processTitleRowEx(session, user, sheet, table, colummns);
                    if (!titleRow.containsKey(ecodeColumn)) {
                        throw new NDSException(String.format("excel中不存在列%s！", ecodeColumn.getDescription(session.getLocale())));
                    }
                    if (!titleRow.containsKey(attrColumn)) {
                        throw new NDSException(String.format("excel中不存在列%s！", attrColumn.getDescription(session.getLocale())));
                    }
                    HashMap<String, Object> orignalValue = new HashMap<>();
                    HashMap<Integer, JSONObject> rows = importer.processRows(errors, session, sheet, table, titleRow, orignalValue);
                    ValueHolder valueHolder;
                    DefaultWebEvent event = new DefaultWebEvent("dosave", Maps.newHashMap());
                    String center = table.getCategory().getSubSystem().getCenter();
                    List<String> nilData = Lists.newArrayList();
                    Map<Integer, String> errorMap = new HashMap<>();
                    JSONObject batchRequests = getBatchDatas(tableName, rows, attr, session, nilData, errorMap, orignalValue, titleRow);
                    log.debug(LogUtil.format("batchRequestsgg：") + batchRequests);
                    rowCount = rows.size() + errors.size();
                    failedCount = errors.size();
                    if (batchRequests == null) {
                        if (errors != null) {
                            JSONArray errorArray = new JSONArray();
                            for (Integer rowIndex : errors.keySet()) {
                                errorArray.add(errors.get(rowIndex));
                                result.put("data", errorArray);
                            }
                        }
                        //在errors增加错误
                        for (Integer key : errorMap.keySet()) {
                            JSONObject currentError = new JSONObject();
                            currentError.put("rowIndex", key);
                            currentError.put("message", errorMap.get(key));
                            errors.put(key, currentError);
                        }
                        if (errors.size() > 0) {
                            result.put("path", importer.generateResultExcel(user, table.getDescription() + "-错误信息", errors));
                        }
                        result.put("code", -1);
                        result.put("message", "插入成功记录数：0，共" + rowCount + "条失败:" + getErrorMsg(nilData));
                        return result.toJSONObject();
                    } else {
                        if (rowCount <= 100) {
                            log.debug(LogUtil.format("storeImport.invokeBigPipe, table: %s, 一共%d条, 请求参数为%s", tableName, rowCount, batchRequests));
                        } else {
                            log.debug(LogUtil.format("storeImport.invokeBigPipe, table: %s, 一共%d条", tableName, rowCount));
                        }
                        valueHolder = handle.batchProcess(user, batchRequests, center, event, "save");
                        if (rowCount <= 100) {
                            log.debug(LogUtil.format("storeImport.invokeBigPipe.END, table: %s, 一共%d条, 返回值是%s", tableName, rowCount, valueHolder.toJSONObject()));
                        } else {
                            log.debug(LogUtil.format("storeImport.invokeBigPipe.END, table: %s, 一共%d条", tableName, rowCount));
                        }
                        failedCount = getImportFailedCount(valueHolder, rowCount, failedCount, errors);
                        //在errors增加错误

                        result = getImportFruit(user, table.getDescription() + "导入", importer, failedCount, rowCount, errors, nilData);
                        for (Integer key : errorMap.keySet()) {
                            JSONObject currentError = new JSONObject();
                            currentError.put("rowIndex", key);
                            currentError.put("message", errorMap.get(key));
                            errors.put(key, currentError);
                        }
                        if (errors.size() > 0) {
                            result.put("path", importer.generateResultExcel(user, table.getDescription() + "-错误信息", errors));
                        }
                    }
                } catch (Exception e) {
                    result.put("code", -1);
                    result.put("message", e.getMessage());
                    return result.toJSONObject();
                }
                return result.toJSONObject();
            }
        };
        //设置线程
        ExecutorService executorService = Executors.newSingleThreadScheduledExecutor();
        //调用任务
        Future<JSONObject> future = executorService.submit(task);
        try {
            //设置执行时间
            //JSONObject importCallResult=future.get(5, TimeUnit.MINUTES);(原设置5分钟)
            JSONObject importCallResult = future.get(1, TimeUnit.MINUTES);
            //测试
            //JSONObject importCallResult=future.get(1, TimeUnit.MICROSECONDS);
            return importCallResult;
        } catch (InterruptedException e) {
            result.put("code", -1);
            result.put("message", e.getMessage());
            return result.toJSONObject();
        } catch (ExecutionException e) {
            result.put("code", -1);
            result.put("message", e.getMessage());
            return result.toJSONObject();
        } catch (TimeoutException e) {
            result.put("code", 0);
            result.put("isTimeOut", true);
        } catch (Exception e) {
            result.put("code", -1);
            result.put("message", e.getMessage());
            return result.toJSONObject();
        } finally {
            executorService.shutdown();
        }
        return result.toJSONObject();
    }

    /*
     * 取ExeclSheet只取第一个匹配到
     * */
    private Sheet getImportSheet(HSSFWorkbook workbook, String tableName) {
        HSSFSheet sheet = workbook.getSheet(tableName);
        if (sheet == null) {
            throw new NDSException("Execl文档中不存：‘" + tableName + "’相同名工作表");
        } else {
            return sheet;
        }

    }

    private String getErrorMsg(Collection<String> nilData) {
        StringBuffer nilMsg = new StringBuffer();
        if (nilData != null && nilData.size() > 0) {
            for (String data : nilData) {
                nilMsg.append(System.getProperty("line.separator")).append(data);
            }
        }
        return nilMsg.toString();
    }

    /*
     * 返回导入结果
     * */
    private ValueHolder getImportFruit(User user, String fileName, Importer importer, int failedCount, int rowCount, SortedMap<Integer, JSONObject> errors, Collection<String> nilData) throws Exception {
        ValueHolder result = new ValueHolder();
        String errorMsg = getErrorMsg(nilData);
        if (failedCount > 0) {
            result.put("code", -1);
            JSONArray errorArray = new JSONArray();
            for (Integer rowIndex : errors.keySet()) {
                errorArray.add(errors.get(rowIndex));
            }
            result.put("data", errorArray);

            result.put("message", "插入成功记录数：" + (rowCount - failedCount - nilData.size()) + "，插入失败记录数：" + failedCount + errorMsg);
        } else {
            if (StringUtils.isNoneBlank(errorMsg)) {
                result.put("code", -1);
                result.put("message", "插入成功记录数：" + (rowCount - nilData.size()) + errorMsg);
                //result.put("path", importer.generateResultExcel(user, fileName, errors));
            } else {
                result.put("code", 0);
                result.put("message", "插入成功记录数：" + rowCount);
            }
        }
        return result;
    }

    /*
     * 返回导入个数结果
     * */
    private int getImportFailedCount(ValueHolder valueHolder, int rowCount, int failedCount, SortedMap<Integer, JSONObject> errors) {
        JSONObject saveResult = valueHolder.toJSONObject();
        int code = Tools.getInt(String.valueOf(saveResult.get("code")), 0);
        if (code < 0) {
            //只要返回值小于0，则认定为整批失败
            failedCount = rowCount;
            errors.clear();
            JSONObject currentError = new JSONObject();
            currentError.put("rowIndex", -1);
            currentError.put("message", saveResult.getString("message"));
            errors.put(-1, currentError);
        } else {
            //说明批次调用本身没问题，有正常返回
            JSONObject subResults = saveResult.getJSONObject("results");
            for (String rowIndex : subResults.keySet()) {
                JSONObject subResult = subResults.getJSONObject(rowIndex);
                int subCode = Tools.getInt(String.valueOf(subResult.get("code")), 0);
                if (subCode < 0) {
                    JSONObject currentError = new JSONObject();
                    Integer nowIndex = 0;
                    try {
                        nowIndex = Integer.parseInt(rowIndex);
                    } catch (Exception e) {

                    }
                    currentError.put("rowIndex", nowIndex + 1);
                    String message = subResult.getString("message");
                    JSONArray tempErrorData = subResult.getJSONArray("data");
                    if (tempErrorData != null && tempErrorData.size() > 0) {
                        String tempMessage = tempErrorData.getJSONObject(0).getString("message");
                        if (tempMessage != null && !tempMessage.isEmpty()) {
                            message = tempMessage;
                        }
                    }
                    currentError.put("message", message);
                    currentError.put("isInner", true);
                    errors.put(Tools.getInt(rowIndex, -1), currentError);
                    failedCount++;
                }
            }
        }
        return failedCount;
    }


    private JSONObject getBatchDatas(String tableName, HashMap<Integer, JSONObject> rows, String arrtId, QuerySession session, List<String> nilData, Map<Integer, String> errorMap, HashMap<String, Object> orignalValue, HashMap<Column, Integer> columnMap) {
        JSONObject batchRequests = new JSONObject();
        batchRequests.put("table", tableName);
        JSONObject datas = new JSONObject();
        batchRequests.put("data", datas);

        int execCount = rows.size() / 500;
        if (rows.size() % 500 > 0) {
            execCount = execCount + 1;
        }
        Map<String, HashMap<String, String>> queryCproData = new HashMap<String, HashMap<String, String>>();
        Object o = ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(), "com.jackrain.nea.ps.api.CproQueryCmd", "ps", "1.0");
        //先从数据库查出所有数据
        for (int i = 1; i <= execCount; i++) {
            int rowCount = 0;
            List<String> ecodes = new ArrayList<String>();
            for (Integer rowIndex : rows.keySet()) {
                rowCount = rowCount + 1;
                if (rowCount <= (i * 500) && rowCount > ((i - 1) * 500)) {
                    JSONObject importRecord = rows.get(rowIndex);
                    String ecode = "";
                    if (importRecord.get("ECODE") == null) {
                        continue;
                    } else {
                        ecode = importRecord.get("ECODE").toString();
                        ecodes.add(ecode);
                    }
                }
            }
            if (ecodes.size() > 0) {
                Map<String, HashMap<String, String>> cproMaps = ((CproQueryCmd) o).bactchEcode(ecodes, arrtId);
                if (cproMaps != null) {
                    queryCproData.putAll(cproMaps);
                }
            }
        }
        Map<Integer, String> isAK = getTable(columnMap, arrtId);

        if (isAK != null) {
            List<Long> retableIds = new ArrayList<>();
            for (String key : queryCproData.keySet()) {
                HashMap<String, String> idEame = queryCproData.get(key);
                for (String keyId : idEame.keySet()) {
                    if (idEame.get(keyId) != null) {
                        if (idEame.get(keyId) != null) {
                            try {
                                Long retableId = Long.parseLong(idEame.get(keyId));
                                retableIds.add(retableId);
                            } catch (Exception e) {

                            }

                        }
                    }
                }
            }
            User user = session.getUser();
            //User user = (User) Security4Utils.getUser("root");
            if (retableIds != null && retableIds.size() > 0) {
                for (Integer tableId : isAK.keySet()) {
                    String center = session.getTableManager().getTable(tableId).getCategory().getSubSystem().getCenter();
                    String[] gv = center.split(":");
                    if (gv.length == 2 || gv.length > 2) {
                        QueryAKCmd queryAKCmd = (QueryAKCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(), "com.jackrain.nea.web.services.QueryAKCmd", gv[0], gv[1]);
                        ValueHolder ak = queryAKCmd.QueryByIDs(user, isAK.get(tableId), retableIds);
                        if (ak.isOK()) {
                            JSONObject json = ak.toJSONObject();
                            JSONArray arr = json.getJSONArray("data");
                            Map<Long, String> aks = Maps.newHashMap();
                            arr.forEach(item -> {
                                JSONObject obj = (JSONObject) item;
                                for (String key : queryCproData.keySet()) {
                                    HashMap<String, String> idEame = queryCproData.get(key);
                                    for (String keyId : idEame.keySet()) {
                                        if (idEame.get(keyId) != null && obj.get("ID") != null && idEame.get(keyId).equals(obj.getString("ID"))) {
                                            idEame.put(keyId, obj.getString("AK"));
                                        }
                                    }
                                }
                            });
                        }
                    }
                }
            }
        }
        for (Integer rowIndex : rows.keySet()) {
            JSONObject importRecord = rows.get(rowIndex);
            String ecode = "";
            if (importRecord.get("ECODE") == null) {
                errorMap.put((rowIndex + 1), "记录款号无法取得");
                nilData.add("第" + (rowIndex + 1) + "记录款号无法取得");
                continue;
            } else {
                ecode = importRecord.get("ECODE").toString();
                importRecord.remove("ECODE");
            }
            getRecyclehandle(importRecord);
            JSONObject jor = new JSONObject();
            HashMap<String, String> idEame = queryCproData.get(ecode);
            if (idEame == null || idEame.size() == 0) {
                //查询不到记录
                errorMap.put((rowIndex + 1), "记录不存在:款号-" + ecode);
                nilData.add("第" + (rowIndex + 1) + "行记录不存在:款号-" + ecode);
                continue;
            }
            String id = "";
            String proBeforeName = "标准商品导入属性";
            for (String key : idEame.keySet()) {
                id = key;
                if (idEame.get(key) != null) {
                    proBeforeName = idEame.get(key);
                }
            }
            jor.put("objid", id);
            JSONObject fixcolumn = new JSONObject();
            fixcolumn.put(tableName, importRecord);
            jor.put("fixcolumn", fixcolumn);
            JSONObject beforevalue = new JSONObject();
            JSONObject afterValue = new JSONObject();
            String beforeKey = "";
            Object afterVlaue = "";
            for (String beforeName : importRecord.keySet()) {
                beforeKey = beforeName;
                afterVlaue = importRecord.get(beforeName);
                String orignalKey = beforeKey + afterVlaue;
                if (orignalValue.containsKey(orignalKey)) {
                    afterVlaue = orignalValue.get(orignalKey);
                }
                try {
                    double douNumber1 = Double.parseDouble(afterVlaue.toString());
                    String s1 = BigDecimal.valueOf(douNumber1).stripTrailingZeros().toPlainString();
                    afterVlaue = s1;
                } catch (Exception e) {

                }
                beforevalue.put(beforeKey, proBeforeName);
                afterValue.put(beforeKey, afterVlaue);
            }

            JSONObject beforeObject = new JSONObject();
            beforeObject.put(tableName, beforevalue);
            jor.put("beforevalue", beforeObject);
            JSONObject afterObject = new JSONObject();
            afterObject.put(tableName, afterValue);
            jor.put("aftervalue", afterObject);
            datas.put(rowIndex.toString(), jor);
        }
        if (datas.size() <= 0) {
            return null;
        }
        return batchRequests;
    }

    private Map getTable(HashMap<Column, Integer> columnMap, String beforeName) {
        Map<Integer, String> tablemap = new HashMap<>();
        for (Column column : columnMap.keySet()) {
            Table refTable = column.getReferenceTable();
            if (refTable != null) {
                if (column.getName() != null && beforeName != null && column.getName().equals(beforeName)) {
                    tablemap.put(refTable.getId(), refTable.getName());
                    return tablemap;
                }
            }
        }
        return null;
    }

    private void getRecyclehandle(JSONObject importRecord) {
        if (importRecord.get("RECYCLE") != null) {
            try {
                int recycle = Double.valueOf(String.valueOf(importRecord.get("RECYCLE")).trim()).intValue();
                //int recycle= Integer.parseInt(String.valueOf(importRecord.get("RECYCLE")));
                ///importRecord.put("PREINDAYS", new BigDecimal((float)recycle/2).setScale(0, BigDecimal.ROUND_HALF_UP));
            } catch (Exception e) {
                log.debug(LogUtil.format("发货在途天数转化出错,Error：{}，RECYCLE：{}"), Throwables.getStackTraceAsString(e),
                        importRecord.get("RECYCLE"));
            }
        }
    }

    private HSSFWorkbook getExcelWorkbook(MultipartFile file, String tableName, Table table) throws IOException {
        if (file == null || file.isEmpty()) {
            throw new NDSException("未选择导入文件！");
        }

        if (table == null) {
            throw new NDSException(String.format("表%s不存在", tableName));
        }

        try {
            InputStream stream = file.getInputStream();
            HSSFWorkbook workbook = new HSSFWorkbook(stream);
            if (workbook.getNumberOfSheets() <= 0) {
                throw new NDSException("导入的文件没有数据！");
            }
            return workbook;
        } catch (NDSException e) {
            throw e;
        } catch (Exception e) {
            throw new NDSException("只支持XLS格式！");
        }
    }

    /*
     * 取属性信息
     * */
    private ValueHolder getAttriValue(Table table, QuerySession session) {
        ValueHolder valueHolder = new ValueHolder();
        ArrayList tempColumns = table.getAllColumns();
        List<HashMap> attriValueLists = new ArrayList();
        for (int i = 0; i < tempColumns.size(); i++) {
            Column col = (Column) tempColumns.get(i);
            if (col != null && col.isShowable(Column.QUERY_OBJECT)) {  //第7位为1的
                HashMap map = new HashMap();
                String description = col.getDescription(session.getLocale());
                if (col.getName() != null && description != null) {
                    map.put("name", col.getName());
                    map.put("description", description);
                    attriValueLists.add(map);
                }
            }
        }
        valueHolder.put("data", attriValueLists);
        valueHolder.put("code", 0);
        return valueHolder;
    }
}