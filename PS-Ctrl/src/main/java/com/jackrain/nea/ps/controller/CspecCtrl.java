package com.jackrain.nea.ps.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.ps.service.CspecDelCmdImpl;
import com.jackrain.nea.ps.service.CspecSaveCmdImpl;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySessionImpl;
import com.jackrain.nea.web.security.Security4Utils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @date 2017/10/16
 */
@Api(value = "Edas测试",description = "规格定义-董海华")
@RestController
@RequestMapping(value = "/api/cs/ps")
public class CspecCtrl {
    @Autowired
    private CspecSaveCmdImpl cspecSaveCmd;
    @Autowired
    private CspecDelCmdImpl cspecDelCmd;

     //插入和修改记录
    @ApiOperation(value = "规格定义的插入和修改的测试")
    @RequestMapping(path = "/api/spec/save",method = RequestMethod.GET)
    public JSONObject testSession(HttpServletRequest request,
                                  @RequestParam(value="param",required = true) String param) throws Exception {

        ValueHolder valueHolder = new ValueHolder();
        //暂时使用root账号
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test",request,false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = cspecSaveCmd.execute(querySession);

        return result.toJSONObject();

    }

    //删除记录
    @ApiOperation(value = "规格定义的删除操作的测试")
    @RequestMapping(path = "/api/spec/del",method = RequestMethod.GET)
    public JSONObject testSession3(HttpServletRequest request,
                                   @RequestParam(value="param",required = true) String param) throws Exception {

        ValueHolder valueHolder = new ValueHolder();
        //暂时使用root账号
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test",request,false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = cspecDelCmd.execute(querySession);

        return result.toJSONObject();

    }
}
