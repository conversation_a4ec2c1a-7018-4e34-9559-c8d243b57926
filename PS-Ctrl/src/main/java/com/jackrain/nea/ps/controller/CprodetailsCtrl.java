package com.jackrain.nea.ps.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.ps.service.CproVideoAuthCmdImpl;
import com.jackrain.nea.ps.service.CproVideoSimpleInfoCmdImpl;
import com.jackrain.nea.ps.service.CprodetailsQueryCmdImpl;
import com.jackrain.nea.ps.service.CpronormsQueryCmdImpl;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import com.jackrain.nea.web.query.QuerySessionImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;

/**
 * <AUTHOR> zhang
 * @date 2018/03/01
 */
@Api(description = "商品中心-商品详情页 功能测试")
@RestController
public class CprodetailsCtrl {
    @Autowired
    private CprodetailsQueryCmdImpl cprodetailsQueryCmd;
    @Autowired
    private CpronormsQueryCmdImpl cpronormsQueryCmd;
    @Autowired
    private CproVideoAuthCmdImpl cproVideoAuthCmd;
    @Autowired
    private CproVideoSimpleInfoCmdImpl cproVideoSimpleInfoCmd;
//    @Autowired
//    private CprostockQueryCmd cprostockQueryCmd;

    @ApiOperation(value = "商品详情页查询 测试")
    @RequestMapping(path = "/api/cs/ps/CprodetailsQueryCmd", method = RequestMethod.POST)
    public JSONObject testSession(HttpServletRequest request,
                                  @RequestParam(value = "param", required = false) String param) throws Exception {
        ValueHolder valueHolder = new ValueHolder();
//        User user = (User) Security4Utils.getUser("root");
//        QuerySessionImpl querySession = new QuerySessionImpl(user);
//        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        QuerySession querySession = new QuerySessionImpl(request);
        DefaultWebEvent event = new DefaultWebEvent("query", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = cprodetailsQueryCmd.execute(querySession);
        return result.toJSONObject();
    }

    @ApiOperation(value = "视频授权")
    @RequestMapping(path = "/api/cs/ps/CproVideoAuthCmd",method = {RequestMethod.POST,RequestMethod.GET})
    public JSONObject videoAuth(@RequestParam(value = "videoId")String videoId){
        HashMap map = new HashMap();
        map.put("videoId",videoId);
        return cproVideoAuthCmd.execute(map).toJSONObject();
    }

    @ApiOperation(value = "视频信息")
    @RequestMapping(path = "/api/cs/ps/videoInfo",method = {RequestMethod.POST,RequestMethod.GET})
    public JSONObject videoSimpleInfo(@RequestParam(value = "videoId")String videoId){
        HashMap map = new HashMap();
        map.put("videoId",videoId);
        return cproVideoSimpleInfoCmd.execute(map).toJSONObject();
    }

    @ApiOperation(value = "商品规格查询 测试")
    @RequestMapping(path = "/api/cs/ps/CpronormsQueryCmd", method = RequestMethod.POST)
    public JSONObject testSession2(HttpServletRequest request,
                                   @RequestParam(value = "param", required = true) String param) throws Exception {
        ValueHolder valueHolder = new ValueHolder();
        //User user = (User) Security4Utils.getUser("root");
       //QuerySessionImpl querySession = new QuerySessionImpl(user);
//        User user = (UserImpl) request.getSession().getAttribute("user");
        QuerySession querySession = new QuerySessionImpl(request);
        DefaultWebEvent event = new DefaultWebEvent("normsquery", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = null;
        try {
            result = cpronormsQueryCmd.execute(querySession);
            return result.toJSONObject();
        }catch (Exception e){
            result = new ValueHolder();
            result.put("code", -1);
            result.put("message", e.getMessage());
            return result.toJSONObject();
        }
    }

//    @ApiOperation(value = "商品库存查询 测试")
//    @RequestMapping(path = "/api/cs/ps/CprostockQueryCmd", method = RequestMethod.POST)
//    public JSONObject testSession3(HttpServletRequest request,
//                                   @RequestParam(value = "param", required = true) String param) throws Exception {
//        ValueHolder valueHolder = new ValueHolder();
//        //User user = (User) Security4Utils.getUser("root");
//        //QuerySessionImpl querySession = new QuerySessionImpl(user);
//        QuerySession querySession = new QuerySessionImpl(request);
//        DefaultWebEvent event = new DefaultWebEvent("stockquery", request, false);
//        event.put("param", JSON.parseObject(param));
//        querySession.setEvent(event);
//        ValueHolder result = cprostockQueryCmd.execute(querySession);
//        return result.toJSONObject();
//    }
}
