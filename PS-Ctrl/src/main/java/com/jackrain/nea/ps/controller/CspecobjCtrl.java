package com.jackrain.nea.ps.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.ps.service.CclrQueryCmdImpl;
import com.jackrain.nea.ps.service.CspecobjLoadCmdImpl;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySessionImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@Api(description = "标准商品档案定义相关功能--颜色尺寸组定义")
@RestController
public class CspecobjCtrl {
    @Autowired
    private CspecobjLoadCmdImpl cspecobjLoadCmd;
    @Autowired
    private CclrQueryCmdImpl cclrQueryCmd;
    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @ApiOperation(value = "列出颜色 尺寸")
    @RequestMapping(path = "/api/cs/ps/cspecobjload", method = RequestMethod.GET)
    public JSONObject loadSpecObj(HttpServletRequest request,
                                   @RequestParam(value = "param", required = true) String param) throws Exception {
        User user =r3PrimWebAuthService.getLoginPrimWebUser(request);
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = cspecobjLoadCmd.execute(querySession);
        return result.toJSONObject();
    }

    @ApiOperation(value = "新增颜色服务")
    @RequestMapping(path = "/api/cs/ps/cclrquery", method = RequestMethod.GET)
    public JSONObject loadSpecObj(HttpServletRequest request,
                                  @RequestParam(value = "PS_C_PRO_ID", required = true) Long ps_c_pro_id,@RequestParam(value = "COLOR", required = true) String color,@RequestParam(value = "SELECTED", required = true) String selectd) throws Exception {
        User user =r3PrimWebAuthService.getLoginPrimWebUser(request);
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        JSONObject param=new JSONObject();
        param.put("PS_C_PRO_ID", ps_c_pro_id);
        param.put("COLOR",color);
        param.put("SELECTED",selectd);
        event.put("param",param);
        querySession.setEvent(event);
        ValueHolder result = cclrQueryCmd.execute(querySession);
        return result.toJSONObject();
    }
}
