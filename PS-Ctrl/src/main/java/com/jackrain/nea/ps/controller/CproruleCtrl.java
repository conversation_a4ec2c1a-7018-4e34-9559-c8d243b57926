package com.jackrain.nea.ps.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.ps.service.CproruleDelCmdImpl;
import com.jackrain.nea.ps.service.CproruleSaveCmdImpl;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySessionImpl;
import com.jackrain.nea.web.security.Security4Utils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@Api(value = "PS_C_PRORULE", description = "商品编码规则定义、商品编码规则明细定义")
@RestController
@RequestMapping(value = "/api/cs/ps")
public class CproruleCtrl {
    @Autowired
    private CproruleSaveCmdImpl cproruleSaveCmd;
    @Autowired
    private CproruleDelCmdImpl cproruleDelCmd;

    @ApiOperation(value = "测试商品编码规则的更新和添加")
    @RequestMapping(path = "/api/cprorulecmdimpl/save", method = RequestMethod.GET)
    public JSONObject testSession(HttpServletRequest request,
                                  @RequestParam(value = "param", required = true) String param) throws Exception {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = cproruleSaveCmd.execute(querySession);
        return result.toJSONObject();
    }

    @ApiOperation(value = "测试商品编码规则的删除")
    @RequestMapping(path = "/api/cprorulecmdimpl/del", method = RequestMethod.GET)
    public JSONObject testDel(HttpServletRequest request,
                              @RequestParam(value = "param", required = true) String param) throws Exception {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = cproruleDelCmd.execute(querySession);
        return result.toJSONObject();
    }
}
