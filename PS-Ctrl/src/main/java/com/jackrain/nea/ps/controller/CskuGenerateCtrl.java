package com.jackrain.nea.ps.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.ps.service.CskuGenerateCmdImpl;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySessionImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@Api(description = "标准商品档案定义相关功能--颜色尺寸组定义")
@RestController
public class CskuGenerateCtrl {
    @Autowired
    private CskuGenerateCmdImpl cskuGenerateCmd;
    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @ApiOperation(value = "生成条码")
    @RequestMapping(path = "/api/cs/ps/cskugenerate",method = RequestMethod.GET)
    public JSONObject skuGenerate(HttpServletRequest request,
                               @RequestParam(value="param",required = true) String param) throws Exception {
        ValueHolder valueHolder = new ValueHolder();
        User user =r3PrimWebAuthService.getLoginPrimWebUser(request);
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test",request,false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = cskuGenerateCmd.execute(querySession);
        return result.toJSONObject();
    }
}
