package com.jackrain.nea.ps.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.ps.service.CdistribProPriceQueryCmdImpl;
import com.jackrain.nea.ps.service.CproPriceQueryCmdImpl;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySessionImpl;
import com.jackrain.nea.web.security.Security4Utils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;


/**
 * @Description:
 * @Author: Yangxy
 * @Date: 2018-04-25 10:13:19
 */
@Api(value = "CproPriceQueryCmd测试", description = "CproPriceQueryCmd")
@RestController
public class CproPriceCtrl {
    @Autowired
    private CproPriceQueryCmdImpl cproPriceQueryCmd;

    @Autowired
    private CdistribProPriceQueryCmdImpl cdistribProPriceQueryCmd;

    @ApiOperation(value = "CproPriceQueryCmd 测试")
    @RequestMapping(path = "/api/cs/ps/cproprice/query", method = RequestMethod.POST)
    public JSONObject testSession(HttpServletRequest request,
                                  @RequestParam(value = "param", required = true) String param) throws Exception {
        //暂时使用root账号
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = cproPriceQueryCmd.execute(querySession);
        return result.toJSONObject();

    }

    @ApiOperation(value = "CdistribuProPriceQueryCmd 测试")
    @RequestMapping(path = "/api/cs/ps/cdistribProprice/query", method = RequestMethod.POST)
    public JSONObject cdistribPropriceTest(HttpServletRequest request,
                                  @RequestParam(value = "eCode", required = true) String eCode,
                                  @RequestParam(value = "distribId", required = true) Long distribId) throws Exception {

        HashMap map = new HashMap();
        map.put("eCode", eCode);
        map.put("distribId", distribId);
        ValueHolder result = cdistribProPriceQueryCmd.execute(map);
        return result.toJSONObject();
    }
}