package com.jackrain.nea.ps.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.ps.service.CskuDelCmdImpl;
import com.jackrain.nea.ps.service.CskuQueryCmdImpl;
import com.jackrain.nea.ps.service.CskuQueryIssueCmdImpl;
import com.jackrain.nea.ps.service.CskuUpdateCmdImpl;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySessionImpl;
import com.jackrain.nea.web.security.Security4Utils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @create 2017/10/11
 */
@Api(description = "标准商品档案-条码维护")
@RestController
public class CskuCtrl {

    @Autowired
    private CskuUpdateCmdImpl cskuUpdateCmd;

    @Autowired
    private CskuDelCmdImpl cskuDelCmd;

    @Autowired
    private CskuQueryCmdImpl cskuQueryCmd;

    @Autowired
    private CskuQueryIssueCmdImpl cskuQueryIssueCmd;

    @ApiOperation(value = "条码维护更新")
    @RequestMapping(path = "/api/cs/ps/api/sku/update",method = RequestMethod.GET)
    public JSONObject update(HttpServletRequest request,
                                 @RequestParam(value="param",required = true) String param) throws Exception {

        //暂时使用root账号
        User user = Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test",request,false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = cskuUpdateCmd.execute(querySession);

        return result.toJSONObject();

    }

    @ApiOperation(value = "条码维护删除")
    @RequestMapping(path = "/api/cs/ps/api/sku/delete",method = RequestMethod.GET)
    public JSONObject delete(HttpServletRequest request,
                             @RequestParam(value="param",required = true) String param) throws Exception {

        //暂时使用root账号
        User user = Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test",request,false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = cskuDelCmd.execute(querySession);

        return result.toJSONObject();

    }

    @ApiOperation(value = "基础信息标准服务条码")
    @RequestMapping(path = "/api/cs/ps/api/sku/query",method = RequestMethod.GET)
    public JSONObject query(HttpServletRequest request,
                             @RequestParam(value="param",required = true) String param) throws Exception {

        //暂时使用root账号
        User user = Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test",request,false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = cskuQueryCmd.execute(querySession);

        return result.toJSONObject();

    }

    @ApiOperation(value = "单SKU商品下发查询")
    @RequestMapping(path = "/api/cs/ps/querySkuIssue",method = RequestMethod.GET)
    public JSONObject queryIssue(HttpServletRequest request,
                             @RequestParam(value="param", required = true) String skuId) throws Exception {
        HashMap hashMap = new HashMap();
        hashMap.put("SKUID", skuId);
        ValueHolder result = cskuQueryIssueCmd.execute(hashMap);
        return result.toJSONObject();

    }
}
