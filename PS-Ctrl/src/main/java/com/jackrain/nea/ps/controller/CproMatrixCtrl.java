package com.jackrain.nea.ps.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.ps.service.CproJudgeProCmdImpl;
import com.jackrain.nea.ps.service.CproMatrixCmdImpl;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySessionImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@Api(description = "配销中心商品档案--颜色尺寸组获取")
@RestController
public class CproMatrixCtrl {
    @Autowired
    private CproMatrixCmdImpl cproMatrixCmd;

    @Autowired
    private CproJudgeProCmdImpl cproJudgeProCmd;
    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @ApiOperation(value = "列出颜色 尺寸")
    @RequestMapping(path = "/api/cs/ps/matrix", method = RequestMethod.GET)
    public JSONObject proMatrix(HttpServletRequest request,
                                @RequestParam(value = "param", required = true) String param) throws Exception {
        User user =r3PrimWebAuthService.getLoginPrimWebUser(request);
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = cproMatrixCmd.execute(querySession);
        return result.toJSONObject();
    }

    @ApiOperation(value = "判断输入时商品编码还是SKU条码")
    @RequestMapping(path = "/api/cs/ps/judgePro", method = RequestMethod.GET)
    public JSONObject judgePro(HttpServletRequest request,
                               @RequestParam(value = "param", required = true) String param) throws Exception {
        User user =r3PrimWebAuthService.getLoginPrimWebUser(request);
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = cproJudgeProCmd.execute(querySession);
        return result.toJSONObject();
    }
    /**
     * 测试用
     *
     * @return
     */
    public User getUser() {
        UserImpl user = new UserImpl();
        user.setClientId(27);
        user.setOrgId(37);
        user.setId(873);
        user.setName("木小雅");
        user.setEname("木小雅plus");
        return user;
    }
}
