package com.jackrain.nea.ps.controller;
/**
 * create by tzp 2017/9/28
 */

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.ps.service.CgbruleSaveCmdImpl;
import com.jackrain.nea.ps.service.CgbruleSubmitCmdImpl;
import com.jackrain.nea.ps.service.CgbruleUnSubmitCmdImpl;
import com.jackrain.nea.ps.service.CgbruleVoidCmdImpl;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySessionImpl;
import com.jackrain.nea.web.security.Security4Utils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@Api(value = "PS_C_GBRULE",description = "国标码生成规则定义")
@RestController
@RequestMapping(value = "/api/cs/ps")
public class CgbruleCtrl {
    @Autowired
    private CgbruleSaveCmdImpl cgbruleSaveCmd;
    @Autowired
    private CgbruleSubmitCmdImpl cgbruleSubmitCmd;
    @Autowired
    private CgbruleUnSubmitCmdImpl cgbruleUnSubmitCmd;
    @Autowired
    private CgbruleVoidCmdImpl cgbruleVoidCmd;

    @ApiOperation(value = "测试国标码规则的更新和添加")
    @RequestMapping(path = "/api/cgbrulecmdimpl/save",method = RequestMethod.GET)
    public JSONObject testSave(HttpServletRequest request,
                                  @RequestParam(value="param",required = true) String param) throws Exception {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test",request,false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = cgbruleSaveCmd.execute(querySession);
        return result.toJSONObject();
    }

    @ApiOperation(value = "测试国标码规则的作废")
    @RequestMapping(path = "/api/cgbrulecmdimpl/void",method = RequestMethod.GET)
    public JSONObject testVoid(HttpServletRequest request,
                                  @RequestParam(value="param",required = true) String param) throws Exception {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test",request,false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = cgbruleVoidCmd.execute(querySession);
        return result.toJSONObject();
    }


    @ApiOperation(value = "测试国标码规则的提交")
    @RequestMapping(path = "/api/cgbrulecmdimpl/submit",method = RequestMethod.GET)
    public JSONObject testSumbit(HttpServletRequest request,
                               @RequestParam(value="param",required = true) String param) throws Exception {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test",request,false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = cgbruleSubmitCmd.execute(querySession);
        return result.toJSONObject();
    }

    @ApiOperation(value = "测试国标码规则的未提交")
    @RequestMapping(path = "/api/cgbrulecmdimpl/unsubmit",method = RequestMethod.GET)
    public JSONObject testUnSumbit(HttpServletRequest request,
                                 @RequestParam(value="param",required = true) String param) throws Exception {
        ValueHolder valueHolder = new ValueHolder();
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test",request,false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = cgbruleUnSubmitCmd.execute(querySession);
        return result.toJSONObject();
    }
}
