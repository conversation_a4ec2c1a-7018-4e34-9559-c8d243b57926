//package com.jackrain.nea.ps.controller;
//
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.jackrain.nea.common.ReferenceUtil;
//import com.jackrain.nea.sys.domain.ValueHolderV14;
//import com.jackrain.nea.util.ApplicationContextHandle;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestMethod;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.servlet.http.HttpServletRequest;
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * @author: 郑立轩
// * @since: 2019/4/8
// * create at : 2019/4/8 15:17
// */
//@Api(description = "测试组合商品查询sku")
//@RestController
//public class CskuGroupQueryCtrl {
//
//    @ApiOperation(value = "查询sku")
//    @RequestMapping(path = "/api/cs/ps/skuGroupQuery",method = RequestMethod.GET)
//    public JSONObject skuGroupQuery(HttpServletRequest request,
//                                  @RequestBody JSONArray param) throws Exception {
//        List<String> list = new ArrayList(param);
//        Object ps = ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(), "com.jackrain.nea.ps.api.CskuGroupQureyCmd", "ps", "1.0");
//        ValueHolderV14 result = (ValueHolderV14)((CskuGroupQureyCmd)ps).skuGroupQuery(list);
//        return result.toJSONObject();
//    }
//    @ApiOperation(value = "查询组合商品提交信息")
//    @RequestMapping(path = "/api/cs/ps/skuGroupSubmitQuery",method = RequestMethod.GET)
//    public JSONObject skuGroupSubmitQuery(HttpServletRequest request,@RequestBody JSONObject param) throws Exception {
//        Object ps = ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(), "com.jackrain.nea.ps.api.CskuGroupEcodeQueryCmd", "ps", "1.0");
//        Integer start = (Integer)param.get("start");
//        Integer count = (Integer)param.get("count");
//        Integer type = (Integer) param.get("type");
//        ValueHolderV14 result = (ValueHolderV14)((CskuGroupEcodeQueryCmd)ps).groupEcodeQuery(start,count,type);
//        return result.toJSONObject();
//    }
//}
