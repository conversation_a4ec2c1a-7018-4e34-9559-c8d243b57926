package com.jackrain.nea.ps.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.ps.service.CsizegroupAddCmdImp;
import com.jackrain.nea.ps.service.CsizegroupDelCmdImp;
import com.jackrain.nea.ps.service.CsizegroupUpdateCmdImp;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySessionImpl;
import com.jackrain.nea.web.security.Security4Utils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR> on 2017/9/29.
 *  尺寸组及尺寸controller
 */
@Api(description = "商品中心-尺寸组定义、尺寸定义")
@RestController
@RequestMapping(value = "/api/cs/ps")
public class CsizegroupCtrl{

    @Autowired
    private CsizegroupDelCmdImp csizegroupDelCmd;
    @Autowired
    private CsizegroupAddCmdImp csizegroupAddCmd;
    @Autowired
    private CsizegroupUpdateCmdImp csizegroupUpdateCmd;
    @ApiOperation(value = "新增尺寸组、尺寸")
    @RequestMapping(path = "/addsize",method = RequestMethod.GET)
    public JSONObject testAddspecgroupSession(HttpServletRequest request,
                                              @RequestParam(value="param",required = true) String param) throws Exception {

        ValueHolder valueHolder = new ValueHolder();
        //暂时使用root账号
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test",request,false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = csizegroupAddCmd.execute(querySession);

        return result.toJSONObject();

    }

    @ApiOperation(value = "更新尺寸组、尺寸")
    @RequestMapping(path = "/updatesize",method = RequestMethod.GET)
    public JSONObject testUpdatespecgroupSession(HttpServletRequest request,
                                                 @RequestParam(value="param",required = true) String param) throws Exception {

        ValueHolder valueHolder = new ValueHolder();
        //暂时使用root账号
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test",request,false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = csizegroupUpdateCmd.execute(querySession);

        return result.toJSONObject();

    }

    @ApiOperation(value = "删除尺寸组、尺寸")
    @RequestMapping(path = "/deletesize",method = RequestMethod.GET)
    public JSONObject testDeletespecgroupSession(HttpServletRequest request,
                                                 @RequestParam(value="param",required = true) String param) throws Exception {

        ValueHolder valueHolder = new ValueHolder();
        //暂时使用root账号
        User user = (User) Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test",request,false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = csizegroupDelCmd.execute(querySession);
        return result.toJSONObject();
    }


}
