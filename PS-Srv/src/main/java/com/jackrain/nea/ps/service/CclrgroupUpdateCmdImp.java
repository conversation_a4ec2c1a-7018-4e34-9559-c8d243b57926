package com.jackrain.nea.ps.service;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.core.db.Tools;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.CclrgroupUpdateCmd;
import com.jackrain.nea.ps.entity.CbrandEntity;
import com.jackrain.nea.ps.entity.CspecobjEntity;
import com.jackrain.nea.ps.mapper.CbrandMapper;
import com.jackrain.nea.ps.mapper.CclrgroupMapper;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * 颜色组及颜色更新service
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps")
public class CclrgroupUpdateCmdImp extends CommandAdapter implements CclrgroupUpdateCmd {

    @Autowired
    private CbrandMapper cbrandMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {

        CclrgroupMapper cclrgroupMapper = ApplicationContextHandle.getBean(CclrgroupMapper.class);
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        JSONObject superTable = fixColumn.getJSONObject("PS_C_CLRGROUP");
        long colSpecId = param.getLong("objid");
        int specIdCount = cclrgroupMapper.findSpaceDelId(colSpecId);
        if (specIdCount <= 0) {
            throw new NDSException(Resources.getMessage("当前记录已不存在！", querySession.getLocale()));
        }
        //主表不为空
        if (superTable != null) {

            String brandID = "PS_C_BRAND_ID";
            String ename = "ENAME";

            if (superTable.containsKey(ename)) {
                //规格ID＋名称唯一
                long specId = cclrgroupMapper.findSpeceByFlag(1);
                String specidStr = cclrgroupMapper.findCountidStr(specId, superTable.getString("ENAME"));
                if (specidStr != null && Integer.valueOf(specidStr) != colSpecId) {
                    throw new NDSException(Resources.getMessage("输入的数据已存在：名称", querySession.getLocale()));
                }
            }
            if (superTable.containsKey(brandID)) {

                long brandId = superTable.getIntValue(brandID);
                //品牌字段不在品牌表中
                int brandCount = cclrgroupMapper.findBrandCount(brandId);
                if (brandCount > 0) {
                    CbrandEntity cbrandEntity = cclrgroupMapper.findBrandByName(brandId);
                    String isactive = "N";
                    if (isactive.equals(cbrandEntity.getIsactive())) {
                        //输入的品牌已停用
                        throw new NDSException(Resources.getMessage("输入的品牌已停用", querySession.getLocale()));
                    }
                } else {
                    throw new NDSException(Resources.getMessage("输入的品牌不存在", querySession.getLocale()));
                }
                //查询规格表中的规格ID
                long specId = cclrgroupMapper.findSpeceByFlag(1);
                //规格id和品牌id唯一
                String specidres = cclrgroupMapper.findSpecid(specId, brandId);
                if (specidres != null && Integer.valueOf(specidres) != colSpecId) {
                    throw new NDSException(Resources.getMessage("输入的数据已存在：品牌", querySession.getLocale()));
                }

            }

            Timestamp timestamp = new Timestamp(System.currentTimeMillis());
            superTable.put("MODIFIERID", Integer.valueOf(querySession.getUser().getId()).longValue());
            superTable.put("MODIFIERNAME", querySession.getUser().getName());
            superTable.put("MODIFIERENAME", querySession.getUser().getEname());
            superTable.put("MODIFIEDDATE", timestamp);
            //更新操作
            superTable.put("ID", colSpecId);
            int updataRes = cclrgroupMapper.updateSpecgroup(superTable);
            if (updataRes < 1) {
                //更新失败
                throw new NDSException(Resources.getMessage("当前记录已不存在！", querySession.getLocale()));
            }
        }

        JSONArray itemIdArray = new JSONArray();
        JSONArray dataArrayJson = new JSONArray();
        JSONArray subTableArry = fixColumn.getJSONArray("PS_C_CLR");
        if (subTableArry != null && subTableArry.size() > 0) {
            for (int i = 0; i < subTableArry.size(); i++) {
                JSONObject subTable = subTableArry.getJSONObject(i);
                long detailID = subTable.getLongValue("ID");
                try {
                    if (detailID <= 0) {
                        if (subTable.getString("ECODE") == null) {
                            //颜色编码不能为空
                            throw new NDSException(Resources.getMessage("颜色编码不能为空", querySession.getLocale()) + Resources.getMessage("", querySession.getLocale()));
                        }
                        if (subTable.getString("ENAME") == null) {
                            // 颜色名称不能为空
                            throw new NDSException(Resources.getMessage("颜色名称不能为空", querySession.getLocale()) + Resources.getMessage("", querySession.getLocale()));
                        }
                        List<String> cloCount = cclrgroupMapper.findClrStr(colSpecId, subTable.getString("ECODE"));
                        if (CollectionUtils.isNotEmpty(cloCount)) {
                            //输入的数据已存在：颜色编码
                            throw new NDSException(Resources.getMessage("输入的数据已存在：颜色编码", querySession.getLocale()));
                        }
                        //查询规格表中的规格ID
                        Long specId = cclrgroupMapper.findSpeceByFlag(1);
                        CspecobjEntity cspecobjEntity = new CspecobjEntity();
                        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
                        String eCodeInsert = subTable.getString("ECODE");
                        String eNameInsert = subTable.getString("ENAME");

                        Long brandId = cclrgroupMapper.selectBrandIdById(colSpecId);
                        String brandName = this.cbrandMapper.selectEnameByBrandId(brandId);
                        cspecobjEntity.setBrandname(brandName + "-" + eCodeInsert + "-" + eNameInsert);
                        cspecobjEntity.setAd_client_id(Integer.valueOf(querySession.getUser().getClientId()).longValue());
                        cspecobjEntity.setAd_org_id(Integer.valueOf(querySession.getUser().getOrgId()).longValue());
                        cspecobjEntity.setPs_c_spec_id(specId);
                        cspecobjEntity.setPs_c_specgroup_id(colSpecId);
                        cspecobjEntity.setEcode(eCodeInsert);
                        cspecobjEntity.setEname(eNameInsert);
                        cspecobjEntity.setMixname("[" + eCodeInsert + "]" + eNameInsert);
                        cspecobjEntity.setRemark(subTable.getString("REMARK"));
                        cspecobjEntity.setOwnerid(Integer.valueOf(querySession.getUser().getId()).longValue());
                        cspecobjEntity.setModifierid(Integer.valueOf(querySession.getUser().getId()).longValue());
                        cspecobjEntity.setCreationdate(timestamp);
                        cspecobjEntity.setModifieddate(timestamp);
                        cspecobjEntity.setIsactive(subTable.getString("ISACTIVE"));
                        cspecobjEntity.setPs_c_clrseries_id(subTable.getLong("PS_C_CLRSERIES_ID"));

                        if (subTable.getString("ISACTIVE") == null || "".equals(subTable.getString("ISACTIVE"))) {
                            cspecobjEntity.setIsactive("Y");
                        }
                        cspecobjEntity.setOwnername(querySession.getUser().getName());
                        cspecobjEntity.setOwnerename(querySession.getUser().getEname());
                        cspecobjEntity.setModifiername(querySession.getUser().getName());
                        cspecobjEntity.setModifierename(querySession.getUser().getEname());

                        Long id = Tools.getSequence("PS_C_SPECOBJ");
                        cspecobjEntity.setId(id);
                        int insertRes = cclrgroupMapper.insertColor(cspecobjEntity);
                        if (insertRes > 0) {
                            itemIdArray.add(cspecobjEntity.getId());
                            //更新主表
                            JSONObject updateObj = new JSONObject();
                            updateObj.put("MODIFIEDDATE", timestamp);
                            updateObj.put("MODIFIERID", Integer.valueOf(querySession.getUser().getId()).longValue());
                            updateObj.put("ID", colSpecId);
                            updateObj.put("MODIFIERNAME", querySession.getUser().getName());
                            updateObj.put("MODIFIERENAME", querySession.getUser().getEname());
                            cclrgroupMapper.updateSpecgroup(updateObj);
                        } else {
                            //插入失败
                            throw new NDSException(Resources.getMessage("颜色表插入失败", querySession.getLocale()));
                        }

                    } else {//更新明细

                        int delColHave = cclrgroupMapper.findColifHave(detailID);
                        String ecode = cclrgroupMapper.findcolEcode(detailID);
                        String ename = cclrgroupMapper.findcolEname(detailID);

                        if (delColHave <= 0) {
                            throw new NDSException(Resources.getMessage("当前记录已不存在！", querySession.getLocale()));
                        }

                        if (subTable.containsKey("ECODE")) {

                            List<String> cloIdStr = cclrgroupMapper.findClrStr(colSpecId, subTable.getString("ECODE"));
                            if (CollectionUtils.isNotEmpty(cloIdStr)) {
                                //输入的数据已存在：颜色编码
                                throw new NDSException(Resources.getMessage("输入的数据已存在：颜色编码", querySession.getLocale()));
                            }
                            ecode = subTable.getString("ECODE");
                        }
                        if (subTable.containsKey("ENAME")) {

                            if (subTable.getString("ENAME") == null) {
                                // 颜色名称不能为空
                                throw new NDSException(Resources.getMessage("颜色名称不能为空", querySession.getLocale()));
                            }

                            ename = subTable.getString("ENAME");
                        }

                        Long brandId = cclrgroupMapper.selectBrandIdById(colSpecId);
                        String brandName = this.cbrandMapper.selectEnameByBrandId(brandId);
                        subTable.put("BRAND_NAME",brandName + "-" + ecode + "-" + ename);
                        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
                        subTable.put("MODIFIERID", Integer.valueOf(querySession.getUser().getId()).longValue());
                        subTable.put("MODIFIEDDATE", timestamp);
                        subTable.put("MODIFIERNAME", querySession.getUser().getName());
                        subTable.put("MODIFIERENAME", querySession.getUser().getEname());
                        subTable.put("MIXNAME", "[" + ecode + "]" + ename);
                        int updataClo = cclrgroupMapper.updateColgroup(subTable);
                        if (updataClo > 0) {
                            itemIdArray.add(subTable.getLong("ID"));
                            //更新成功
                            JSONObject updateObj = new JSONObject();
                            updateObj.put("MODIFIEDDATE", timestamp);
                            updateObj.put("MODIFIERID", Integer.valueOf(querySession.getUser().getId()).longValue());
                            updateObj.put("MODIFIERNAME", querySession.getUser().getName());
                            updateObj.put("MODIFIERENAME", querySession.getUser().getEname());
                            updateObj.put("ID", colSpecId);
                            cclrgroupMapper.updateSpecgroup(updateObj);

                        } else {
                            //更新失败
                            throw new NDSException(Resources.getMessage("当前记录已不存在！", querySession.getLocale()));
                        }
                    }
                } catch (NDSException e) {
                    // 将错误信息存入dataArrayJson中
                    dataArrayJson.add(getData(e, detailID));
                }
            }
        }
        JSONObject ret = new JSONObject();
        ret.put("objid", colSpecId);
        if (!itemIdArray.isEmpty()) {
            ret.put("PS_C_CLR", itemIdArray);
        }
        vh.put("ret", ret);
        if (dataArrayJson.size() > 0) {

            vh.put("data", dataArrayJson);
            vh.put("code", -1);
            vh.put("message", Resources.getMessage("失败", querySession.getLocale()));

        } else {
            vh.put("code", 0);
            vh.put("message", Resources.getMessage("success", querySession.getLocale()));
        }

        return vh;
    }

    /**
     * 获取明细表错误信息并插入dataJson
     *
     * @param e
     * @param itemId
     * @return
     */
    private JSONObject getData(NDSException e, Long itemId) {
        JSONObject dataJson = new JSONObject();
        dataJson.put("id", itemId);
        dataJson.put("code", -1);
        dataJson.put("message", e.getMessage());
        return dataJson;
    }
}