package com.jackrain.nea.ps.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.CproUpCmd;
import com.jackrain.nea.ps.mapper.CproMapper;
import com.jackrain.nea.ps.mapper.CproUpDownMapper;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * 商品上架
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps")
public class CproUpCmdImpl extends CommandAdapter implements CproUpCmd {

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder valueHolder = new ValueHolder();
        CproUpDownMapper cproUpDownMapper = ApplicationContextHandle.getBean(CproUpDownMapper.class);
        CproMapper cproMapper = ApplicationContextHandle.getBean(CproMapper.class);
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
        JSONArray pros = JSON.parseArray(String.valueOf(param.getString("ids")));
        log.debug(LogUtil.format("pros：") + pros);
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        Integer userId = querySession.getUser().getId();
        if (null != pros && pros.size() > 0) {
            JSONArray errorArray = new JSONArray();
            int success = 0;

            for (int i = 0; i < pros.size(); i++) {
                Long id = pros.getLong(i);
                String repeatPro = cproUpDownMapper.repeatPro(id);
                JSONObject errorJson = new JSONObject(true);
                errorJson.put("code", -1);
                if (null == repeatPro) {
                    errorJson.put("objid", id);
                    errorJson.put("message", Resources.getMessage("当前商品已不存在！", querySession.getLocale()));
                    errorArray.add(errorJson);
                } else {
                    JSONObject proJson = new JSONObject(true);
                    proJson.put("MODIFIEDDATE", timestamp);
                    proJson.put("MODIFIERID", Integer.valueOf(userId).longValue());
                    proJson.put("MODIFIERNAME", querySession.getUser().getName());
                    proJson.put("MODIFIERENAME", querySession.getUser().getEname());
                    proJson.put("DATEONSHELF", timestamp);
                    proJson.put("ID", id);
                    int result = cproUpDownMapper.up(proJson);
                    if (result == 0) {
                        errorJson.put("objid", id);
                        errorJson.put("message", Resources.getMessage("商品已上架展示，不允许重复操作！", querySession.getLocale()));
                        errorArray.add(errorJson);
                    } else {

                        success++;
                        proJson.put("ECODE", repeatPro);

                        cproUpDownMapper.upDis(proJson);
                    }
                }
            }

            if (errorArray.size() == 0) {
                valueHolder.put("code", 0);
                valueHolder.put("message", Resources.getMessage("上架展示成功的记录数：" + success + ",上架展示失败的记录数：" + errorArray.size(), querySession.getLocale()));
            } else {
                valueHolder.put("code", -1);
                valueHolder.put("message", Resources.getMessage("上架展示成功的记录数：" + success + ",上架展示失败的记录数：" + errorArray.size(), querySession.getLocale()));
                valueHolder.put("data", errorArray);
            }
        } else {
            valueHolder.put("code", -1);
            valueHolder.put("message", Resources.getMessage("请先选择需要上架展示的商品记录！", querySession.getLocale()));
        }
        return valueHolder;
    }
}
