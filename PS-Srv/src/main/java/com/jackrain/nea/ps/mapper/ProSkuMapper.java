package com.jackrain.nea.ps.mapper;

import com.alibaba.fastjson.JSONArray;
import com.jackrain.nea.ps.api.request.PsCSkuPrintInfoRequest;
import com.jackrain.nea.ps.api.result.ProAndSkuResult;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.ps.api.result.PsCSkuPrintInfoResult;
import com.jackrain.nea.ps.api.table.ExtPsCSku;
import com.jackrain.nea.ps.api.table.PsCPro;
import com.jackrain.nea.ps.api.table.PsCSku;
import com.jackrain.nea.web.utils.ArrayToSUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.List;
import java.util.Map;

/**
 * @author: pankai
 * @since: 2019-03-14
 * create at : 2019-03-14 13:46
 */
@Mapper
public interface ProSkuMapper {


    String field = "\ts.id, \n" +
            "\ts.CP_C_DISTRIB_ID AS cpCDistribId,\n" +
            "\ts.PS_C_PRO_ID AS psCProId,\n" +
            "\ts.QTY_LOWSTOCK AS qtyLowstock,\n" +
            "\ts.QTY_SAFENUM AS qtySafenum,\n" +
            "\ts.CP_C_SHOP_ID AS cpCShopId,\n" +
            "\tv.ECODE AS clrsEcode,\n" +
            "\tv.ENAME AS clrsEname,\n" +
            "\tu.ECODE AS sizesEcode,\n" +
            "\tu.ENAME AS sizesEname,\n" +
            "\ts.CP_C_SHOP_TITLE AS cpCShopTitle,\n" +
            "\ts.LENGTH AS length,\n" +
            "\ts.width,\n" +
            "\ts.height,\n" +
            "\ts.weight,\n" +
            "\ts.weight_unit as weightUnit,\n" +
            "\tp.M_DIM3_ID as mDim3Id,\n" +
            "\ts.CP_C_SHOP_TITLE AS cpCShopTitle,\n" +
            "\ts.length,\n" +
            "\ts.ecode as skuEcode,\n" +
            "\ts.forcode1,\n" +
            "\ts.forcode2,\n" +
            "\ts.forcode3,\n" +
            "\ts.gbcode,\n" +
            "\ts.forcode,\n" +
            "\ts.maincolor,\n" +
            "\ts.fabcolor,\n" +
            "\ts.stoppur,\n" +
            "\ts.sync,\n" +
            "\ts.dispalystandard,\n" +
            "\ts.preavgdaily,\n" +
            "\ts.pronature,\n" +
            "\ts.purchasemode,\n" +
            "\ts.remark,\n" +
            "\ts.stopreplen,\n" +
            "\ts.PS_C_SKU_ID as psCSkuId,\n" +
            "\ts.PS_C_SPEC1OBJ_ID as psCSpec1objId,\n" +
            "\ts.PS_C_SPEC2OBJ_ID as psCSpec2objId,\n" +
            "\ts.PS_C_SPEC3OBJ_ID as psCSpec3objId,\n" +
            "\ts.PS_C_SPEC4OBJ_ID as psCSpec4objId,\n" +
            "\ts.modifierename,\n" +
            "\ts.ownerename,\n" +
            "\tp.PS_C_BRAND_ID as psCBrandId,\n" +
            "\ts.discontid,\n" +
            "\ts.discontname,\n" +
            "\ts.discontename,\n" +
            "\ts.discontdate,\n" +
            "\ts.enableid,\n" +
            "\ts.enablename,\n" +
            "\ts.enableename,\n" +
            "\ts.enabledate,\n" +
            "\ts.GROUP_EXTRACT_NUM as groupExtractNum,\n" +
            "\ts.WARE_TYPE as wareType,\n" +
            "\tp.PS_C_PRORULE_ID as psCProruleId,\n" +
            "\tp.PS_C_SKURULE_ID as psCSkuruleId,\n" +
            "\tp.ECODE as psCProEcode,\n" +
            "\tp.ENAME as psCProEname,\n" +
            "\tp.PS_C_SHAPEGROUP_ID as psCShapegroupId,\n" +
            "\tp.status,\n" +
            "\tp.PS_C_SPEC1GROUP_ID as psCSpec1groupId,\n" +
            "\tp.PS_C_SPEC2GROUP_ID as psCSpec2groupId,\n" +
            "\tp.PS_C_SPEC3GROUP_ID as psCSpec3groupId,\n" +
            "\tp.PS_C_SPEC4GROUP_ID as psCSpec4groupId,\n" +
            "\tp.clrs,\n" +
            "\tp.sizes,\n" +
            "\tp.GROUP_TYPE as groupType,\n" +
            "\tp.GROUP_TYPE as skuType,\n" +
            "\tp.IS_GROUP as isGroup,\n" +
            "\tp.WARE_TYPE as psCProWareType,\n" +
            "\tp.IS_GIFT as isGift,\n" +
            "\tp.IS_AIRFORBIDDEN as isAirforbidden,\n" +
            "\tp.image,\n" +
            "\tp.PS_C_SPEC_IDS as psCSpecIds,\n" +
            "\tp.PS_C_SPECOBJ_IDS as psCSpecobjIds,\n" +
            "\tp.IMAGE_SKU as imageSku,\n" +
            "\tp.IS_VIRTUAL as isVirtual,\n" +
            "\tp.is_enable_expiry,\n" +
            "\tp.price_list as priceList,\n" +
            "\tp.base_price_down,\n" +
            "\tp.m_dim1_id,\n" +
            "\tp.m_dim2_id,\n" +
            "\tp.m_dim3_id,\n" +
            "\tp.m_dim4_id,\n" +
            "\tp.m_dim5_id,\n" +
            "\tp.m_dim6_id,\n" +
            "\tp.m_dim12_id,\n" +
            "\ts.ISACTIVE,\n" +
            "\tp.video";


    @SelectProvider(type = ProSkuQuery.class, method = "select")
    List<PsCSku> findIdByProId(String join);

    @Select("SELECT * FROM PS_C_SKU WHERE PS_C_PRO_ID=#{proId} and WARE_TYPE=#{type}")
    List<ExtPsCSku> selectSkuByProId(@Param("proId") Long proId, @Param("type") Integer type);

    @SelectProvider(type = ProSkuQuery.class, method = "selectPrintSkuInfo")
    List<PsCSkuPrintInfoResult> selectPrintSkuInfo(PsCSkuPrintInfoRequest request);

    @Select("SELECT * FROM PS_C_SKU WHERE PS_C_PRO_ECODE=#{proEcode} and WARE_TYPE=0")
    List<PsCSku> selectSkuByProEcode(String proEcode);

    @Select("SELECT\n" +
            "\tsku.*\n" +
            "FROM\n" +
            "\tps_c_sku AS sku\n" +
            "\tLEFT JOIN ps_c_specobj sp1 ON sku.ps_c_spec1obj_id = sp1.id\n" +
            "\tLEFT JOIN ps_c_specobj sp2 ON sku.ps_c_spec2obj_id = sp2.id\n" +
            "WHERE\n" +
            "\tps_c_pro_ecode =#{proEcode}\n" +
            "\tand PS_C_SPEC1OBJ_ID=#{colrId}\n" +
            "\tand PS_C_SPEC2OBJ_ID=#{sizeId}\n" +
            "\tAND sp1.ecode =#{colorCode}\n" +
            "\tand sp2.ecode=#{sizeCode}\n" +
            " and sku.ISACTIVE = 'Y'")
    PsCSku selectSkuByskureq(@Param("proEcode") String proEcode, @Param("colrId") Long colrId,
                             @Param("sizeId") Long sizeId, @Param("colorCode") String colorCode,
                             @Param("sizeCode") String sizeCode);

    @Select("SELECT\n" +
            "  v.* \n" +
            "FROM\n" +
            "  (\n" +
            "SELECT\n" +
            "  sku.id AS skuId,\n" +
            "  sku.ECODE AS skuEcode,\n" +
            "  pro.ID AS proId,\n" +
            "  pro.ECODE AS proEcode,\n" +
            "  pro.ENAME AS proEname,\n" +
            "  sp1.ID AS colorId,\n" +
            "  sp1.ECODE AS colorEcode,\n" +
            "  sp1.ENAME AS colorEname,\n" +
            "  sp2.ID AS sizeId,\n" +
            "  sp2.ECODE AS sizeEcode,\n" +
            "  sp2.ENAME AS sizeEname,\n" +
            "  pro.price_list AS pricelist,\n" +
            "  sku.GBCODE AS gbCode,\n" +
            "  concat_ws( '_', sku.PS_C_PRO_ECODE, sp1.ecode, sp2.ecode ) AS posKey \n" +
            "FROM\n" +
            "  ps_c_sku sku\n" +
            "  INNER JOIN ps_C_pro pro ON pro.ecode = sku.PS_C_PRO_ECODE\n" +
            "  INNER JOIN ps_C_specobj sp1 ON sku.PS_C_SPEC1OBJ_ID = sp1.ID\n" +
            "  INNER JOIN ps_c_specobj sp2 ON sku.PS_C_SPEC2OBJ_ID = sp2.ID \n" +
            "  ) v \n" +
            "WHERE\n" +
            "  v.posKey in (${posKeys})" +
            "  and v.proEcode in (${skus}) ")
    List<ProAndSkuResult> selectSkuProByskureq(@Param("posKeys") String posKeys, @Param("skus") String skus);


    @Select("<script>" +
            "select a.id PS_C_SKU_ID,b.* from ps_c_sku a left join ps_c_pro b " +
            "on a.ps_c_pro_id = b.id " +
            "where a.id in " +
            "<foreach collection='ids' item='item' open='(' separator=',' close=')'> #{item} </foreach> " +
            "and b.isactive = 'Y'" +
            "</script>")
    List<PsCPro> queryProInfoBySkuIdList(@Param("ids") List<Long> psCSkuIdList);


    @SelectProvider(type = ProSkuMapper.ProSkuQuery.class,
            method = "selectProSkuByEcodeWithOutActiveSql")
    List<PsCProSkuResult> selectProSkuByEcodeWithOutActiveSql(@Param("ecode") String ecode);


    @SelectProvider(type = ProSkuMapper.ProSkuQuery.class,
            method = "selectProSkuSql")
    List<PsCProSkuResult> proSkuQuery(@Param("skuIds") JSONArray skuIds);


    @SelectProvider(type = ProSkuMapper.ProSkuQuery.class,
            method = "selectProSkuByEcodeSql")
    List<PsCProSkuResult> proSkuQueryByEcode(@Param("ecodes") JSONArray ecodes);

    @SelectProvider(type = ProSkuMapper.ProSkuQuery.class,
            method = "selectProSkuByEcodeSqlIgnoreActive")
    List<PsCProSkuResult> proSkuQueryByEcodeIgnoreActive(@Param("ecodes") JSONArray ecodes);

    class ProSkuQuery {
        public String select(String join) {
            return "SELECT ID,PS_C_PRO_ECODE FROM PS_C_SKU WHERE PS_C_PRO_ID IN ( " + join + " ) ";
        }

        public String selectPrintSkuInfo(PsCSkuPrintInfoRequest request) {
            StringBuilder sql = new StringBuilder("SELECT " +
                    "s.PS_C_PRO_ECODE psCProEcode," +
                    "s.PS_C_PRO_ENAME psCProEname," +
                    "s.ECODE psCSkuEcode," +
                    "p.COMPONENT, " +
                    "a.ENAME psCSpec1Ename," +
                    "b.ENAME psCSpec2Ename," +
                    "p.PRODUCT_CATEGORY productCategory," +
                    "p.IMAGE," +
                    "c.ECODE as level FROM PS_C_SKU s ");
            sql.append("LEFT JOIN PS_C_SPECOBJ as a ON s.PS_C_SPEC1OBJ_ID = a.id ");
            sql.append("LEFT JOIN PS_C_SPECOBJ as b ON s.PS_C_SPEC2OBJ_ID = b.id ");
            sql.append("LEFT JOIN PS_C_PRO as p ON s.PS_C_PRO_ID = p.id  ");
            sql.append("LEFT JOIN PS_C_PRODIM_ITEM as c ON p.LEVEL = c.id WHERE ");
            if (CollectionUtils.isNotEmpty(request.getProIdList())) {
                List<Long> proIdList = request.getProIdList();
                sql.append("s.PS_C_PRO_ID in (");
                for (int i = 0, len = proIdList.size(); i < len; i++) {
                    sql.append(proIdList.get(i));
                    if (i != len - 1) {
                        sql.append(",");
                    }
                }
                sql.append(") AND ");
            }
            if (CollectionUtils.isNotEmpty(request.getSkuIdList())) {
                List<Long> skuIdList = request.getSkuIdList();
                sql.append("s.ID in (");
                for (int i = 0, len = skuIdList.size(); i < len; i++) {
                    sql.append(skuIdList.get(i));
                    if (i != len - 1) {
                        sql.append(",");
                    }
                }
                sql.append(") AND ");
            }
            if (CollectionUtils.isNotEmpty(request.getSizeIdList())) {
                List<Long> sizeIdList = request.getSizeIdList();
                sql.append("s.PS_C_SPEC2OBJ_ID in (");
                for (int i = 0, len = sizeIdList.size(); i < len; i++) {
                    sql.append(sizeIdList.get(i));
                    if (i != len - 1) {
                        sql.append(",");
                    }
                }
                sql.append(") AND ");
            }
            if (CollectionUtils.isNotEmpty(request.getClrIdList())) {
                List<Long> clrIdList = request.getClrIdList();
                sql.append("s.PS_C_SPEC1OBJ_ID in (");
                for (int i = 0, len = clrIdList.size(); i < len; i++) {
                    sql.append(clrIdList.get(i));
                    if (i != len - 1) {
                        sql.append(",");
                    }
                }
                sql.append(") AND ");
            }
            sql.append(" s.ISACTIVE = 'Y'");
            return sql.toString();
        }

        public String selectProSkuSql(Map<String, Object> para) {
            // 查询字段
            // 条码ID集合
            JSONArray ids = (JSONArray) para.get("skuIds");
            StringBuilder sql = new StringBuilder(" SELECT ");
            sql.append(field);
            sql.append(" FROM PS_C_PRO p INNER JOIN PS_C_SKU s ON s.PS_C_PRO_ID = p.ID ");
            sql.append(" LEFT JOIN PS_C_SPECOBJ as v ON s.PS_C_SPEC1OBJ_ID=v.id " +
                       " LEFT JOIN PS_C_SPECOBJ as u ON s.PS_C_SPEC2OBJ_ID=u.id ");
            sql.append(" WHERE s.ID IN (").append(ArrayToSUtil.join((ids).toArray(), ",")).append(")");
            sql.append(" AND p.ISACTIVE = 'Y' AND s.ISACTIVE = 'Y'");
            if (!sql.toString().toLowerCase().contains("where")) {
                return "";
            }
            return sql.toString();
        }

        public String selectProSkuByEcodeSql(Map<String, Object> para) {
            // 查询字段
            // 条码ID集合
            JSONArray ecodes = (JSONArray) para.get("ecodes");
            StringBuilder sql = new StringBuilder(" SELECT ");
            sql.append(field);
            sql.append(" FROM PS_C_PRO p INNER JOIN PS_C_SKU s ON s.PS_C_PRO_ID = p.ID ");
            sql.append(" LEFT JOIN PS_C_SPECOBJ as v ON s.PS_C_SPEC1OBJ_ID=v.id " +
                    "LEFT JOIN PS_C_SPECOBJ as u ON s.PS_C_SPEC2OBJ_ID=u.id ");
            sql.append(" WHERE (s.ECODE IN (").append(ArrayToSUtil.join((ecodes).toArray(), ",")).append(")");
            sql.append(" or s.FORCODE IN (").append(ArrayToSUtil.join((ecodes).toArray(), ",")).append("))");
            sql.append(" AND p.ISACTIVE = 'Y' AND s.ISACTIVE = 'Y'");
            if (!sql.toString().toLowerCase().contains("where")) {
                return "";
            }
            return sql.toString();
        }

        public String selectProSkuByEcodeSqlIgnoreActive(Map<String, Object> para) {
            // 查询字段
            // 条码ID集合
            JSONArray ecodes = (JSONArray) para.get("ecodes");
            StringBuilder sql = new StringBuilder(" SELECT ");
            sql.append(field);
            sql.append(" FROM PS_C_PRO p INNER JOIN PS_C_SKU s ON s.PS_C_PRO_ID = p.ID ");
            sql.append(" LEFT JOIN PS_C_SPECOBJ as v ON s.PS_C_SPEC1OBJ_ID=v.id " +
                    "LEFT JOIN PS_C_SPECOBJ as u ON s.PS_C_SPEC2OBJ_ID=u.id ");
            sql.append(" WHERE (s.ECODE IN (").append(ArrayToSUtil.join((ecodes).toArray(), ",")).append(")");
            sql.append(" or s.FORCODE IN (").append(ArrayToSUtil.join((ecodes).toArray(), ",")).append("))");
            if (!sql.toString().toLowerCase().contains("where")) {
                return "";
            }
            return sql.toString();
        }

        public String selectProSkuByEcodeWithOutActiveSql(String ecode) {
            // 查询字段
            StringBuilder sql = new StringBuilder(" SELECT p.id psCProId,p.`m_dim3_id` mDim3Id, p.ecode psCProEcode,p.ename  psCProEname, s.id id, s.ecode skuEcode");
            sql.append(" FROM PS_C_PRO p INNER JOIN PS_C_SKU s ON s.PS_C_PRO_ID = p.ID ");
            sql.append(" WHERE p.ECODE = #{ecode}");
            return sql.toString();
        }

        public String selectProSkuByProEcode(@Param("proEcode") String proEcodes) {
            // 查询字段
            StringBuilder sql = new StringBuilder(" SELECT ");
            sql.append(field);
            sql.append(" FROM PS_C_PRO p INNER JOIN PS_C_SKU s ON s.PS_C_PRO_ID = p.ID ");
//            sql.append(" LEFT JOIN PS_C_PRODIM_ITEM as a ON a.id=p.BASICUNIT");
            sql.append(" LEFT JOIN PS_C_SPECOBJ as v ON s.PS_C_SPEC1OBJ_ID=v.id " +
                    "LEFT JOIN PS_C_SPECOBJ as u ON s.PS_C_SPEC2OBJ_ID=u.id "
//                    "LEFT JOIN PS_C_PRODIM_ITEM as g ON p.sex=g.id " +
//                    "LEFT JOIN PS_C_PRODIM_ITEM as h ON p.BOOT_MOUTH=g.id " +
//                    "LEFT JOIN PS_C_PRODIM_ITEM as i ON p.FACTORY_COLOR=g.id " +
//                    "LEFT JOIN PS_C_PRODIM_ITEM as j ON p.CLOTHFABRIC=g.id"
            );
            sql.append(" WHERE p.ECODE = #{proEcode}");
            sql.append(" AND p.ISACTIVE = 'Y' AND s.ISACTIVE = 'Y'");
            return sql.toString();
        }


        public String selectProSkuByStoreCode(@Param("storeCode") String storeCode, @Param("shopId") Long shopId) {
            // 查询字段
            StringBuilder sql = new StringBuilder(" SELECT ");
            sql.append(field);
            sql.append(" FROM PS_C_PRO p INNER JOIN PS_C_SKU s ON s.PS_C_PRO_ID = p.ID ");
            sql.append(" LEFT JOIN PS_C_SPECOBJ as v ON s.PS_C_SPEC1OBJ_ID=v.id " +
                        "LEFT JOIN PS_C_SPECOBJ as u ON s.PS_C_SPEC2OBJ_ID=u.id " +
                        "LEFT JOIN PS_C_SKU_PLATFORM_CODE as g ON s.id=g.ps_c_sku_id "
            );
            sql.append(" WHERE g.trade_store_code = #{storeCode}");
            sql.append(" AND g.cp_c_shop_id = #{shopId}");
            sql.append(" AND g.ISACTIVE = 'Y'");
            sql.append(" AND p.ISACTIVE = 'Y' AND s.ISACTIVE = 'Y'");
            return sql.toString();
        }
    }

    @SelectProvider(type = ProSkuMapper.ProSkuQuery.class,
            method = "selectProSkuByProEcode")
    List<PsCProSkuResult> selectProSkuByProEcode(@Param("proEcode") String proEcode);


    @SelectProvider(type = ProSkuMapper.ProSkuQuery.class,
            method = "selectProSkuByStoreCode")
    List<PsCProSkuResult> selectProSkuByTradeStoreCode(@Param("storeCode") String storeCode, @Param("shopId") Long shopId);


    @Select("SELECT outerid FROM ip_c_jingdong_product_item WHERE wareid = #{wareId} order by creationdate desc")
    List<String> querySkuIdByWareId(@Param("wareId") String wareId);

    /**
     * 根据多个商品编码查询商品信息
     *
     * @param ecodes
     * @return
     */
    @Select("<script>" +
            "SELECT p.id psCProId,p.m_dim3_id mDim3Id,p.ecode psCProEcode,p.ename psCProEname,s.id id,s.ecode skuEcode " +
            "FROM PS_C_PRO p INNER JOIN PS_C_SKU s ON s.PS_C_PRO_ID = p.ID " +
            "WHERE p.ECODE in " +
            "<foreach collection='ecodes' item='item' open='(' separator=',' close=')'> #{item} </foreach> " +
            "</script>")
    List<PsCProSkuResult> selectProSkuByEcodesWithOutActive(@Param("ecodes") List<String> ecodes);

}
