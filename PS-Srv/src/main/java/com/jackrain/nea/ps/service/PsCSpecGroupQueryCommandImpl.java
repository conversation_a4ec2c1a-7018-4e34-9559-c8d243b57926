package com.jackrain.nea.ps.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.common.ValueHolderUtils;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.exception.NDSRuntimeException;
import com.jackrain.nea.ps.api.PsCSpecGroupQueryCommand;
import com.jackrain.nea.ps.mapper.PsCSpecgroupQueryMapper;
import com.jackrain.nea.ps.mapper.PsCSpecobjQueryMapper;
import com.jackrain.nea.psext.model.table.PsCSpecobj;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 尺寸组信息查询
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps")
public class PsCSpecGroupQueryCommandImpl extends CommandAdapter implements PsCSpecGroupQueryCommand {

    @Autowired
    private PsCSpecgroupQueryMapper psCSpecgroupMapper;

    @Autowired
    private PsCSpecobjQueryMapper psCSpecobjQueryMapper;

    @Override
    public ValueHolder queryPsCSpecGroupQueryId(Long id) throws NDSException {
        if (id == null || id <= 0L) {
            throw new NDSRuntimeException("不正确的ID");
        }

        return ValueHolderUtils.success("成功", psCSpecgroupMapper.selectById(id));
    }

    @Override
    public ValueHolder queryPsCSpecObjById(Long id) throws NDSException {
        if (id == null || id <= 0L) {
            throw new NDSRuntimeException("不正确的ID");
        }

        return ValueHolderUtils.success("成功", psCSpecobjQueryMapper.selectById(id));
    }

    @Override
    public ValueHolder queryPsCSpecObjByCondition(Long psCSpecGroupId, String ecode) throws NDSException {
        if (null == psCSpecGroupId || StringUtils.isBlank(ecode)) {
            return null;
        }
        List<PsCSpecobj> list = psCSpecobjQueryMapper.selectList(new QueryWrapper<PsCSpecobj>().lambda()
                .eq(PsCSpecobj::getPsCSpecgroupId, psCSpecGroupId)
                .eq(PsCSpecobj::getEcode, ecode)
                .eq(PsCSpecobj::getIsactive, "Y"));
        return ValueHolderUtils.success("成功", CollectionUtils.isEmpty(list) ? null : list.get(0));
    }
}