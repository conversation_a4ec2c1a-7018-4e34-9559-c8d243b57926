package com.jackrain.nea.ps.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.github.pagehelper.PageHelper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.CproQueryCmd;
import com.jackrain.nea.ps.api.common.PsConstantsIF;
import com.jackrain.nea.ps.api.request.ProRequest;
import com.jackrain.nea.ps.mapper.CproMapper;
import com.jackrain.nea.ps.mapper.CproQueryMapper;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps")
public class CproQueryCmdImpl extends CommandAdapter implements CproQueryCmd {

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        CproQueryMapper cproQueryMapper = ApplicationContextHandle.getBean(CproQueryMapper.class);
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
        StringBuffer sql = new StringBuffer();
        //判断是否有query这个jsonObject
        //拼接select后面需要查询的列名
        if (param.containsKey("query")) {
            JSONObject queryJo = param.getJSONObject("query");
            //判断是否有需要查询的fields字段
            if (queryJo.containsKey("fields") && queryJo.getJSONArray("fields") != null && queryJo.getJSONArray("fields").size() > 0) {
                JSONArray fieldsArray = queryJo.getJSONArray("fields");
                sql.append("SELECT ");
                int sum = 0;
                for (int i = 0; i < fieldsArray.size(); i++) {
                    if (fieldsArray.getString(i).contains("PS_C_PRO")) {
                        sql.append(fieldsArray.getString(i)).append(",");
                    } else {
                        //拼接fields有值时的情况，别名时"."换成"_"
                        sql.append(fieldsArray.getString(i)).append(" AS ").append(fieldsArray.getString(i).replace(".", "_")).append(",");
                    }
                    if (sum == fieldsArray.size() - 1) {
                        if (fieldsArray.getString(i).contains("PS_C_PRO")) {
                            sql.append(fieldsArray.getString(i));
                        } else {
                            sql.append(fieldsArray.getString(i)).append(" AS ").append(fieldsArray.getString(i).replace(".", "_"));
                        }
                    }
                    sum++;
                }
            } else {
                //没有fields这个键或者fields等于null或者size小于0,为查询所有服务
                sql.append("SELECT PS_C_PRO.ID,PS_C_PRO.CP_C_DISTRIB_ID,PS_C_PRO.ECODE,PS_C_PRO.ENAME,PS_C_PRO.PS_C_BRAND_ID,PS_C_PRO.DATEMARKET,PS_C_PRO.CP_C_SUPPLIER_ID,PS_C_PRO.FACTORYCODE,PS_C_PRO.GBCODE,PS_C_PRO.FABDESC,PS_C_PRO.TAGSPEC,PS_C_PRO.REMARK,PS_C_PRO.STATUS,PS_C_PRO.ISACTIVE,PS_C_PRO.PROYEAR,PS_C_PRO.LARGECLASS,PS_C_PRO.SEX,PS_C_PRO.SUPBRAND,PS_C_PRO.PRONATURE,PS_C_PRO.PROSEA,PS_C_PRO.PROBAND,")
                        .append("PS_C_PRO.PROMONTH,PS_C_PRO.PROSOURCE,PS_C_PRO.PROMOTIONTYPE,PS_C_PRO.PROLINE,PS_C_PRO.POPULAR,PS_C_PRO.COMPOSITION,PS_C_PRO.STYLE,PS_C_PRO.DETAILS,PS_C_PRO.PRICEBAND,PS_C_PRO.SERIES,PS_C_PRO.PURNATURE,PS_C_PRO.BUYER,PS_C_PRO.FOLLOWER,PS_C_PRO.NEWPROER,PS_C_PRO.MDLARGECLASS,PS_C_PRO.MDMIDDLECLASS,PS_C_PRO.FABRIC,PS_C_PRO.SAFETECHCLASS,PS_C_PRO.PROSTANDARD,PS_C_PRO.PROSTANDARD1,")
                        .append("PS_C_PRO.PROSTANDARD2,PS_C_PRO.BUYPATTERNER,PS_C_PRO.DESIGNER,PS_C_PRO.THEMESTORY,PS_C_PRO.DISCENTER,PS_C_PRO.PRICECOSTLIST,PS_C_PRO.PRICELIST,PS_C_PRO.PRICESETTLE,PS_C_PRO.PRICELOWER,PS_C_PRO.TRIALSTORENUM,PS_C_PRO.DISSTORENUM,PS_C_PRO.REDISSTORENUM,PS_C_PRO.PURCHASEMODE,PS_C_PRO.TRIALDAYS,PS_C_PRO.RECYCLE,PS_C_PRO.DATEOFFSHELF,PS_C_PRO.SALEPERIOD,PS_C_PRO.RELIABILITY,")
                        .append("PS_C_PRO.CAPACITY,PS_C_PRO.MINLOTSIZE,PS_C_PRO.UNIT,PS_C_PRO.DATEENDRECYCLE,PS_C_PRO.DATEENDRETURN,PS_C_PRO.SUPREMARK,PS_C_PRO.NUMDIM1,PS_C_PRO.NUMDIM2,PS_C_PRO.NUMDIM3,PS_C_PRO.NUMDIM4,PS_C_PRO.NUMDIM5,PS_C_PRO.NUMDIM6,PS_C_PRO.NUMDIM7,PS_C_PRO.NUMDIM8,PS_C_PRO.NUMDIM9,PS_C_PRO.NUMDIM10,PS_C_PRO.NUMDIM11,PS_C_PRO.NUMDIM12,PS_C_PRO.NUMDIM13,PS_C_PRO.NUMDIM14,PS_C_PRO.NUMDIM15,PS_C_PRO.NUMDIM16,PS_C_PRO.NUMDIM17,PS_C_PRO.NUMDIM18,PS_C_PRO.NUMDIM19,PS_C_PRO.NUMDIM20,")
                        .append("PS_C_PRO.DECDIM1,PS_C_PRO.DECDIM2,PS_C_PRO.DECDIM3,PS_C_PRO.DECDIM4,PS_C_PRO.DECDIM5,")
                        .append("PS_C_PRO.TXTDIM1,PS_C_PRO.TXTDIM2,PS_C_PRO.TXTDIM3,PS_C_PRO.TXTDIM4,PS_C_PRO.TXTDIM5,PS_C_PRO.TXTDIM6,PS_C_PRO.TXTDIM7,PS_C_PRO.TXTDIM8,PS_C_PRO.TXTDIM9,PS_C_PRO.TXTDIM10,")
                        .append("PS_C_PRO.DATEDIM1,PS_C_PRO.DATEDIM2,PS_C_PRO.DATEDIM3,PS_C_PRO.DATEDIM4,PS_C_PRO.DATEDIM5,PS_C_PRO.DATETIMEDIM1,PS_C_PRO.DATETIMEDIM2,PS_C_PRO.DATETIMEDIM3,PS_C_PRO.DATETIMEDIM4,PS_C_PRO.DATETIMEDIM5,")
                        .append("PS_C_PRO.PS_C_SPEC_IDS,PS_C_PRO.OWNERID,PS_C_PRO.CREATIONDATE,PS_C_PRO.MODIFIERID,PS_C_PRO.MODIFIEDDATE,PS_C_PRO.STATUSERID,PS_C_PRO.STATUSTIME,")
                        .append("CP_C_STOREORG.ECODE AS CP_C_DISTRIB_ECODE,CP_C_STOREORG.ENAME AS CP_C_DISTRIB_ENAME,")
                        .append("PS_C_BRAND.ECODE AS PS_C_BRAND_ECODE,PS_C_BRAND.ENAME AS PS_C_BRAND_ENAME,CP_C_SUPPLIER.ECODE AS CP_C_SUPPLIER_ECODE,CP_C_SUPPLIER.ENAME AS CP_C_SUPPLIER_ENAME,")
                        .append("PROYEAR.ECODE AS PROYEAR_ECODE,PROYEAR.ENAME AS PROYEAR_ENAME,")
                        .append("LARGECLASS.ECODE AS LARGECLASS_ECODE,LARGECLASS.ENAME AS LARGECLASS_ENAME,")
                        .append("SEX.ECODE AS SEX_ECODE,SEX.ENAME AS SEX_NAME,")
                        .append("SUPBRAND.ECODE AS SUPBRAND_ECODE,SUPBRAND.ENAME AS SUPBRAND_ENAME,")
                        .append("PRONATURE.ECODE AS PRONATURE_ECODE,PRONATURE.ENAME AS PRONATURE_ENAME,")
                        .append("PROSEA.ECODE AS PROSEA_ECODE,PROSEA.ENAME AS PROSEA_ENAME,")
                        .append("PROBAND.ECODE AS PROBAND_ECODE,PROBAND.ENAME AS PROBAND_ENAME,")
                        .append("PROMONTH.ECODE AS PROMONTH_ECODE,PROMONTH.ENAME AS PROMONTH_ENAME,")
                        .append("PROSOURCE.ECODE AS PROSOURCE_ECODE,PROSOURCE.ENAME AS PROSOURCE_ENAME,")
                        .append("PROMOTIONTYPE.ECODE AS PROMOTIONTYPE_ECODE,PROMOTIONTYPE.ENAME AS PROMOTIONTYPE_ENAME,")
                        .append("PROLINE.ECODE AS PROLINE_ECODE,PROLINE.ENAME AS PROLINE_ENAME,")
                        .append("POPULAR.ECODE AS POPULAR_ECODE,POPULAR.ENAME AS POPULAR_ENAME,")
                        .append("COMPOSITION.ECODE AS COMPOSITION_ECODE,COMPOSITION.ENAME AS COMPOSITION_ENAME,")
                        .append("STYLE.ECODE AS STYLE_ECODE,STYLE.ENAME AS STYLE_ENAME,")
                        .append("DETAILS.ECODE AS DETAILS_ECODE,DETAILS.ENAME AS DETAILS_ENAME,")
                        .append("PRICEBAND.ECODE AS PRICEBAND_ECODE,PRICEBAND.ENAME AS PRICEBAND_ENAME,")
                        .append("SERIES.ECODE AS SERIES_ECODE,SERIES.ENAME AS SERIES_ENAME,")
                        .append("PURNATURE.ECODE AS PURNATURE_ECODE,PURNATURE.ENAME AS PURNATURE_ENAME,")
                        .append("BUYER.ECODE AS BUYER_ECODE,BUYER.ENAME AS BUYER_ENAME,")
                        .append("FOLLOWER.ECODE AS FOLLOWER_ECODE,FOLLOWER.ENAME AS FOLLOWER_ENAME,")
                        .append("NEWPROER.ECODE AS NEWPROER_ECODE,NEWPROER.ENAME AS NEWPROER_ENAME,")
                        .append("MDLARGECLASS.ECODE AS MDLARGECLASS_ECODE,MDLARGECLASS.ENAME AS MDLARGECLASS_ENAME,")
                        .append("MDMIDDLECLASS.ECODE AS MDMIDDLECLASS_ECODE,MDMIDDLECLASS.ENAME AS MDMIDDLECLASS_ENAME,")
                        .append("FABRIC.ECODE AS FABRIC_ECODE,FABRIC.ENAME AS FABRIC_ENAME,")
                        .append("SAFETECHCLASS.ECODE AS SAFETECHCLASS_ECODE,SAFETECHCLASS.ENAME AS SAFETECHCLASS_ENAME,")
                        .append("PROSTANDARD.ECODE AS PROSTANDARD_ECODE,PROSTANDARD.ENAME AS PROSTANDARD_ENAME,")
                        .append("PROSTANDARD1.ECODE AS PROSTANDARD1_ECODE,PROSTANDARD1.ENAME AS PROSTANDARD1_ENAME,")
                        .append("PROSTANDARD2.ECODE AS PROSTANDARD2_ECODE,PROSTANDARD2.ENAME AS PROSTANDARD2_ENAME,")
                        .append("BUYPATTERNER.ECODE AS BUYPATTERNER_ECODE,BUYPATTERNER.ENAME AS BUYPATTERNER_ENAME,")
                        .append("DESIGNER.ECODE AS DESIGNER_ECODE,DESIGNER.ENAME AS DESIGNER_ENAME,")
                        .append("THEMESTORY.ECODE AS THEMESTORY_ECODE,THEMESTORY.ENAME AS THEMESTORY_ENAME,")
                        .append("CP_C_STORE.ECODE AS CP_C_WAREHOUSE_ECODE,CP_C_STORE.ENAME AS CP_C_WAREHOUSE_ENAME,")
                        .append("PURCHASEMODE.ECODE AS PURCHASEMODE_ECODE,PURCHASEMODE.ENAME AS PURCHASEMODE_ENAME,")
                        .append("UNIT.ECODE AS UNIT_ECODE,UNIT.ENAME AS UNIT_ENAME,")
                        .append("PS_C_PRO.PS_C_SPEC1GROUP_ID,PS_C_SPEC1GROUP.ENAME AS PS_C_SPEC1GROUP_ENAME,")
                        .append("PS_C_PRO.PS_C_SPEC2GROUP_ID,PS_C_SPEC2GROUP.ENAME AS PS_C_SPEC2GROUP_ENAME");
            }
        } else {
            //没有query这个键查询所有
            sql.append("SELECT PS_C_PRO.ID,PS_C_PRO.CP_C_DISTRIB_ID,PS_C_PRO.ECODE,PS_C_PRO.ENAME,PS_C_PRO.PS_C_BRAND_ID,PS_C_PRO.DATEMARKET,PS_C_PRO.CP_C_SUPPLIER_ID,PS_C_PRO.FACTORYCODE,PS_C_PRO.GBCODE,PS_C_PRO.FABDESC,PS_C_PRO.TAGSPEC,PS_C_PRO.REMARK,PS_C_PRO.STATUS,PS_C_PRO.ISACTIVE,PS_C_PRO.PROYEAR,PS_C_PRO.LARGECLASS,PS_C_PRO.SEX,PS_C_PRO.SUPBRAND,PS_C_PRO.PRONATURE,PS_C_PRO.PROSEA,PS_C_PRO.PROBAND,")
                    .append("PS_C_PRO.PROMONTH,PS_C_PRO.PROSOURCE,PS_C_PRO.PROMOTIONTYPE,PS_C_PRO.PROLINE,PS_C_PRO.POPULAR,PS_C_PRO.COMPOSITION,PS_C_PRO.STYLE,PS_C_PRO.DETAILS,PS_C_PRO.PRICEBAND,PS_C_PRO.SERIES,PS_C_PRO.PURNATURE,PS_C_PRO.BUYER,PS_C_PRO.FOLLOWER,PS_C_PRO.NEWPROER,PS_C_PRO.MDLARGECLASS,PS_C_PRO.MDMIDDLECLASS,PS_C_PRO.FABRIC,PS_C_PRO.SAFETECHCLASS,PS_C_PRO.PROSTANDARD,PS_C_PRO.PROSTANDARD1,")
                    .append("PS_C_PRO.PROSTANDARD2,PS_C_PRO.BUYPATTERNER,PS_C_PRO.DESIGNER,PS_C_PRO.THEMESTORY,PS_C_PRO.DISCENTER,PS_C_PRO.PRICECOSTLIST,PS_C_PRO.PRICELIST,PS_C_PRO.PRICESETTLE,PS_C_PRO.PRICELOWER,PS_C_PRO.TRIALSTORENUM,PS_C_PRO.DISSTORENUM,PS_C_PRO.REDISSTORENUM,PS_C_PRO.PURCHASEMODE,PS_C_PRO.TRIALDAYS,PS_C_PRO.RECYCLE,PS_C_PRO.DATEOFFSHELF,PS_C_PRO.SALEPERIOD,PS_C_PRO.RELIABILITY,")
                    .append("PS_C_PRO.CAPACITY,PS_C_PRO.MINLOTSIZE,PS_C_PRO.UNIT,PS_C_PRO.DATEENDRECYCLE,PS_C_PRO.DATEENDRETURN,PS_C_PRO.SUPREMARK,PS_C_PRO.NUMDIM1,PS_C_PRO.NUMDIM2,PS_C_PRO.NUMDIM3,PS_C_PRO.NUMDIM4,PS_C_PRO.NUMDIM5,PS_C_PRO.NUMDIM6,PS_C_PRO.NUMDIM7,PS_C_PRO.NUMDIM8,PS_C_PRO.NUMDIM9,PS_C_PRO.NUMDIM10,PS_C_PRO.NUMDIM11,PS_C_PRO.NUMDIM12,PS_C_PRO.NUMDIM13,PS_C_PRO.NUMDIM14,PS_C_PRO.NUMDIM15,PS_C_PRO.NUMDIM16,PS_C_PRO.NUMDIM17,PS_C_PRO.NUMDIM18,PS_C_PRO.NUMDIM19,PS_C_PRO.NUMDIM20,")
                    .append("PS_C_PRO.DECDIM1,PS_C_PRO.DECDIM2,PS_C_PRO.DECDIM3,PS_C_PRO.DECDIM4,PS_C_PRO.DECDIM5,")
                    .append("PS_C_PRO.TXTDIM1,PS_C_PRO.TXTDIM2,PS_C_PRO.TXTDIM3,PS_C_PRO.TXTDIM4,PS_C_PRO.TXTDIM5,PS_C_PRO.TXTDIM6,PS_C_PRO.TXTDIM7,PS_C_PRO.TXTDIM8,PS_C_PRO.TXTDIM9,PS_C_PRO.TXTDIM10,")
                    .append("PS_C_PRO.DATEDIM1,PS_C_PRO.DATEDIM2,PS_C_PRO.DATEDIM3,PS_C_PRO.DATEDIM4,PS_C_PRO.DATEDIM5,PS_C_PRO.DATETIMEDIM1,PS_C_PRO.DATETIMEDIM2,PS_C_PRO.DATETIMEDIM3,PS_C_PRO.DATETIMEDIM4,PS_C_PRO.DATETIMEDIM5,")
                    .append("PS_C_PRO.PS_C_SPEC_IDS,PS_C_PRO.OWNERID,PS_C_PRO.CREATIONDATE,PS_C_PRO.MODIFIERID,PS_C_PRO.MODIFIEDDATE,PS_C_PRO.STATUSERID,PS_C_PRO.STATUSTIME,")
                    .append("CP_C_STOREORG.ECODE AS CP_C_DISTRIB_ECODE,CP_C_STOREORG.ENAME AS CP_C_DISTRIB_ENAME,")
                    .append("PS_C_BRAND.ECODE AS PS_C_BRAND_ECODE,PS_C_BRAND.ENAME AS PS_C_BRAND_ENAME,CP_C_SUPPLIER.ECODE AS CP_C_SUPPLIER_ECODE,CP_C_SUPPLIER.ENAME AS CP_C_SUPPLIER_ENAME,")
                    .append("PROYEAR.ECODE AS PROYEAR_ECODE,PROYEAR.ENAME AS PROYEAR_ENAME,")
                    .append("LARGECLASS.ECODE AS LARGECLASS_ECODE,LARGECLASS.ENAME AS LARGECLASS_ENAME,")
                    .append("SEX.ECODE AS SEX_ECODE,SEX.ENAME AS SEX_NAME,")
                    .append("SUPBRAND.ECODE AS SUPBRAND_ECODE,SUPBRAND.ENAME AS SUPBRAND_ENAME,")
                    .append("PRONATURE.ECODE AS PRONATURE_ECODE,PRONATURE.ENAME AS PRONATURE_ENAME,")
                    .append("PROSEA.ECODE AS PROSEA_ECODE,PROSEA.ENAME AS PROSEA_ENAME,")
                    .append("PROBAND.ECODE AS PROBAND_ECODE,PROBAND.ENAME AS PROBAND_ENAME,")
                    .append("PROMONTH.ECODE AS PROMONTH_ECODE,PROMONTH.ENAME AS PROMONTH_ENAME,")
                    .append("PROSOURCE.ECODE AS PROSOURCE_ECODE,PROSOURCE.ENAME AS PROSOURCE_ENAME,")
                    .append("PROMOTIONTYPE.ECODE AS PROMOTIONTYPE_ECODE,PROMOTIONTYPE.ENAME AS PROMOTIONTYPE_ENAME,")
                    .append("PROLINE.ECODE AS PROLINE_ECODE,PROLINE.ENAME AS PROLINE_ENAME,")
                    .append("POPULAR.ECODE AS POPULAR_ECODE,POPULAR.ENAME AS POPULAR_ENAME,")
                    .append("COMPOSITION.ECODE AS COMPOSITION_ECODE,COMPOSITION.ENAME AS COMPOSITION_ENAME,")
                    .append("STYLE.ECODE AS STYLE_ECODE,STYLE.ENAME AS STYLE_ENAME,")
                    .append("DETAILS.ECODE AS DETAILS_ECODE,DETAILS.ENAME AS DETAILS_ENAME,")
                    .append("PRICEBAND.ECODE AS PRICEBAND_ECODE,PRICEBAND.ENAME AS PRICEBAND_ENAME,")
                    .append("SERIES.ECODE AS SERIES_ECODE,SERIES.ENAME AS SERIES_ENAME,")
                    .append("PURNATURE.ECODE AS PURNATURE_ECODE,PURNATURE.ENAME AS PURNATURE_ENAME,")
                    .append("BUYER.ECODE AS BUYER_ECODE,BUYER.ENAME AS BUYER_ENAME,")
                    .append("FOLLOWER.ECODE AS FOLLOWER_ECODE,FOLLOWER.ENAME AS FOLLOWER_ENAME,")
                    .append("NEWPROER.ECODE AS NEWPROER_ECODE,NEWPROER.ENAME AS NEWPROER_ENAME,")
                    .append("MDLARGECLASS.ECODE AS MDLARGECLASS_ECODE,MDLARGECLASS.ENAME AS MDLARGECLASS_ENAME,")
                    .append("MDMIDDLECLASS.ECODE AS MDMIDDLECLASS_ECODE,MDMIDDLECLASS.ENAME AS MDMIDDLECLASS_ENAME,")
                    .append("FABRIC.ECODE AS FABRIC_ECODE,FABRIC.ENAME AS FABRIC_ENAME,")
                    .append("SAFETECHCLASS.ECODE AS SAFETECHCLASS_ECODE,SAFETECHCLASS.ENAME AS SAFETECHCLASS_ENAME,")
                    .append("PROSTANDARD.ECODE AS PROSTANDARD_ECODE,PROSTANDARD.ENAME AS PROSTANDARD_ENAME,")
                    .append("PROSTANDARD1.ECODE AS PROSTANDARD1_ECODE,PROSTANDARD1.ENAME AS PROSTANDARD1_ENAME,")
                    .append("PROSTANDARD2.ECODE AS PROSTANDARD2_ECODE,PROSTANDARD2.ENAME AS PROSTANDARD2_ENAME,")
                    .append("BUYPATTERNER.ECODE AS BUYPATTERNER_ECODE,BUYPATTERNER.ENAME AS BUYPATTERNER_ENAME,")
                    .append("DESIGNER.ECODE AS DESIGNER_ECODE,DESIGNER.ENAME AS DESIGNER_ENAME,")
                    .append("THEMESTORY.ECODE AS THEMESTORY_ECODE,THEMESTORY.ENAME AS THEMESTORY_ENAME,")
                    .append("CP_C_STORE.ECODE AS CP_C_WAREHOUSE_ECODE,CP_C_STORE.ENAME AS CP_C_WAREHOUSE_ENAME,")
                    .append("PURCHASEMODE.ECODE AS PURCHASEMODE_ECODE,PURCHASEMODE.ENAME AS PURCHASEMODE_ENAME,")
                    .append("UNIT.ECODE AS UNIT_ECODE,UNIT.ENAME AS UNIT_ENAME,")
                    .append("PS_C_PRO.PS_C_SPEC1GROUP_ID,PS_C_SPEC1GROUP.ENAME AS PS_C_SPEC1GROUP_ENAME,")
                    .append("PS_C_PRO.PS_C_SPEC2GROUP_ID,PS_C_SPEC2GROUP.ENAME AS PS_C_SPEC2GROUP_ENAME");
        }

        //拼接left join语句
        sql.append(" FROM PS_C_PRO LEFT JOIN CP_C_STOREORG ON PS_C_PRO.CP_C_DISTRIB_ID=CP_C_STOREORG.ID")
                .append(" LEFT JOIN PS_C_BRAND ON PS_C_PRO.PS_C_BRAND_ID=PS_C_BRAND.ID")
                .append(" LEFT JOIN CP_C_SUPPLIER ON PS_C_PRO.CP_C_SUPPLIER_ID=CP_C_SUPPLIER.ID")
                .append(" LEFT JOIN PS_C_PRODIM_ITEM AS PROYEAR ON PS_C_PRO.PROYEAR=PROYEAR.ID")
                .append(" LEFT JOIN PS_C_PRODIM_ITEM AS LARGECLASS ON PS_C_PRO.LARGECLASS=LARGECLASS.ID")
                .append(" LEFT JOIN PS_C_PRODIM_ITEM AS SEX ON PS_C_PRO.SEX=SEX.ID")
                .append(" LEFT JOIN PS_C_PRODIM_ITEM AS SUPBRAND ON PS_C_PRO.SUPBRAND=SUPBRAND.ID")
                .append(" LEFT JOIN PS_C_PRODIM_ITEM AS PRONATURE ON PS_C_PRO.PRONATURE=PRONATURE.ID")
                .append(" LEFT JOIN PS_C_PRODIM_ITEM AS PROSEA ON PS_C_PRO.PROSEA=PROSEA.ID")
                .append(" LEFT JOIN PS_C_PRODIM_ITEM AS PROBAND ON PS_C_PRO.PROBAND=PROBAND.ID")
                .append(" LEFT JOIN PS_C_PRODIM_ITEM AS PROMONTH ON PS_C_PRO.PROMONTH=PROMONTH.ID")
                .append(" LEFT JOIN PS_C_PRODIM_ITEM AS PROSOURCE ON PS_C_PRO.PROSOURCE=PROSOURCE.ID")
                .append(" LEFT JOIN PS_C_PRODIM_ITEM AS PROMOTIONTYPE ON PS_C_PRO.PROMOTIONTYPE=PROMOTIONTYPE.ID")
                .append(" LEFT JOIN PS_C_PRODIM_ITEM AS PROLINE ON PS_C_PRO.PROLINE=PROLINE.ID")
                .append(" LEFT JOIN PS_C_PRODIM_ITEM AS POPULAR ON PS_C_PRO.POPULAR=POPULAR.ID")
                .append(" LEFT JOIN PS_C_PRODIM_ITEM AS COMPOSITION ON PS_C_PRO.COMPOSITION=COMPOSITION.ID")
                .append(" LEFT JOIN PS_C_PRODIM_ITEM AS STYLE ON PS_C_PRO.STYLE=STYLE.ID")
                .append(" LEFT JOIN PS_C_PRODIM_ITEM AS DETAILS ON PS_C_PRO.DETAILS=DETAILS.ID")
                .append(" LEFT JOIN PS_C_PRODIM_ITEM AS PRICEBAND ON PS_C_PRO.PRICEBAND=PRICEBAND.ID")
                .append(" LEFT JOIN PS_C_PRODIM_ITEM AS SERIES ON PS_C_PRO.SERIES=SERIES.ID")
                .append(" LEFT JOIN PS_C_PRODIM_ITEM AS PURNATURE ON PS_C_PRO.PURNATURE=PURNATURE.ID")
                .append(" LEFT JOIN PS_C_PRODIM_ITEM AS BUYER ON PS_C_PRO.BUYER=BUYER.ID")
                .append(" LEFT JOIN PS_C_PRODIM_ITEM AS FOLLOWER ON PS_C_PRO.FOLLOWER=FOLLOWER.ID")
                .append(" LEFT JOIN PS_C_PRODIM_ITEM AS NEWPROER ON PS_C_PRO.NEWPROER=NEWPROER.ID")
                .append(" LEFT JOIN PS_C_PRODIM_ITEM AS MDLARGECLASS ON PS_C_PRO.MDLARGECLASS=MDLARGECLASS.ID")
                .append(" LEFT JOIN PS_C_PRODIM_ITEM AS MDMIDDLECLASS ON PS_C_PRO.MDMIDDLECLASS=MDMIDDLECLASS.ID")
                .append(" LEFT JOIN PS_C_PRODIM_ITEM AS FABRIC ON PS_C_PRO.FABRIC=FABRIC.ID")
                .append(" LEFT JOIN PS_C_PRODIM_ITEM AS SAFETECHCLASS ON PS_C_PRO.SAFETECHCLASS=SAFETECHCLASS.ID")
                .append(" LEFT JOIN PS_C_PRODIM_ITEM AS PROSTANDARD ON PS_C_PRO.PROSTANDARD=PROSTANDARD.ID")
                .append(" LEFT JOIN PS_C_PRODIM_ITEM AS PROSTANDARD1 ON PS_C_PRO.PROSTANDARD1=PROSTANDARD1.ID")
                .append(" LEFT JOIN PS_C_PRODIM_ITEM AS PROSTANDARD2 ON PS_C_PRO.PROSTANDARD2=PROSTANDARD2.ID")
                .append(" LEFT JOIN PS_C_PRODIM_ITEM AS BUYPATTERNER ON PS_C_PRO.BUYPATTERNER=BUYPATTERNER.ID")
                .append(" LEFT JOIN PS_C_PRODIM_ITEM AS DESIGNER ON PS_C_PRO.DESIGNER=DESIGNER.ID")
                .append(" LEFT JOIN PS_C_PRODIM_ITEM AS THEMESTORY ON PS_C_PRO.THEMESTORY=THEMESTORY.ID")
                .append(" LEFT JOIN CP_C_STORE ON PS_C_PRO.DISCENTER=CP_C_STORE.ID")
                .append(" LEFT JOIN PS_C_PRODIM_ITEM AS PURCHASEMODE ON PS_C_PRO.PURCHASEMODE=PURCHASEMODE.ID")
                .append(" LEFT JOIN PS_C_PRODIM_ITEM AS UNIT ON PS_C_PRO.UNIT=UNIT.ID")
                .append(" LEFT JOIN PS_C_SPECGROUP AS PS_C_SPEC1GROUP ON PS_C_PRO.PS_C_SPEC1GROUP_ID=PS_C_SPEC1GROUP.ID")
                .append(" LEFT JOIN PS_C_SPECGROUP AS PS_C_SPEC2GROUP ON PS_C_PRO.PS_C_SPEC2GROUP_ID=PS_C_SPEC2GROUP.ID");

        //拼接where条件
        //包含condition
        if (param.containsKey("condition") && param.getString("condition") != null && !("".equals(param.getString("condition")))) {
            //有拼接where
            sql.append(" WHERE ").append(param.getString("condition"));
            if (param.containsKey("query")) {
                JSONObject queryJo = param.getJSONObject("query");
                if (queryJo.containsKey("fixcolumn") && queryJo.getJSONObject("fixcolumn") != null && queryJo.getJSONObject("fixcolumn").size() > 0) {
                    //包含fixcolum则拼接
                    JSONObject fixJson = queryJo.getJSONObject("fixcolumn");
                    Set<String> keySet = fixJson.keySet();
                    for (int i = 0; i < fixJson.size(); i++) {
                        String key = (String) keySet.toArray()[i];
                        sql.append(" AND ").append(key).append("='").append(fixJson.getString(key)).append("'");
                    }
                }
            }
        } else {
            if (param.containsKey("query")) {
                JSONObject queryJo = param.getJSONObject("query");
                if (queryJo.containsKey("fixcolumn") && queryJo.getJSONObject("fixcolumn") != null && queryJo.getJSONObject("fixcolumn").size() > 0) {
                    //包含fixcolum则拼接where
                    sql.append(" WHERE 1=1");
                    JSONObject fixJson = queryJo.getJSONObject("fixcolumn");
                    Set<String> keySet = fixJson.keySet();
                    for (int i = 0; i < fixJson.size(); i++) {
                        String key = (String) keySet.toArray()[i];
                        sql.append(" AND ").append(key).append("='").append(fixJson.getString(key)).append("'");
                    }
                }
            }
        }

        //查询商品基础信息服务标准
        List<HashMap> selectProLoad = cproQueryMapper.selectProLoad(sql.toString());

        vh.put("code", 0);
        vh.put("message", "SUCCESS");
        vh.put("data", JSONArray.parseArray(JSON.toJSONStringWithDateFormat(selectProLoad, "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue)));
        return vh;
    }

    @Override
    public Map<String, HashMap<String, String>> bactchEcode(List<String> ecodes, String arrtId) {
        List<Map<String, String>> cproLists = new ArrayList<>();
        CproQueryMapper cproQueryMapper = ApplicationContextHandle.getBean(CproQueryMapper.class);
        List<HashMap> cproEntitys = cproQueryMapper.bactchEcode(ecodes);
        Map<String, HashMap<String, String>> cproMaps = new HashMap<>();
        for (HashMap map : cproEntitys) {
            if (map.containsKey("ID") && map.get("ID") != null && map.containsKey("ECODE") && map.get("ECODE") != null) {
                String Ename = "";
                if (map.containsKey(arrtId) && map.get(arrtId) != null) {
                    Ename = map.get(arrtId).toString();
                }
                HashMap<String,String> cproEnameMap=new HashMap<>();
                cproEnameMap.put(map.get("ID").toString(),Ename);
                cproMaps.put(map.get("ECODE").toString(),cproEnameMap);
            }
        }
        return cproMaps;

    }

    @Override
    public ValueHolderV14<List<Long>> queryProId(ProRequest request) {
        ValueHolderV14<List<Long>> holder;

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start.CproQueryCmdImpl.queryProId.ReceiveParams:PsCSkuPrintInfoRequest：{}"),
                    JSONObject.toJSONString(request));
        }

        holder = checkServiceParam(request);

        if (!holder.isOK()) {
            return holder;
        }

        CproMapper mapper = ApplicationContextHandle.getBean(CproMapper.class);
        Integer integer = 0;
//        Integer integer = mapper.selectCount(new QueryWrapper<PsCPro>().lambda()
//                .in(CollectionUtils.isNotEmpty(request.getSexidList()), PsCPro::getSex, request.getSexidList())
//                .in(CollectionUtils.isNotEmpty(request.getProYearIdList()), PsCPro::getProyear, request.getProYearIdList())
//                .in(CollectionUtils.isNotEmpty(request.getProseaIdList()), PsCPro::getProsea, request.getProseaIdList())
//                .in(CollectionUtils.isNotEmpty(request.getPromotiontype()), PsCPro::getPromotiontype, request.getProseaIdList())
//                .in(CollectionUtils.isNotEmpty(request.getPronature()), PsCPro::getPronature, request.getPronature())
//        );

        List<PsCPro> list = new ArrayList<>();
        int pageSize = PsConstantsIF.maxQueryLimit;
        int listSize = integer;
        int page = listSize / pageSize;

        if (listSize % pageSize != 0) {
            page++;
        }

        //分页批量查询
        for (int i = 0; i < page; i++) {

            PageHelper.startPage(i + 1, pageSize);
            List<PsCPro> psCPros = new ArrayList<>();
//            List<PsCPro> psCPros = mapper.selectList(new QueryWrapper<PsCPro>().lambda()
//                    .in(CollectionUtils.isNotEmpty(request.getSexidList()), PsCPro::getSex, request.getSexidList())
//                    .in(CollectionUtils.isNotEmpty(request.getProYearIdList()), PsCPro::getProyear, request.getProYearIdList())
//                    .in(CollectionUtils.isNotEmpty(request.getProseaIdList()), PsCPro::getProsea, request.getProseaIdList())
//                    .in(CollectionUtils.isNotEmpty(request.getPromotiontype()), PsCPro::getPromotiontype, request.getProseaIdList())
//                    .in(CollectionUtils.isNotEmpty(request.getPronature()), PsCPro::getPronature, request.getPronature())
//            );

            if (CollectionUtils.isNotEmpty(psCPros)) {
                list.addAll(psCPros);
                log.info(LogUtil.format(" 第{}批,商品查询参数,{}", i + 1), JSONObject.toJSONString(psCPros));
            }
        }

        List<Long> collect = list.stream().map(PsCPro::getId).collect(Collectors.toList());

        holder.setData(collect);
        return holder;
    }

    private ValueHolderV14<List<Long>> checkServiceParam(ProRequest request) {
        ValueHolderV14<List<Long>> holder = new ValueHolderV14<>(ResultCode.SUCCESS, "查询成功");
        if (request == null) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("REQUEST IS NULL");
            return holder;
        }

        if (CollectionUtils.isEmpty(request.getSexidList())
                && CollectionUtils.isEmpty(request.getProseaIdList())
                && CollectionUtils.isEmpty(request.getProYearIdList())) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("查询条件不能为空");
            return holder;
        }

        return holder;

    }
}
