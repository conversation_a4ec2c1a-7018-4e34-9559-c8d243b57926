package com.jackrain.nea.ps.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.ps.api.CproSupplierCmd;
import com.jackrain.nea.ps.mapper.CproSupItemMapper;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

/**
 * wms  更新商品与供应商关系
 *
 * <AUTHOR>
 * @create 2018/11/23
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps")
public class CproSupplierCmdImpl extends CommandAdapter implements CproSupplierCmd {

    @Override
    public ValueHolder execute(QuerySession querySession) {
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        log.debug(LogUtil.format("CproSupplierCmd.param：") + param.toJSONString());
        Integer maxId= param.getInteger("maxId");
        if(maxId==null || maxId<0){
            vh.put("code", -1);
            vh.put("message", Resources.getMessage("查询失败,参数不合法", querySession.getLocale()));
            return  vh;
        }
        // Mapper
        CproSupItemMapper cproSupItemMapper = ApplicationContextHandle.getBean(CproSupItemMapper.class);
        List<HashMap>list = cproSupItemMapper.selectRelation(maxId);
        vh.put("data",JSONObject.toJSONString(list));
        vh.put("code", 0);
        vh.put("message", Resources.getMessage("查询成功", querySession.getLocale()));
        return  vh;

    }
}
