package com.jackrain.nea.ps.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.CproLikeQueryCmd;
import com.jackrain.nea.ps.mapper.CproLikeQueryMapper;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps")

public class CproLikeQueryCmdImpl extends CommandAdapter implements CproLikeQueryCmd {
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder vh = new ValueHolder();
        CproLikeQueryMapper cproLikeQueryMapper = ApplicationContextHandle.getBean(CproLikeQueryMapper.class);
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
        //String param = (String) event.getParameterValue("query");

        //商品模糊查询
        List<HashMap> supp=new ArrayList<>();
        if(param!=null) {
            String query=param.getString("query");
            supp = cproLikeQueryMapper.proLikeQuery(query);
        }
        vh.put("code",0);
        vh.put("data",supp);
        return vh;
    }
}
