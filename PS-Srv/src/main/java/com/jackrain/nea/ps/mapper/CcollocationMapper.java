package com.jackrain.nea.ps.mapper;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.jdbc.SQL;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * 商品搭配Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CcollocationMapper {

    String FIELDS = "ID, AD_CLIENT_ID, AD_ORG_ID, ISACTIVE, NAME, " +
            "REMARK, CODELIST, NAMELIST, ISSYSTEM, COLLOCATION, " +
            "OWNERID, OWNERNAME, OWNERENAME, CREATIONDATE, MODIFIERID, " +
            "MODIFIERNAME, MODIFIERENAME, MODIFIEDDATE";

    /**
     * 新增
     *
     * @param jsonObject 参数对象
     */
    @InsertProvider(type = CcolloctionSql.class, method = "insert")
    int insert(@Param("jsonObject") JSONObject jsonObject);

    /**
     * 修改
     *
     * @param jsonObject 参数对象
     */
    @InsertProvider(type = CcolloctionSql.class, method = "update")
    int update(@Param("jsonObject") JSONObject jsonObject);

    /**
     * 删除
     *
     * @param id 主键ID
     */
    @Delete("DELETE FROM PS_C_COLLOCATION WHERE ID = #{id}")
    int delete(@Param("id") Long id);

    /**
     * 删除系统生成款
     */
    @Delete("DELETE FROM PS_C_COLLOCATION WHERE ISSYSTEM = 'Y'")
    int deleteBySystem();

    /**
     * 根据ID查询
     *
     * @param id 主键ID
     */
    @Select("SELECT " + FIELDS + " FROM PS_C_COLLOCATION WHERE ID = #{id}")
    HashMap<String, Object> getById(@Param("id") Long id);

    /**
     * 根据Name查询
     *
     * @param name 主键ID
     */
    @Select("SELECT " + FIELDS + " FROM PS_C_COLLOCATION WHERE NAME = #{name}")
    HashMap<String, Object> getByName(@Param("name") String name);

    /**
     * 根据
     *
     * @param codition 搜索条件
     * @param order    排序顺序
     *                 备注：分页条件衔接在order by后面
     */
    @SelectProvider(type = CcolloctionSql.class, method = "listFieldsByCondition")
    ArrayList<HashMap<String, Object>> listByCondition(@Param("codition") String codition,@Param("order") String order);

    @InsertProvider(type = CcolloctionSql.class, method = "saveBatch")
    void saveBatch(@Param("data") JSONArray array);

    @Select("SELECT " + FIELDS + " FROM PS_C_COLLOCATION WHERE ISSYSTEM = 'Y'")
    ArrayList<HashMap<String, Object>> listBySystem();

    class CcolloctionSql {
        public String update(JSONObject jsonObject) {
            return new SQL() {
                {
                    UPDATE("PS_C_COLLOCATION");
                    for (String key : jsonObject.keySet()) {
                        if (!"ID".equals(key)) {
                            SET(key + " = #{" + key + "}");
                        }
                    }
                    WHERE("ID=#{ID}");
                }
            }.toString();
        }

        public String insert(JSONObject jsonObject) {
            return new SQL() {
                {
                    INSERT_INTO("PS_C_COLLOCATION");
                    for (String key : jsonObject.keySet()) {
                        VALUES(key, "#{" + key + "}");
                    }
                }
            }.toString();
        }

        public String listFieldsByCondition(String codition, String order) {
            return new SQL() {
                {
                    SELECT(FIELDS);
                    FROM("PS_C_COLLOCATION");
                    WHERE(codition);
                    ORDER_BY(order);
                }
            }.toString();
        }

        public String saveBatch(Map map) {
            JSONArray array = (JSONArray) map.get("data");
            JSONArray keyArr = new JSONArray();
            keyArr.add("ID");
            keyArr.add("AD_CLIENT_ID");
            keyArr.add("AD_ORG_ID");
            keyArr.add("NAME");
            keyArr.add("REMARK");
            keyArr.add("CODELIST");
            keyArr.add("NAMELIST");
            keyArr.add("COLLOCATION");
            keyArr.add("ISSYSTEM");
            keyArr.add("OWNERID");
            keyArr.add("OWNERNAME");
            keyArr.add("OWNERENAME");
            keyArr.add("CREATIONDATE");
            keyArr.add("MODIFIERID");
            keyArr.add("MODIFIERNAME");
            keyArr.add("MODIFIERENAME");
            keyArr.add("MODIFIEDDATE");

            StringBuilder sb = new StringBuilder();
            sb.append("INSERT INTO PS_C_COLLOCATION");
            sb.append("(ID");
            for (Object key : keyArr) {
                if (!"ID".equals(key.toString())) {
                    sb.append("," + key.toString());
                }
            }
            sb.append(")");
            sb.append("VALUES");
            for (Object o : array) {
                if (sb.toString().endsWith(")")) {
                    sb.append(",");
                }
                sb.append("(");
                JSONObject object = JSON.parseObject(o.toString());
                sb.append(object.getString("ID"));
                for (Object key : keyArr) {
                    if (!"ID".equals(key.toString())) {
                        if (object.containsKey(key.toString())) {
                            sb.append(",'" + object.getString(key.toString()) + "'");
                        } else {
                            sb.append(",NULL");
                        }
                    }
                }
                sb.append(")");
            }
            sb.append(" ON DUPLICATE KEY UPDATE NAME=values(NAME),CODELIST=values(CODELIST),NAMELIST=values(NAMELIST)," +
                    "MODIFIERID=values(MODIFIERID),MODIFIERNAME=values(MODIFIERNAME),MODIFIERENAME=values(MODIFIERENAME)," +
                    "MODIFIEDDATE=values(MODIFIEDDATE) ");
            return sb.toString();
        }
    }
}
