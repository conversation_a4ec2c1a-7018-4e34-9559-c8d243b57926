package com.jackrain.nea.ps.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * 标准商品档案同步至00、000配销中心
 */
@Mapper
public interface CproOcMapper {

    @Select("SELECT ID FROM PS_C_SKU WHERE CP_C_DISTRIB_ID=0 AND PS_C_PRO_ID=#{objid}")
    List<Long> skuIds(@Param("objid") Long objid);

    @Select("SELECT ID FROM CP_C_STOREORG WHERE ECODE = #{ecode}")
    List<Long> distribIds(@Param("ecode") String ecode);
}
