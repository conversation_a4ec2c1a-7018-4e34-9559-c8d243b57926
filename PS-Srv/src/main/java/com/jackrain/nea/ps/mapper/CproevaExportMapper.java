package com.jackrain.nea.ps.mapper;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.HashMap;
import java.util.List;

@Mapper
public interface CproevaExportMapper {

    @Select("SELECT ID,ENAME FROM PS_C_SCORE WHERE ISACTIVE='Y' ORDER BY ORDERNO ASC")
    List<HashMap> querystartinfo();

    class CproevaqueryProvider {
        //根据评价时间店仓排序
        public String evaquerylist(Long proid, JSONObject wherekeys) {
            StringBuffer sql = new StringBuffer();
            sql.append("SELECT a.ID AS ID,a.REMARK AS REMARK,a.DL_B_TRAN_PLAN_ID AS OC_C_OREDER_ID,a.CP_C_STORE_ID AS STOREID,")
                    .append("a.EVALUATEDATE AS EVATIME,b.ENAME AS STORENAME,c.ENAME AS EVAENAME,c.NAME AS EVANAME")
                    .append(" FROM PS_C_EVALUATE a")
                    .append(" LEFT JOIN CP_C_STORE b ON a.CP_C_STORE_ID=b.ID")
                    .append(" LEFT JOIN USERS c ON a.EVAID=c.ID");
            if (wherekeys != null && wherekeys.get("flagid") != null) {
                sql.append(" LEFT JOIN PS_C_EVA_FLAG_ITEM e ON a.ID=e.PS_C_EVA_ID");
            }
            sql.append(" WHERE a.PS_C_PRO_ID=" + proid);
            if (wherekeys != null && wherekeys.get("flagid") != null) {
                sql.append(" AND e.PS_C_SCORE_ITEM_ID=" + wherekeys.get("flagid"));
            }
            if (wherekeys != null && wherekeys.getJSONArray("ids").size()>0) {
                sql.append(" AND a.ID in (");
                JSONArray joarray=wherekeys.getJSONArray("ids");
                for(int i=0;i<joarray.size();i++){
                    sql.append(joarray.get(i));
                }
                sql.append(")");
            }
            return sql.toString();
        }
    }

    //根据评价时间店仓排序
    @SelectProvider(type = CproevaExportMapper.CproevaqueryProvider.class, method = "evaquerylist")
    List<HashMap> querybyproidlist(Long proid, JSONObject wherekeys);

    //  查询所有标签的名称
    @Select("SELECT b.ENAME FROM PS_C_EVA_FLAG_ITEM a LEFT JOIN PS_C_SCORE_ITEM b ON a.PS_C_SCORE_ITEM_ID=b.ID" +
            " WHERE a.PS_C_SCORE_ID=#{startid} AND PS_C_EVA_ID=#{evaid} AND b.ISACTIVE='Y'")
    List<HashMap> evaflaglist(@Param("startid") Long startid,@Param("evaid") Long evaid);

    @Select("SELECT ID FROM PS_C_SCORE WHERE ISACTIVE='Y' ORDER BY ORDERNO ASC")
    List<HashMap> scoreidlist();

    //查询以平价商品接口的星级评分ID和分数
    @Select("SELECT PS_C_SCORE_ID,STARTSCORE FROM PS_C_EVA_START_ITEM WHERE PS_C_EVA_ID=#{evaid} AND PS_C_SCORE_ID=#{startid}")
    HashMap evastartscore(@Param("evaid") Long evaid, @Param("startid") Long startid);


}
