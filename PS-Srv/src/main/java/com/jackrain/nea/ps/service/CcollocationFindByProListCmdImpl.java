package com.jackrain.nea.ps.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.CcollocationFindByProListCmd;
import com.jackrain.nea.ps.mapper.CcollocationItemMapper;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

/**
 * <p>Title: CcollocationFindByProListCmdImpl</p>
 * <p>Date: 2018/6/8 </p>
 * <p>Description: </p>
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps")
public class CcollocationFindByProListCmdImpl extends CommandAdapter implements CcollocationFindByProListCmd {

    @Autowired
    private CcollocationItemMapper ccollocationItemMapper;

    @Override
    public ValueHolder execute(HashMap map) throws NDSException {
        JSONObject param = new JSONObject(map);

        List<HashMap> collocationItems = ccollocationItemMapper.findByProList(param);

        JSONArray collocationItemList = new JSONArray();
        for (HashMap item : collocationItems) {
            collocationItemList.add(new JSONObject(item));
        }

        ValueHolder valueHolder = new ValueHolder();
        valueHolder.put("code", 0);
        valueHolder.put("message", "SUCCESS");
        valueHolder.put("data", collocationItemList);
        return valueHolder;
    }
}
