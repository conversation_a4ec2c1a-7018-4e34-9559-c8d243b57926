package com.jackrain.nea.ps.service;

/**
 * create by tzp 2017/9/28
 */

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.CgbruleVoidCmd;
import com.jackrain.nea.ps.mapper.CgbruleMapper;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;

@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps")
public class CgbruleVoidCmdImpl extends CommandAdapter implements CgbruleVoidCmd {

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        CgbruleMapper cgbruleMapper = ApplicationContextHandle.getBean(CgbruleMapper.class);
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
        Long objid=param.getLong("objid");
        String selectDocno = null;

        selectDocno = cgbruleMapper.selectDocno(objid);
        //判断当前记录是否作废
        String selectIsActive = cgbruleMapper.selectIsActive(objid);
        if ("N".equals(selectIsActive)) {
            throw new NDSException(Resources.getMessage("当前记录已作废！", querySession.getLocale()));
        }
        //判断当前记录是否已经提交
        int selectStatus = cgbruleMapper.selectStatus(objid);
        if (selectStatus != 1) {
            throw new NDSException(Resources.getMessage("当前记录已提交，不允许作废！", querySession.getLocale()));
        }
        //设置废弃人
        JSONObject jsonObject = new JSONObject();
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        jsonObject.put("CANCELID", Integer.valueOf(querySession.getUser().getId()).longValue());
        jsonObject.put("CANCELDATE", timestamp);
        jsonObject.put("ISACTIVE", "N");
        jsonObject.put("CANCELNAME",querySession.getUser().getName());
        jsonObject.put("CANCELENAME",querySession.getUser().getEname());
        jsonObject.put("ID", objid);
        cgbruleMapper.updateGbrule(jsonObject);

        vh.put("code", 0);
        vh.put("message", Resources.getMessage("SUCCESS", querySession.getLocale()));
        return vh;
    }
}
