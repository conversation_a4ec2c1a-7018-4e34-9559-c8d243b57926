package com.jackrain.nea.ps.service;

import com.jackrain.nea.ps.api.CproProvidePurCmd;
import com.jackrain.nea.ps.mapper.CproMapper;
import com.jackrain.nea.sys.CommandAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @Date Create in 9:15 2018-5-7
 * @Description: 配销中心商品档案服务
 * @Modified by:
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps")
public class CproProvidePurCmdImpl extends CommandAdapter implements CproProvidePurCmd {
    @Autowired
    CproMapper cproMapper;

    /**
     * @Description: 配销中心商品档案查询接口ByID
     * @Param:
     * @return:
     * @Author: Yangxy
     * @Date: 2018-05-07 10:44:58
     */
    @Override
    public HashMap getProById(Long id) {
        return cproMapper.getById(id);
    }
}
