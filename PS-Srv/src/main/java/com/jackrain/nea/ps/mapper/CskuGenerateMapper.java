package com.jackrain.nea.ps.mapper;

import com.alibaba.fastjson.JSONObject;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.jdbc.SQL;

import java.util.HashMap;
import java.util.List;
import java.util.Set;

@Mapper
public interface CskuGenerateMapper {
    class CskuProvider {
        public String update(final JSONObject jsonObject) {
            return new SQL() {
                {
                    Set<String> keySet = jsonObject.keySet();
                    UPDATE("PS_C_SKU");
                    for (int i = 0; i < jsonObject.size(); i++) {
                        String key = (String) keySet.toArray()[i];
                        if ("ID".equals(key)) {
                            WHERE("ID=#{" + key + "}");
                        } else {
                            SET(key + "= #{" + key + "}");
                        }
                    }
                    WHERE("CP_C_DISTRIB_ID=0");
                }
            }.toString();
        }

        public String insert(final JSONObject jsonObject) {
            return new SQL() {
                {
                    Set<String> keySet = jsonObject.keySet();
                    INSERT_INTO("PS_C_SKU");
                    for (int i = 0; i < jsonObject.size(); i++) {
                        String key = (String) keySet.toArray()[i];
                        VALUES(key, "#{" + key + "}");
                    }
                }
            }.toString();
        }

        public String updatepro(final JSONObject jsonObject) {
            return new SQL() {
                {
                    Set<String> keySet = jsonObject.keySet();
                    UPDATE("PS_C_PRO");
                    for (int i = 0; i < jsonObject.size(); i++) {
                        String key = (String) keySet.toArray()[i];
                        if ("ID".equals(key)) {
                            WHERE("ID=#{" + key + "}");
                        } else {
                            SET(key + "= #{" + key + "}");
                        }
                    }
                }
            }.toString();
        }
    }

    //判断传入的标准商品档案ID是否存在
    @Select("SELECT COUNT(1) FROM PS_C_PRO WHERE ID=#{id}")
    int selectProCount(@Param("id") Long id);

    @Select("SELECT ENAME FROM PS_C_PRO WHERE ID = #{proId}")
    String selectProNameByProId(@Param("proId") Long proId);

    //根据前端传入的【标准商品档案】ID，获取【标准商品档案】表中的【规格ID集合】、【使用规格ID集】
    @Select("SELECT PS_C_SPEC_IDS,PS_C_SPECGROUP_IDS FROM PS_C_PRO WHERE ID=#{id}")
    HashMap selectSpecIdAndSpecGroupId(@Param("id") Long id);

    //根据规格id在规格表中找到系统标识字段为1和2的记录
    @Select("SELECT FLAG,ID FROM PS_C_SPEC WHERE ID=#{id}")
    HashMap selectSpecFlag(@Param("id") Long id);

    //查找【标准条码】表中所有【商品】ID=以上解析出的配销中心商品档案ID的条码记录集（包含条码ID，商品ID，规格ID集合）
    @Select("SELECT ID,PS_C_PRO_ID,PS_C_SPEC1OBJ_ID,PS_C_SPEC2OBJ_ID,ECODE FROM PS_C_SKU WHERE PS_C_PRO_ID=#{ps_c_pro_id} AND PS_C_SPEC1OBJ_ID IS NOT NULL AND PS_C_SPEC2OBJ_ID IS NOT NULL")
    List<HashMap> selectSku(@Param("ps_c_pro_id") Long ps_c_pro_id);

    @Select("SELECT ID,PS_C_PRO_ID,PS_C_SPEC1OBJ_ID,PS_C_SPEC2OBJ_ID,ECODE FROM PS_C_SKU WHERE PS_C_PRO_ID=#{ps_c_pro_id} AND PS_C_SPEC1OBJ_ID IS NOT NULL AND PS_C_SPEC2OBJ_ID IS NULL")
    List<HashMap> selectSkuObj2IsNull(@Param("ps_c_pro_id") Long ps_c_pro_id);

    @Select("SELECT ID,PS_C_PRO_ID,PS_C_SPEC1OBJ_ID,PS_C_SPEC2OBJ_ID,ECODE FROM PS_C_SKU WHERE PS_C_PRO_ID=#{ps_c_pro_id} AND PS_C_SPEC1OBJ_ID IS NULL AND PS_C_SPEC2OBJ_ID IS NOT NULL")
    List<HashMap> selectSkuObj1IsNull(@Param("ps_c_pro_id") Long ps_c_pro_id);

    @Select("SELECT ID,PS_C_PRO_ID,PS_C_SPEC1OBJ_ID,PS_C_SPEC2OBJ_ID,ECODE FROM PS_C_SKU WHERE PS_C_PRO_ID=#{ps_c_pro_id} AND PS_C_SPEC1OBJ_ID IS NULL AND PS_C_SPEC2OBJ_ID IS NULL")
    List<HashMap> selectSkuAllNull(@Param("ps_c_pro_id") Long ps_c_pro_id);

    //查询当前商品的ECODE
    @Select("SELECT ECODE FROM PS_C_PRO WHERE ID=#{id}")
    String selectProEcode(@Param("id") Long id);

    //查询颜色的ecode
    @Select("SELECT ECODE FROM PS_C_SPECOBJ WHERE ID=#{id}")
    String selectColorEcode(@Param("id") Long id);

    //查询尺寸的ecode
    @Select("SELECT ECODE FROM PS_C_SPECOBJ WHERE ID=#{id}")
    String selectSizeEcode(@Param("id") Long id);

    //查询颜色的ename
    @Select("SELECT ENAME FROM PS_C_SPECOBJ WHERE ID=#{id}")
    String selectColorEname(@Param("id") Long id);

    //查询尺寸的ename
    @Select("SELECT ENAME FROM PS_C_SPECOBJ WHERE ID=#{id}")
    String selectSizeEname(@Param("id") Long id);

    //插入标准条码标
    @InsertProvider(type = CskuProvider.class, method = "insert")
    int insertSku(JSONObject jsonObject);

    //更新条码表
    @UpdateProvider(type = CskuProvider.class, method = "update")
    int updateSku(JSONObject jsonObject);

    //查询条码是否存在
    @Select("SELECT COUNT(1) FROM PS_C_SKU WHERE ID=#{id}")
    int selectDistrib(@Param("id") Long id);

    //删除标准条码
    @Delete("DELETE FROM PS_C_SKU WHERE ID=#{id}")
    int deletSku(@Param("id") Long id);

    //如笛卡尔积中的【配销中心商品档案ID+规格ID集合】，判断是否存在该集合
    @Select("SELECT ID FROM PS_C_SKU WHERE PS_C_PRO_ID=#{ps_c_pro_id} AND PS_C_SPECOBJ_IDS=#{ps_c_specobj_ids}")
    Long selectProAndIds(@Param("ps_c_pro_id") Long ps_c_pro_id, @Param("ps_c_specobj_ids") String ps_c_specobj_ids);

    @Select("SELECT ID FROM PS_C_SKU WHERE PS_C_PRO_ID=#{ps_c_pro_id} AND PS_C_SPECOBJ_IDS IS NULL")
    Long selectIds(@Param("ps_c_pro_id") Long ps_c_pro_id);

    //判断条码生成规则是否为空
    @Select("SELECT PS_C_SKURULE_ID FROM PS_C_PRO WHERE ID=#{id}")
    Long selectSkuRuleId(@Param("id") Long id);

    //判断条码规则是否启用
    @Select("SELECT ISACTIVE,ENAME FROM PS_C_SKURULE WHERE ID=#{id}")
    HashMap selectIsactive(@Param("id") Long id);

    //获取【条码生成规则】的所有明细数据中的【参与编码规格】
    @Select("SELECT PS_C_SPEC_ID FROM PS_C_SKURULE_ITEM WHERE PS_C_SKURULE_ID=#{ps_c_skurule_id}")
    List<HashMap> selectspecId(@Param("ps_c_skurule_id") Long ps_c_skurule_id);

    @Select("SELECT ECODE FROM PS_C_SPEC WHERE ID=#{id}")
    String selectSpecEoce(@Param("id") Long id);

    //解析【条码规格ID集合】，根据上面的位置，获取每个【参与编码规格】对应的具体【规格实例】的ID及ECODE，假设为CODE1，CODE2，CODE3
    @Select("SELECT ECODE FROM PS_C_SPECOBJ WHERE ID=#{id}")
    String selectObjEcode(@Param("id") Long id);

    //按照【条码生成规则】明细中的【编码排序】正序排序，获取所有明细记录的【前缀】（前缀1，前缀2，前缀3）
    @Select("SELECT PS_C_SPEC_ID,PREFIX,ORDERNO FROM PS_C_SKURULE_ITEM WHERE PS_C_SKURULE_ID=#{ps_c_skurule_id} ORDER BY ORDERNO ASC")
    List<HashMap> selectPrefix(@Param("ps_c_skurule_id") Long ps_c_skurule_id);

    //获取【条码生成规则】表头的【款号前缀】、【后缀】
    @Select("SELECT PREFIX,SUFFIX,ID FROM PS_C_SKURULE WHERE ID=#{id}")
    HashMap selectSkuRulePreFixAndSuffix(@Param("id") Long id);

    //查询尺寸或颜色编码
    @Select("SELECT ECODE FROM PS_C_SPECOBJ WHERE ID=#{id}")
    String selectColorOrSizeEcode(@Param("id") Long id);

    @Select("SELECT ID FROM PS_C_SKU WHERE PS_C_PRO_ID=#{ps_c_pro_id} AND PS_C_SPEC1OBJ_ID=#{spec1objid} AND PS_C_SPEC2OBJ_ID=#{spec2objid} AND CP_C_DISTRIB_ID=0")
    Long skuId(@Param("ps_c_pro_id") Long ps_c_pro_id, @Param("spec1objid") Long spec1objid, @Param("spec2objid") Long spec2objid);

    @UpdateProvider(type = CskuProvider.class, method = "updatepro")
    int updatepro(JSONObject jsonObject);

    @Select("SELECT DISTINCT(PS_C_SPEC1OBJ_ID) FROM PS_C_SKU WHERE PS_C_PRO_ID=#{ps_c_pro_id}")
    List<HashMap> selectMainAndFab(@Param("ps_c_pro_id") Long ps_c_pro_id);

    @Select("SELECT DISTINCT(PS_C_SPEC2OBJ_ID) FROM PS_C_SKU WHERE PS_C_PRO_ID=#{ps_c_pro_id}")
    List<HashMap> selectSize(@Param("ps_c_pro_id") Long ps_c_pro_id);

    @Select("SELECT PRONATURE,PURCHASEMODE FROM PS_C_PRO WHERE ID=#{id}")
    HashMap selectNatureAndmode(@Param("id") Long id);

    @Select("SELECT COUNT(1) FROM PS_C_SKU WHERE ECODE=#{ecode} AND CP_C_DISTRIB_ID=0 AND ID!=#{id}")
    int skuEcodeCount(@Param("ecode") String ecode, @Param("id") Long id);

    // @Select("SELECT COUNT(1) FROM PS_C_SKU WHERE ECODE=#{ecode} AND CP_C_DISTRIB_ID=0") 去除配销中心判断
    @Select("SELECT COUNT(1) FROM PS_C_SKU WHERE ECODE=#{ecode}")
    int skuEcodeCountbyinsert(@Param("ecode") String ecode);

    @Delete("DELETE FROM PS_C_SKU WHERE PS_C_PRO_ID=#{ps_c_pro_id} AND CP_C_DISTRIB_ID=0")
    int deleteSku(@Param("ps_c_pro_id") Long ps_c_pro_id);

    @Select("SELECT ID,ECODE FROM PS_C_SKU WHERE PS_C_PRO_ID=#{ps_c_pro_id} AND CP_C_DISTRIB_ID=0")
    List<HashMap> selectSkuid(@Param("ps_c_pro_id") Long ps_c_pro_id);

    @Select("SELECT ID,PS_C_SPEC1GROUP_ID,PS_C_SPEC2GROUP_ID FROM PS_C_PRO WHERE ID=#{id}")
    HashMap selectgroup(@Param("id") Long id);

    @Select("SELECT PS_C_BRAND_ID FROM PS_C_PRO WHERE ID=#{id}")
    Long selectBrandid(@Param("id") Long id);

    @Select("SELECT COUNT(1) FROM PS_C_SKU WHERE PS_C_PRO_ID = #{proId} AND PS_C_SPEC1OBJ_ID = #{colorSpecIds} AND PS_C_SPEC2OBJ_ID = #{sizeSpecIds}")
    int querySkuIsExist(@Param("proId") Long proId, @Param("colorSpecIds") Long colorSpecIds, @Param("sizeSpecIds") Long sizeSpecIds);

}
