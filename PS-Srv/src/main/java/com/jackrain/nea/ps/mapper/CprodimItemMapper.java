package com.jackrain.nea.ps.mapper;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.ps.api.table.PsCProdimItem;
import com.jackrain.nea.ps.entity.CprodimItemEntity;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.jdbc.SQL;

import java.util.HashMap;


@Mapper
public interface CprodimItemMapper extends ExtentionMapper<PsCProdimItem> {

    class CprodimItemSqlProvider {
        public String insert(final JSONObject map) {
            return new SQL() {
                {
                    INSERT_INTO("PS_C_PRODIM_ITEM");
                    for (String key : map.keySet()) {
                        VALUES(key, "#{" + key + "}");
                    }
                }
            }.toString();
        }

        public String update(JSONObject map) {
            return new SQL() {
                {
                    UPDATE("PS_C_PRODIM_ITEM");
                    for (String key : map.keySet()) {
                        if (!key.equals("ID")) {
                            SET(key + "= #{" + key + "}");
                        }
                    }
                    WHERE("ID = #{ID}");
                }
            }.toString();
        }

        public String selectByEcode(@Param("ecode") String ecode, @Param("prodimId") Long prodimId, Long id) {
            return new SQL() {
                {
                    SELECT("count(*)");
                    FROM("PS_C_PRODIM_ITEM");
                    WHERE("ECODE=#{ecode} and PS_C_PRODIM_ID=#{prodimId} and isactive = 'Y'");
                    if (id > 0) {
                        WHERE("ID!=" + id);
                    }
                }
            }.toString();
        }

        public String selectByEname(@Param("ename") String ename, @Param("prodimId") Long prodimId, Long id) {
            return new SQL() {
                {
                    SELECT("count(*)");
                    FROM("PS_C_PRODIM_ITEM");
                    WHERE("ENAME=#{ename} and PS_C_PRODIM_ID=#{prodimId} and isactive = 'Y'");
                    if (id > 0) {
                        WHERE("ID!=" + id);
                    }
                }
            }.toString();
        }
    }

    @InsertProvider(type = CprodimItemSqlProvider.class, method = "insert")
    int insertSubByMap(JSONObject map);

    @Delete("DELETE FROM ps_c_prodim_item WHERE ID = #{id}")
    int deleteSubById(@Param("id") Long id);

    @UpdateProvider(type = CprodimItemSqlProvider.class, method = "update")
    int updateSubByMap(JSONObject map);

    @Select("select ENAME,ECODE from ps_c_prodim_item where ID=#{id}")
    CprodimItemEntity selectByEname(@Param("id") Long id);

    @Select("select ENAME from ps_c_prodim_item where ID=#{id}")
    String getNameById(@Param("id") Long id);

    @Select("select * from ps_c_prodim_item where PS_C_PRODIM_ID=#{prodimId}")
    CprodimItemEntity selectByProdimId(@Param("prodimId") Long prodimId);

    @Select("select count(*) from ps_c_prodim_item where ID=#{id}")
    int selectCountById(@Param("id") Long id);

    @SelectProvider(type = CprodimItemSqlProvider.class, method = "selectByEcode")
    int selectEcode(@Param("ecode") String ecode, @Param("prodimId") Long prodimId, Long id);

    @SelectProvider(type = CprodimItemSqlProvider.class, method = "selectByEname")
    int selectEname(@Param("ename") String ename, @Param("prodimId") Long prodimId, Long id);

    //查询主表编码空值
    @Select("select ISCODENULL from ps_c_prodim where ID=#{id}")
    int selectIsCode(@Param("id") Long id);

    @Select("select AD_PROCOLUMN_NAME from ps_c_prodim where ID=#{id}")
    String selectAdProcolumnName(@Param("id") Long id);

    //查询是否在标准商品档案中存在
    @Select("select count(*) from ps_c_pro where ${adProcolumnName}=#{itemId}")
    int selectPro(@Param("adProcolumnName") String adProcolumnName, @Param("itemId") Long itemId);

    //查询是否在配销商品档案中存在
    @Select("select count(*) from ps_c_pro where ${adProcolumnName}=#{itemId} and CP_C_DISTRIB_ID<>0")
    int selectProDistrib(@Param("adProcolumnName") String adProcolumnName, @Param("itemId") Long itemId);

    @Select("select ID,ENAME,ECODE from ps_c_prodim_item where ID=#{id}")
    HashMap getName(@Param("id") Long id);

    /**
     * 根据id查询明细表信息
     *
     * @param id
     * @return
     */
    @Select("select * from ps_c_prodim_item where id=#{id} and isactive='Y'")
    PsCProdimItem selectPsCProDimItemById(@Param("id") Long id);

    @Select("SELECT * FROM `ps_c_prodim_item` n LEFT JOIN ps_c_prodim m on n ps_c_prodim_id = m.id where m.AD_PROCOLUMN_NAME = #{type} and n.ECODE = #{sapEcode} and n.isactive = 'Y'")
    PsCProdimItem findProdimItemByEcode(@Param("type") String type,  @Param("sapEcode") String sapEcode);


}
