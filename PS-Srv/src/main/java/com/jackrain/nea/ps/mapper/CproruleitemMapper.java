package com.jackrain.nea.ps.mapper;

import com.alibaba.fastjson.JSONObject;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.jdbc.SQL;

import java.util.List;
import java.util.Set;

@Mapper
public interface CproruleitemMapper {

    class CproruleitemProvider {
        public String update(final JSONObject jsonObject) {
            return new SQL() {
                {

                    Set<String> keySet = jsonObject.keySet();
                    UPDATE("PS_C_PRORULE_ITEM");
                    for (int i = 0; i < jsonObject.size(); i++) {
                        String key = (String) keySet.toArray()[i];
                        if("ID".equals(key)){
                            WHERE("ID=#{"+key+"}");
                        }else{
                            SET(key +"= #{"+key+"}");
                        }
                    }
                }
            }.toString();
        }

        public String insert(final JSONObject jsonObject){
            return new SQL(){
                {
                    INSERT_INTO("PS_C_PRORULE_ITEM");
                    Set<String> keySet = jsonObject.keySet();
                    for (int i = 0; i < jsonObject.size(); i++) {
                        String key = (String) keySet.toArray()[i];
                        VALUES(key,"#{"+key+"}");
                    }
                }
            }.toString();
        }
    }

    @InsertProvider(type = CproruleitemProvider.class, method = "insert")
    int insertProruleItem(JSONObject jsonObject);

    @UpdateProvider(type = CproruleitemProvider.class, method = "update")
    int updateProruleItem(JSONObject jsonObject);

    @Delete("DELETE FROM PS_C_PRORULE_ITEM WHERE ID=#{id}")
    int deleteProruleItem(@Param("id") Long id);

    @Select("SELECT ID FROM PS_C_PRORULE_ITEM WHERE PS_C_PRORULE_ID=#{ps_c_prorule_id}")
    List<Long> selectItemId(@Param("ps_c_prorule_id") Long ps_c_prorule_id);

    @Select("SELECT ORDERNO FROM PS_C_PRORULE_ITEM WHERE PS_C_PRORULE_ID = #{proruleID}")
    List<Long> selectOrderNo(@Param("proruleID") Long proruleID);

    @Select("SELECT PREFIX FROM PS_C_PRORULE_ITEM WHERE ID = #{ruleId}")
    String queryPrefix(@Param("ruleId") Long ruleId);

    @Select("SELECT PS_C_PRODIM_ID FROM PS_C_PRORULE_ITEM WHERE ID = #{ruleId}")
    Long queryProdimId(@Param("ruleId") Long ruleId);
}


