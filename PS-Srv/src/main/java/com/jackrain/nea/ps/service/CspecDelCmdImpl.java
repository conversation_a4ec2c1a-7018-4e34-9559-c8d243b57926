package com.jackrain.nea.ps.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.CspecDelCmd;
import com.jackrain.nea.ps.mapper.CspecMapper;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2017/10/16
 */
@Slf4j
@Component
@Service(protocol="dubbo", validation="true", version="1.0", group="ps")
public class CspecDelCmdImpl extends CommandAdapter implements CspecDelCmd {
    @Autowired
    private PropertiesConf propertiesConf;

    @Override
    public ValueHolder execute(HashMap map) throws NDSException {
        ValueHolder vh = new ValueHolder();
        vh.put("code",0);
        vh.put("vhmessage","Hello EDAS Test");
        return vh;
    }

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        CspecMapper cspecMapper = ApplicationContextHandle.getBean(CspecMapper.class);
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),"yyyy-MM-dd HH:mm:ss"),Feature.OrderedField);

        int count5=0;
        //主表不为空
        if(param!= null){
            //判断该条记录是否不存在了
            long id=param.getLong("objid");
            if(id<0){
                throw new NDSException(Resources.getMessage("objid不能小于0",querySession.getLocale()));

            }
            int count=cspecMapper.selectid(id);
            if(count==0){
                throw new NDSException(Resources.getMessage("当前记录已不存在！",querySession.getLocale()));

            }
            //系统标识符不为0，则不允许删除

            long flag=cspecMapper.selectflag(id);
            String ename=cspecMapper.selectenamebyid(id);
            if(flag!=0){
                //throw new NDSException("该条记录不允许被删除");
                throw new NDSException(Resources.getMessage(ename+"为系统保留数据，不允许删除！",querySession.getLocale()));

            }
            //判断是否在条码生成规则明细表中，为真则不允许删除
            int count2=cspecMapper.selectitem(id);
            if(count2>0){
                //throw new NDSException("该条记录不允许被删除");
                throw new NDSException(Resources.getMessage(ename+"已有条码生成规则记录，不允许删除！",querySession.getLocale()));

            }
            //判断是否在规则组表中，存在的话不允许被删除
            int count4=cspecMapper.selectgroup(id);
            if(count4>0){
                //throw new NDSException("该条记录不允许被删除");
                throw new NDSException(Resources.getMessage(ename+"已有规格组记录，不允许删除！",querySession.getLocale()));
            }
            count5=cspecMapper.delete(id);
        }

        vh.put("code",0);
        vh.put("message",Resources.getMessage("删除成功的记录数:"+count5,querySession.getLocale()));
        return vh;
    }

}
