package com.jackrain.nea.ps.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.util.TypeUtils;
import com.jackrain.nea.common.ValueHolderUtils;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.CshapegroupDelCmd;
import com.jackrain.nea.ps.mapper.CshapeMapper;
import com.jackrain.nea.ps.mapper.CshapegroupMapper;
import com.jackrain.nea.ps.mapper.CskuMapper;
import com.jackrain.nea.ps.mapper.CspecLoadMapper;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;

/**
 * 号型组删除
 *
 * <AUTHOR>
 * @date 2017/9/26.
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps")
public class CshapegroupDelCmdImpl extends CommandAdapter implements CshapegroupDelCmd {

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);

        log.info(LogUtil.format("号型组param删除数据：") + param.toJSONString());
        if (param != null) {
            Boolean isDel = param.getBoolean("isdelmtable");
            Long cshapegroupid = TypeUtils.castToLong(param.get("objid"));
            if (isDel != null && cshapegroupid != null) {
                if (isDel) {
                    //判断主表是否有明细，有则无法删除
                    CshapegroupMapper cshapegroupMapper = ApplicationContextHandle.getBean(CshapegroupMapper.class);
                    long num = cshapegroupMapper.selectNumByShapeGrpopId(cshapegroupid);
                    if (num > 0) {
                        throw new NDSException(Resources.getMessage("该号型组已有明细，不允许删除！", querySession.getLocale()));
                    }
                    // 删除主表 -- 带事物，明细或者主表失败均回滚
                    return groupdelete(cshapegroupid, querySession);
                } else {
                    // 纯删除明细 -- 不带事物
                    JSONObject fixcolumn = param.getJSONObject("tabitem");
                    log.info(LogUtil.format("号型组参数fixcolumn数据：") + fixcolumn.toJSONString());
                    if (fixcolumn != null) {
                        JSONArray itemArray = fixcolumn.getJSONArray("PS_C_SHAPE");
                        log.info(LogUtil.format("号型组参数fixcolumn数据：") + itemArray.toJSONString());
                        if (itemArray != null && itemArray.size() > 0) {
                            return getValueHolderOnDeleteItems(itemArray, cshapegroupid, querySession);
                        } else {
                            return ValueHolderUtils.fail("请先选择需要删除的明细记录！");
                        }
                    }
                }
            }
        }
        return ValueHolderUtils.fail("请先选择需要删除的记录！");
    }

    /**
     * 删除明细返回数据
     *
     * @param itemArray 明细数据
     * @param id        主表ID
     * @param session   自定义会话信息
     */
    private ValueHolder getValueHolderOnDeleteItems(JSONArray itemArray, Long id, QuerySession session) {
        // 查询当前主表号型组
        CshapegroupMapper cshapegroupMapper = ApplicationContextHandle.getBean(CshapegroupMapper.class);
        HashMap shape = cshapegroupMapper.findgroupcolumn(id);
        if (shape == null) {
            return ValueHolderUtils.fail("当前号型组记录已不存在！");
        }
        // 删除号型组明细
        JSONObject error = new JSONObject();
        int fail = 0;
        int success = 0;
        // 遍历明细数组
        for (Object o : itemArray) {
            Long itemId = Long.valueOf(o.toString());
            if (deleteShape(itemId, error, id)) {
                success++;
            } else {
                fail++;
            }
        }
        // 更新主表时间
        if (success > 0) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("ID", id);
            jsonObject.put("MODIFIERID", session.getUser().getId());
            jsonObject.put("MODIFIERNAME", session.getUser().getName());
            jsonObject.put("MODIFIERENAME", session.getUser().getEname());
            jsonObject.put("MODIFIEDDATE", new Timestamp(System.currentTimeMillis()));
            cshapegroupMapper.groupupdate(jsonObject);
        }
        if (fail > 0) {
            return ValueHolderUtils.fail("删除失败！", error.getJSONArray("data"));
        }
        return ValueHolderUtils.success("删除成功！", error.getJSONArray("data"));
    }


    @Transactional(rollbackFor = Exception.class)
    public ValueHolder groupdelete(Long cshapegroupid, QuerySession querySession) throws NDSException {
        CshapegroupMapper cshapegroupMapper = ApplicationContextHandle.getBean(CshapegroupMapper.class);
        CshapeMapper cshapeMapper = ApplicationContextHandle.getBean(CshapeMapper.class);
        ValueHolder vh = new ValueHolder();
        //TODO 该号型是否被标准商品表应用
        Integer pronum = cshapegroupMapper.findprogroupnum(cshapegroupid);
        if (pronum > 0) {
//      已定义商品，不允许删除！
            HashMap grounamesh = cshapegroupMapper.findnamegroupch(cshapegroupid);
            String enamegroupsh = null;
            if (grounamesh != null && grounamesh.size() > 0) {
                enamegroupsh = TypeUtils.castToString(grounamesh.get("ENAME"));
            }
            throw new NDSException(enamegroupsh + Resources.getMessage("已有商品记录，不允许删除！", querySession.getLocale()));

        }

        List<HashMap> list = cshapeMapper.findshapeId(cshapegroupid);
        if (list != null && list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                HashMap shapet = list.get(i);
                Long shapeid = TypeUtils.castToLong(shapet.get("ID"));
                String Enameshape = TypeUtils.castToString(shapet.get("ENAME"));
                if (shapeid != null) {
                    // 调用方法删除明细 -- Yvan
                    JSONObject error = new JSONObject();
                    if (!deleteShape(shapeid, error, cshapegroupid)) {
                        JSONArray errorArray = error.getJSONArray("data");
                        JSONObject errorData = errorArray.getJSONObject(0);
                        throw new NDSException(Enameshape + "删除失败！错误原因：" + errorData.getString("message"));
                    }
                }
            }
        }
        // TODO 删除主表
        Integer groupcount = cshapegroupMapper.deletecshapegroup(cshapegroupid);
        if (groupcount > 0) {

            vh.put("code", 0);
//        号型组删除成功
            vh.put("message", Resources.getMessage("删除成功的记录数:", querySession.getLocale()) + groupcount);
            return vh;
        } else {
            throw new NDSException(Resources.getMessage("当前记录已不存在！", querySession.getLocale()));
        }
    }


    /**
     * 删除号型组明细 -- Yvan
     *
     * @param id    主键ID
     * @param error 错误收集对象
     */
    private boolean deleteShape(Long id, JSONObject error, Long groupId) {
        CshapeMapper mapper = ApplicationContextHandle.getBean(CshapeMapper.class);
        if (checkQueto(error, id, mapper, groupId)) {
            int res = mapper.deletecshape(id);
            if (res > 0) {
                return true;
            }
            ValueHolderUtils.addFailData(error, "删除失败！", id);
        }
        return false;
    }

    /**
     * 如所删除记录对应的【号型组】+【尺寸】已在【条码】表（条码表PS_C_SKU中本身没有【号型组】字段，需要到对应的商品档案中获取）中存在，则不允许删除 -- Yvan
     *
     * @param error  错误收集对象
     * @param id     主键ID
     * @param mapper 映射对象
     */
    private boolean checkQueto(JSONObject error, Long id, CshapeMapper mapper, Long groupId) {
        if (id == null) {
            ValueHolderUtils.addFailData(error, "请先选择需要删除的明细记录！", id);
            return false;
        }
        // 获取号型对象
        HashMap<String, Object> shape = mapper.getSizeIdAndNameById(id);
        if (shape == null) {
            ValueHolderUtils.addFailData(error, "当前记录已不存在！", id);
            return false;
        }
        // 排除错误数据
        if (shape.get("PS_C_SIZE_ID") == null || shape.get("ENAME") == null) {
            // 这里理论上调不到，调到了说明数据有问题，缺少号型关联的尺寸
            ValueHolderUtils.addFailData(error, "数据库数据错误！", id);
            return true;
        }
        Long sizeId = (Long) shape.get("PS_C_SIZE_ID");
        String shapeName = shape.get("ENAME").toString();
        // 判断SKU条形码下是否存在引用该号型
        CskuMapper cskuMapper = ApplicationContextHandle.getBean(CskuMapper.class);
        int count = cskuMapper.getCountBySpec2ObjId(sizeId, groupId);
        if (count < 1) {
            return true;
        }
        CspecLoadMapper cspecLoadMapper = ApplicationContextHandle.getBean(CspecLoadMapper.class);
        String specObjName = cspecLoadMapper.getNameById(sizeId);
        ValueHolderUtils.addFailData(error, "尺寸【" + shapeName + "】" + specObjName + "已定义条码，不允许删除！", id);
        return false;
    }

}
