package com.jackrain.nea.ps.mapper;

import com.alibaba.fastjson.JSONObject;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.jdbc.SQL;

import java.util.List;

@Mapper
public interface CproDimQueryMapper {


    @Select("SELECT b.ECODE AS SEXCODE,CONCAT(\"[\",b.ECODE,\"]\",b.ENAME) SEX,CONCAT(\"[\",c.ECODE,\"]\",c.ENAME) LARGECLASS,CONCAT(\"[\",d.ECODE,\"]\",d.ENAME) SEASON,a.ID FROM ps_c_pro a\n" +
            "LEFT JOIN ps_c_prodim_item b ON a.SEX=b.ID\n" +
            "LEFT JOIN ps_c_prodim_item c ON a.LARGECLASS=c.ID\n" +
            "LEFT JOIN ps_c_prodim_item d ON a.PROSEA=d.ID\n" +
            "WHERE a.ecode=#{ecode} AND a.CP_C_DISTRIB_ID=0")
    JSONObject cproDim(@Param("ecode") String ecode);

    @SelectProvider(type = CproDimQuerySql.class,method = "cproDims")
    List<JSONObject> cproDims(String ecodes);


    @SelectProvider(type = CproDimQuerySql.class,method = "cproDimsBytribid")
    List<JSONObject> cproDimsBytribid(Long distribId);

    class CproDimQuerySql{
        public String cproDims(String ecodes){
            return new SQL() {
                {
                    SELECT("a.ECODE,b.ECODE AS SEXCODE,CONCAT(\"[\",b.ECODE,\"]\",b.ENAME) SEX,CONCAT(\"[\",c.ECODE,\"]\",c.ENAME)" +
                            " LARGECLASS,CONCAT(\"[\",d.ECODE,\"]\",d.ENAME) SEASON,a.ID");
                    FROM("ps_c_pro a");
                    LEFT_OUTER_JOIN("ps_c_prodim_item b ON a.SEX=b.ID");
                    LEFT_OUTER_JOIN("ps_c_prodim_item c ON a.LARGECLASS=c.ID");
                    LEFT_OUTER_JOIN("ps_c_prodim_item d ON a.PROSEA=d.ID");
                    WHERE("a.ecode in (" + ecodes + ") AND a.CP_C_DISTRIB_ID=0");
                }
            }.toString();
        }

        public String cproDimsBytribid(Long distribId){
            return new SQL() {
                {
                    SELECT("a.ECODE,b.ECODE AS SEXCODE,CONCAT(\"[\",b.ECODE,\"]\",b.ENAME) SEX,CONCAT(\"[\",c.ECODE,\"]\",c.ENAME) LARGECLASS,CONCAT(\"[\",d.ECODE,\"]\",d.ENAME) SEASON,a.ID");
                    FROM("ps_c_pro a");
                    LEFT_OUTER_JOIN("ps_c_prodim_item b ON a.SEX=b.ID");
                    LEFT_OUTER_JOIN("ps_c_prodim_item c ON a.LARGECLASS=c.ID");
                    LEFT_OUTER_JOIN("ps_c_prodim_item d ON a.PROSEA=d.ID");
                    WHERE("a.CP_C_DISTRIB_ID="+distribId);
                }
            }.toString();
        }
    }
}
