package com.jackrain.nea.ps.mapper;

import com.alibaba.fastjson.JSONObject;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.jdbc.SQL;

import java.util.HashMap;
import java.util.List;
import java.util.Set;

@Mapper
public interface CprospecUpdateMapper {
    class CskuDisProvider {
        public String update(JSONObject jsonObject, Long proid, JSONObject wherejo) {
            StringBuffer sql = new StringBuffer();
            Set<String> keySet = jsonObject.keySet();
            sql.append("UPDATE PS_C_SKU SET ");
            for (int i = 0; i < jsonObject.size(); i++) {
                String key = (String) keySet.toArray()[i];
                if (i == jsonObject.size() - 1) {
                    if (jsonObject.get(key) != null) {
                        sql.append(key + "= '" + jsonObject.get(key) + "'");
                    } else {
                        sql.append(key + "= " + jsonObject.get(key));
                    }
                } else {
                    if (jsonObject.get(key) != null) {
                        sql.append(key + "= '" + jsonObject.get(key) + "',");
                    } else {
                        sql.append(key + "= " + jsonObject.get(key) + ",");
                    }
                }
            }
            where(proid, wherejo, sql);
            return sql.toString();
        }

        private void where(Long proid, JSONObject wherejo, StringBuffer sql) {
            sql.append(" WHERE PS_C_PRO_ID=" + proid);
            Set<String> whereSet = wherejo.keySet();
            for (int j = 0; j < wherejo.size(); j++) {
                String key = (String) whereSet.toArray()[j];
                sql.append(" AND " + key + "=" + wherejo.get(key));
            }
        }

        public String selectIds(Long proid, JSONObject wherejo) {
            StringBuffer sql = new StringBuffer();
            sql.append("SELECT ID FROM PS_C_SKU ");
            where(proid, wherejo, sql);
            return sql.toString();
        }

        public String updateall(JSONObject jsonObject) {
            return new SQL() {
                {
                    Set<String> keySet = jsonObject.keySet();
                    UPDATE("PS_C_SKU");
                    for (int i = 0; i < jsonObject.size(); i++) {
                        String key = (String) keySet.toArray()[i];
                        if ("ID".equals(key)) {
                            WHERE("ID=#{" + key + "}");
                        } else {
                            SET(key + "= #{" + key + "}");
                        }
                    }
                }
            }.toString();
        }

        public String select(Long ps_c_pro_id) {
            return new SQL() {
                {
                    SELECT("ID");
                    FROM("PS_C_SKU");
                    WHERE("PS_C_PRO_ID=#{arg0}");
                }
            }.toString();
        }
    }

    //判断传入的标准商品档案ID是否存在
    @Select("SELECT COUNT(1) FROM PS_C_PRO WHERE ID=#{id}")
    int selectProCount(@Param("id") Long id);

    @SelectProvider(type = CskuDisProvider.class, method = "select")
    List<HashMap> selectSkuDisId(Long ps_c_pro_id);

    @Select("SELECT ID FROM PS_C_SKU WHERE PS_C_PRO_ID=#{ps_c_pro_id} AND PS_C_SPECOBJ_IDS IS NULL")
    Long querySkuDisId(@Param("ps_c_pro_id") Long ps_c_pro_id);

    @UpdateProvider(type = CskuDisProvider.class, method = "update")
    int updateDisSku(JSONObject jsonObject, Long proid, JSONObject wherejo);

    @SelectProvider(type = CskuDisProvider.class, method = "selectIds")
    List<Long> selectIds(Long proid, JSONObject wherejo);

    @UpdateProvider(type = CskuDisProvider.class, method = "updateall")
    int updateDisSkuAll(JSONObject jsonObject);

    @Select("SELECT CP_C_DISTRIB_ID FROM PS_C_SKU WHERE PS_C_PRO_ID=#{id}")
    List<HashMap> seletDisid(@Param("id") Long id);
}
