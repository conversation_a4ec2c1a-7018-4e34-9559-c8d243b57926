package com.jackrain.nea.ps.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.data.basic.common.BasicConstants;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.CskudistribUpdateCmd;
import com.jackrain.nea.ps.mapper.CskuMapper;
import com.jackrain.nea.ps.mapper.CskudistribMapper;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

/**
 * 配销中心商品档案-条码维护-更新
 *
 * <AUTHOR>
 * @create 2017/10/12
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps")
public class CskudistribUpdateCmdImpl extends CommandAdapter implements CskudistribUpdateCmd {

    @Autowired
    private CskuMapper cskuMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        // Mapper
        CskudistribMapper cskudistribMapper = ApplicationContextHandle.getBean(CskudistribMapper.class);
        // 当前系统时间
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        // 获取主表json
        JSONObject jo = fixColumn.getJSONObject("PS_C_SKUDISTRIB");

        // 获取objid 并更新
        Long objid = param.getLong("objid");
        if (objid > 0) {
            // 更新
            updateTable(jo, querySession, cskudistribMapper, timestamp, objid);
        }

        vh.put("code", 0);
        vh.put("message", Resources.getMessage("成功", querySession.getLocale()));
        return vh;
    }

    /**
     * 更新
     *
     * @param jo
     * @param querySession
     * @param cskudistribMapper
     * @param timestamp
     * @param objid
     */
    private void updateTable(JSONObject jo, QuerySession querySession, CskudistribMapper cskudistribMapper, Timestamp timestamp, Long objid) {
        // 添加修改人
        jo.put("MODIFIERID", querySession.getUser().getId().longValue());
        jo.put("MODIFIERNAME", querySession.getUser().getName());
        jo.put("MODIFIERENAME", querySession.getUser().getEname());
        // 添加修改时间
        jo.put("MODIFIEDDATE", timestamp);
        // 插入id
        jo.put("ID", objid);
        JSONObject csku = cskuMapper.selectSku(objid);
        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
        // 执行mapper更新
        int count = cskudistribMapper.updateCskudistrib(jo);
        List<String> keys = new ArrayList<>();
        //执行redis清除
        if (csku != null) {
            String key = BasicConstants.CP_SKU_ID_KEY + objid;
            String ecodeKey = BasicConstants.CP_SKU_ECODE_KEY + csku.get("ECODE");
            String proKey = BasicConstants.CP_PRO_ID_KEY + csku.get("PS_C_PRO_ID");
            String proEcodeKey = BasicConstants.CP_PRO_ECODE_KEY + csku.get("PS_C_PRO_ECODE");
            keys.add(key);
            keys.add(ecodeKey);
            keys.add(proKey);
            keys.add(proEcodeKey);
        }
        redisTemplate.delete(keys);

        if (count == 0) {
            // TODO 更新条数为0时 错误 提示“当前记录已不存在！”
            throw new NDSException(Resources.getMessage("当前记录已不存在！", querySession.getLocale()));
        }
    }
}
