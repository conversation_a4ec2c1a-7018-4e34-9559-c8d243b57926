package com.jackrain.nea.ps.mapper;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.web.utils.ArrayToSUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.jdbc.SQL;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * @author: zhu lin yu
 * @since: 2019/4/10
 * create at : 2019/4/10 11:47
 */
public interface CskuIsActiveMapper {
    class CskuIsActiveSelect {
        public String updateSql(Map map) {
            String sql = new SQL() {
                String tableName = (String) map.get("tableName");
                JSONObject setKeys = (JSONObject) map.get("setKeys");
                JSONObject whereKeys = (JSONObject) map.get("whereKeys");
                boolean isIn = whereKeys.getBoolean("is_in") != null;

                {
                    UPDATE(tableName);
                    for (String key : setKeys.keySet()) {
                        if ("id".equalsIgnoreCase(key)) {
                            continue;
                        }
                        SET(key + "= #{setKeys." + key + "}");
                    }
                    if (isIn) {
                        Set<String> keySet = whereKeys.keySet();
                        for (String key : keySet) {
                            if (!Objects.equals(key, "is_in")) {
                                JSONArray retailIds = whereKeys.getJSONArray(key);
                                WHERE(key + " in (" + ArrayToSUtil.join(retailIds.toArray(), ",") + ")");
                            }
                        }
                    } else {
                        for (String key : whereKeys.keySet()) {
                            if (whereKeys.getString(key) == null) {
                                continue;
                            }
                            WHERE(key + "= #{whereKeys." + key + "}");
                        }
                    }
                }
            }.toString();
            return sql;
        }

        public String getEcodeByIds(Map map) {
            String sql = new SQL() {
                JSONArray ids = (JSONArray) map.get("ids");
                String tableName = (String) map.get("tableName");

                {
                    SELECT("ID,ECODE,ISACTIVE,GBCODE,FORCODE");
                    FROM(tableName);
                    WHERE("ID in (" + StringUtils.join(ids.toArray(), ',') + ")");
                }
            }.toString();
            return sql;
        }
    }

    /**
     * 根据IDS查询ID、ECODE、ISACTIVE
     *
     * @param ids       ID集合
     * @param tableName 表名
     * @return String
     */
    @SelectProvider(type = CskuIsActiveMapper.CskuIsActiveSelect.class,
            method = "getEcodeByIds")
    List<HashMap> getEcodeByIds(@Param("ids") JSONArray ids, @Param("tableName") String tableName);

    /**
     * 更新
     *
     * @param tableName 表名
     * @param setKeys   更新参数
     * @param whereKeys 条件参数
     */
    @UpdateProvider(type = CskuIsActiveSelect.class,
            method = "updateSql")
    int updateTable(@Param("tableName") String tableName, @Param("setKeys") JSONObject setKeys, @Param("whereKeys") JSONObject whereKeys);

}
