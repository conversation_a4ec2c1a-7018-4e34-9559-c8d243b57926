package com.jackrain.nea.ps.mapper;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.web.utils.ArrayToSUtil;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.jdbc.SQL;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2018-07-24 19:11
 * @remark 查询当前零售价商品条码关联查询
 */
@Mapper
public interface DlPriceQuerySqlMapper {
    class DlPriceQuery{
        public String selectSql(Map<String, Object> para){
            String tableName = (String) para.get("tableName");
            String[] field = (String[])para.get("field");
            JSONArray whereKeys = (JSONArray)para.get("whereKeys");
            String sql = new SQL() {
                {
                    SELECT(field);
                    FROM(tableName);
                    for (int i = 0; i < whereKeys.size(); i++) {
                        JSONObject whereKey = whereKeys.getJSONObject(i);
                        String columnName = whereKey.getString("column_name");
                        String columnValue = whereKey.getString("column_value");
                        boolean isIn = whereKey.getBoolean("is_in") != null ? whereKey.getBoolean("is_in") : false;
                        if (isIn){
                            WHERE(columnName + " in (" + ArrayToSUtil.join((JSONArray.parseArray(columnValue)).toArray(), ",") + ")");
                        }else{
                            WHERE(columnName + "='" + columnValue + "'");
                        }
                    }
                }
            }.toString();

            if (!sql.toLowerCase().contains("where")) {
                return "";
            }
            return sql;
        }

        public String selectProSkuSql(Map<String, Object> para){
            // 查询字段
            String field = (String) para.get("field");
            // 配销中心ID集合
            JSONArray distribIds = (JSONArray) para.get("distribIds");
            // 是否需要商品ID集合
            boolean isNeedPro = (boolean) para.get("isNeedPro");
            // 是否为查询总计
            boolean isCount = (boolean) para.get("isCount");
            // 当前开始条数
            Integer startindex = (Integer) para.get("startindex");
            // 当前每页显示数
            Integer range = (Integer) para.get("range");
            StringBuilder sql = new StringBuilder(" SELECT ");
            sql.append(field);
            sql.append(" FROM PS_C_PRO p INNER JOIN PS_C_SKU s ON s.PS_C_PRO_ID = p.ID ");
            sql.append(" WHERE p.CP_C_DISTRIB_ID IN (").append(ArrayToSUtil.join((distribIds).toArray(), ",")).append(")");
            sql.append(" AND p.ISACTIVE = 'Y' AND s.ISACTIVE = 'Y'");
            if (isNeedPro){
                JSONArray proIds = (JSONArray) para.get("proIds");
                sql.append(" AND p.ID IN (").append(ArrayToSUtil.join((proIds).toArray(), ",")).append(")");
            }
            if (!isCount) {
                sql.append(" ORDER BY s.ID LIMIT ").append(startindex).append(",").append(range);
            }
            if (!sql.toString().toLowerCase().contains("where")) {
                return "";
            }
            return sql.toString();
        }
    }

    
    @SelectProvider(type = DlPriceQuery.class, method = "selectSql")
    List<HashMap> query(@Param("tableName")String tableName, @Param("field")String[] field, @Param("whereKeys")JSONArray whereKeys);

    @SelectProvider(type = DlPriceQuery.class,
            method = "selectProSkuSql")
    List<HashMap> proSkuQuery(@Param("field")String field,@Param("distribIds")JSONArray distribIds,@Param("isNeedPro")boolean isNeedPro,@Param("proIds")JSONArray proIds,@Param("startindex")Integer startindex,@Param("range")Integer range,@Param("isCount")boolean isCount);
}
