package com.jackrain.nea.ps.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.core.db.Tools;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.CbrandSaveCmd;
import com.jackrain.nea.ps.entity.CbrandEntity;
import com.jackrain.nea.ps.mapper.CbrandMapper;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;

/**
 * <AUTHOR> zhang
 * @date 2017/9/24
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps")
public class CbrandSaveCmdImpl extends CommandAdapter implements CbrandSaveCmd {

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        CbrandMapper cbrandMapper = ApplicationContextHandle.getBean(CbrandMapper.class);
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        if (fixColumn.containsKey("PS_C_BRAND")) {
            JSONObject jo = fixColumn.getJSONObject("PS_C_BRAND");
            //主表不为空
            if (jo != null) {
                String ename = jo.getString("ENAME");
                String ecode = jo.getString("ECODE");
                //实例化
                Timestamp timestamp = new Timestamp(System.currentTimeMillis());
                long oneId = Integer.valueOf(querySession.getUser().getId()).longValue();
                long adClientId = Integer.valueOf(querySession.getUser().getClientId()).longValue();
                long adOrgId = Integer.valueOf(querySession.getUser().getOrgId()).longValue();
                String mEname = String.valueOf(querySession.getUser().getEname());
                String mName = String.valueOf(querySession.getUser().getName());

                //判断增加或修改操作
                if (param.getLong("objid") != null && param.getLong("objid") > 0) {
                    //更新操作
                    //判断当前修改的记录是否存在
                    long id = param.getLong("objid");
                    CbrandEntity cbrandEntity = cbrandMapper.findById(id);
                    if (cbrandEntity == null) {
                        throw new NDSException(Resources.getMessage("当前记录已不存在！", querySession.getLocale()));
                    }
                    //判断当前要修改的字段是否存在
                    judgeUpdate(cbrandMapper, id, ename, ecode, querySession);
                    jo.put("ID", id);
                    jo.put("MODIFIEDDATE", timestamp);
                    jo.put("MODIFIERID", oneId);
                    jo.put("MODIFIERNAME", mName);
                    jo.put("MODIFIERENAME", mEname);
                    //执行更新操作
                    cbrandMapper.update(jo);
                } else {
                    //增加操作
                    //判断是否重复
                    judgeAdd(cbrandMapper, ename, ecode, querySession);
                    long id = Tools.getSequence("PS_C_BRAND");

                    jo.put("ID", id);
                    jo.put("PS_C_BRAND_ID", id);
                    jo.put("AD_CLIENT_ID", adClientId);
                    jo.put("AD_ORG_ID", adOrgId);
                    jo.put("OWNERID", oneId);
                    jo.put("MODIFIERID", oneId);
                    jo.put("CREATIONDATE", timestamp);
                    jo.put("MODIFIEDDATE", timestamp);
                    jo.put("MODIFIERNAME", mName);
                    jo.put("OWNERNAME", mName);
                    jo.put("MODIFIERENAME", mEname);
                    jo.put("OWNERENAME", mEname);
                    cbrandMapper.insert(jo);

                    //增加成功返回信息增加
                    JSONObject jsonObj = new JSONObject();
                    jsonObj.put("tablename", "PS_C_BRAND");
                    jsonObj.put("objid", id);
                    vh.put("data", jsonObj);
                }
                vh.put("code", 0);
                vh.put("message", Resources.getMessage("Success", querySession.getLocale()));
                return vh;
            }
            vh.put("code", 0);
            vh.put("message", Resources.getMessage("Success", querySession.getLocale()));
            return vh;
        }

        vh.put("code", -1);
        vh.put("message", Resources.getMessage("Failure", querySession.getLocale()));
        return vh;
    }

    public void judgeAdd(CbrandMapper cbrandMapper, String ename, String ecode, QuerySession querySession) {
        int recordEname = cbrandMapper.selectEname(ename);
        int recordEcode = cbrandMapper.selectEcode(ecode);
        if (recordEcode > 0) {
            throw new NDSException(Resources.getMessage("输入的数据已存在：编码", querySession.getLocale()));
        }
        if (recordEname > 0) {
            throw new NDSException(Resources.getMessage("输入的数据已存在：名称", querySession.getLocale()));
        }
    }

    public void judgeUpdate(CbrandMapper cbrandMapper, Long id, String ename, String ecode, QuerySession querySession) {
        if (!StringUtils.isEmpty(ename)) {
            Long recordEname = cbrandMapper.selectEnameById(ename);
            if (recordEname != null && !recordEname.equals(id)) {
                throw new NDSException(Resources.getMessage("输入的数据已存在：名称", querySession.getLocale()));
            }
        }

        if (!StringUtils.isEmpty(ecode)) {
            Long recordEcode = cbrandMapper.selectEcodeById(ecode);
            if (recordEcode != null && !recordEcode.equals(id)) {
                throw new NDSException(Resources.getMessage("输入的数据已存在：编码", querySession.getLocale()));
            }
        }
    }

}
