package com.jackrain.nea.ps.service;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.model.constant.OcCommonConstant;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.ps.Constant.PsCommonConstant;
import com.jackrain.nea.ps.api.PsCTaobaoNewsCouponSaveCmd;
import com.jackrain.nea.ps.api.table.PsCTaobaoNewsCoupon;
import com.jackrain.nea.ps.mapper.PsCTaobaoNewsCouponMapper;
import com.jackrain.nea.ps.util.AssertUtil;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utils.ValueHolderUtils;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import utils.AssertUtils;
import utils.CommandAdapterUtil;

import java.util.Date;
import java.util.HashMap;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2022/3/17 14:59
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps")
public class PsCTaobaoNewsCouponSaveCmdImpl extends CommandAdapter implements PsCTaobaoNewsCouponSaveCmd {

    @Autowired
    private PsCTaobaoNewsCouponMapper psCTaobaoNewsCouponMapper;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {

        User user = session.getUser();
        ValueHolder valueHolder = CommandAdapterUtil.checkSaveSession(session, PsCommonConstant.PS_C_TAOBAO_NEWS);

        if (!valueHolder.isOK()) {
            return valueHolder;
        }

        if (log.isDebugEnabled()) {
            log.debug("Start Sava psCTaobaoNewsCoupon valueHolder#{}", valueHolder.getData());
        }

        JSONObject fixColumn = (JSONObject) valueHolder.getData().get(OcCommonConstant.FIX_COLUMN);

        Long id = (Long) ((HashMap) valueHolder.getData().get("data")).get(OcCommonConstant.OBJ_ID);

        if (fixColumn != null && id != null) {
            PsCTaobaoNewsCoupon psCTaobaoNewsCoupon = ((JSONObject) fixColumn.get(PsCommonConstant.PS_C_TAOBAO_NEWS_COUPON)).toJavaObject(PsCTaobaoNewsCoupon.class);
            // 判断请求参数
            AssertUtil.assertException(Objects.isNull(psCTaobaoNewsCoupon), "请求参数异常");

            if (log.isDebugEnabled()) {
                log.debug("Start Sava  psCTaobaoNewsCoupon#{}", psCTaobaoNewsCoupon);
            }
            // 判断新增还是修改
            if (id < 0) {
                // 新增方法
                // 入参校验
                checkParam(psCTaobaoNewsCoupon);
                // 取id值
                id = ModelUtil.getSequence(PsCommonConstant.PS_C_TAOBAO_NEWS_COUPON);
                psCTaobaoNewsCoupon.setId(id);
                // 系统参数
                psCTaobaoNewsCoupon.setOwnername(user.getName());
                psCTaobaoNewsCoupon.setAdOrgId((long) user.getOrgId());
                psCTaobaoNewsCoupon.setAdClientId((long) user.getClientId());
                psCTaobaoNewsCoupon.setOwnerid(Long.valueOf(user.getId()));
                psCTaobaoNewsCoupon.setCreationdate(new Date());
                psCTaobaoNewsCoupon.setModifierid(Long.valueOf(user.getId()));
                psCTaobaoNewsCoupon.setModifiername(user.getName());
                psCTaobaoNewsCoupon.setModifieddate(new Date());
                // 是否启用
                psCTaobaoNewsCoupon.setIsactive(OcBOrderConst.IS_ACTIVE_YES);
                int insert = psCTaobaoNewsCouponMapper.insert(psCTaobaoNewsCoupon);

                if (insert <= 0) {
                    throw new NDSException("新增打标申请数据失败！");
                }
            } else {
                // 修改
                PsCTaobaoNewsCoupon updatePsCTaobaoNewsCoupon = psCTaobaoNewsCouponMapper.selectById(id);
                AssertUtil.assertException(Objects.isNull(updatePsCTaobaoNewsCoupon), "当前记录已不存在");
                if("1".equals(updatePsCTaobaoNewsCoupon.getCouponUploadStatus())){
                    AssertUtils.logAndThrow("当前优惠券打标已上传，不允许修改！", session.getLocale());
                }
                psCTaobaoNewsCoupon.setId(id);
                psCTaobaoNewsCoupon.setModifieddate(new Date());
                psCTaobaoNewsCoupon.setModifierid(Long.valueOf(user.getId()));
                psCTaobaoNewsCoupon.setModifiername(user.getName());
                int update = psCTaobaoNewsCouponMapper.updateById(psCTaobaoNewsCoupon);
                if (update <= 0) {
                    throw new NDSException("更新打标申请数据失败！");
                }
            }
        }
        valueHolder = ValueHolderUtils.getSuccessValueHolder(id, PsCommonConstant.PS_C_TAOBAO_NEWS_COUPON, "保存打标申请数据成功");
        return valueHolder;
    }

    private void checkParam(PsCTaobaoNewsCoupon psCTaobaoNewsCoupon) {

        if (Objects.isNull(psCTaobaoNewsCoupon.getCpCShopId())) {
            throw new NDSException("店铺不允许为空");
        }
        if (Objects.isNull(psCTaobaoNewsCoupon.getItemId())) {
            throw new NDSException("天猫商品id不允许为空！！");
        }
        if (Objects.isNull(psCTaobaoNewsCoupon.getBarndId())) {
            throw new NDSException("天猫品牌id不允许为空！！");
        }
        if (Objects.isNull(psCTaobaoNewsCoupon.getProtectionPeriod())) {
            throw new NDSException("店铺优惠券新品保护期档次不允许为空！！");
        }
    }
}
