package com.jackrain.nea.ps.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.data.basic.model.request.SkuInfoQueryRequest;
import com.jackrain.nea.data.basic.services.BasicPsQueryService;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.CSkuListQueryCmd;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR> lin yu
 * @since 2019/9/9
 * create at : 2019/9/9 20:48
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps")
public class CSkuListQueryCmdImpl extends CommandAdapter implements CSkuListQueryCmd {
    @Autowired
    private BasicPsQueryService basicPsQueryService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {

        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
        JSONArray skuEcodes = param.getJSONArray("SkuEcodeList");
        Long shopId = param.getLong("shopId");
        String isQuery = param.getString("isQuery");
        if (CollectionUtils.isEmpty(skuEcodes)) {
            throw new NDSException(Resources.getMessage("条码编码不能为空", session.getLocale()));
        }

        List<String> skuEcodeList = JSON.parseArray(skuEcodes.toJSONString(), String.class);
        HashMap<String, PsCProSkuResult> skuInfoByEcode = new HashMap<>();
        SkuInfoQueryRequest skuInfoQueryRequest = new SkuInfoQueryRequest();
        skuInfoQueryRequest.setSkuEcodeList(skuEcodeList);
        skuInfoQueryRequest.setShopId(shopId);
        skuInfoQueryRequest.setIsQuery(isQuery);
        try {
            skuInfoByEcode = basicPsQueryService.getSkuInfoByEcode(skuInfoQueryRequest);
        } catch (Exception e) {
            e.printStackTrace();
        }

        List<PsCProSkuResult> psCProSkuResults = new ArrayList<>();
        for (String skuEcode : skuEcodeList) {
            PsCProSkuResult psCProSkuResult = skuInfoByEcode.get(skuEcode.toUpperCase());
            psCProSkuResults.add(psCProSkuResult);
        }

        vh.put("code", 0);
        vh.put("data", psCProSkuResults);
        return vh;
    }

}
