package com.jackrain.nea.ps.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.CproIsActiveYCmd;
import com.jackrain.nea.ps.mapper.CproIsActiveMapper;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.RuntimeCompute;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-06-01 16:48
 * @remark 标准商品启用
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps")
public class CproIsActiveYCmdImpl extends CommandAdapter implements CproIsActiveYCmd {

    @Override
    public ValueHolder execute(QuerySession querySession) {
        RuntimeCompute runtimeCompute = ApplicationContextHandle.getBean("runtimeCompute");
        runtimeCompute.startRuntime();
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        log.debug(LogUtil.format("CproIsActiveCmdImpl.param：") + param.toJSONString());
        CproIsActiveMapper mapper = ApplicationContextHandle.getBean(CproIsActiveMapper.class);
        // 标准商品ID
        String objid = param.getString("objid");
        JSONObject eCodes = new JSONObject();
        // 批量
        if (null == objid) {
            JSONArray ids = param.getJSONArray("ids");
            // 更新状态
            JSONObject resultJson = update(mapper, querySession, ids);
            eCodes.putAll(resultJson.getJSONObject("proIdEcodes"));
            JSONArray errorInfo = resultJson.getJSONArray("errorInfo");
            JSONArray proEcodes = resultJson.getJSONArray("proEcodes");
            // 成功记录数
            int successCount = proEcodes.size();
            // 失败记录数
            int failCount = ids.size() - successCount;
            if (errorInfo.size() > 0) {
                vh.put("data", errorInfo);
                vh.put("code", -1);
                vh.put("message", "成功" + successCount + "条,失败" + failCount + "条");
            } else {
                vh.put("code", 0);
                vh.put("message", "成功" + successCount + "条");
            }
        } else {
            JSONArray ids = new JSONArray();
            ids.add(objid);
            // 更新状态
            JSONObject resultJson = update(mapper, querySession, ids);
            String eCode = resultJson.getJSONArray("proEcodes").getString(0);
            eCodes.put(objid, eCode);
            JSONArray errorInfo = resultJson.getJSONArray("errorInfo");
            if (errorInfo.size() > 0) {
                vh.put("code", -1);
                vh.put("message", errorInfo.getJSONObject(0).getString("message"));
            } else {
                vh.put("code", 0);
                vh.put("message", eCode + "启用成功");
            }
        }
        vh.put("proecode", eCodes);
        vh.put("ISACTIVE", "Y");
        Double spendTime = runtimeCompute.endRuntime();
        log.debug(LogUtil.format("spentime：") + spendTime);
        return vh;
    }

    // 款号启用
    public JSONObject update(CproIsActiveMapper mapper, QuerySession querySession, JSONArray ids) {
        JSONObject resultJson = new JSONObject();
        JSONArray errorInfo = new JSONArray();
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        // 根据objid查询商品编码
        List<HashMap> proInfo = mapper.getEcodeByIds(ids, "PS_C_PRO");
        if (null == proInfo) {
            throw new NDSException("当前记录已不存在！");
        }
        JSONObject proIdEcodes = new JSONObject();
        JSONArray proEcodes = new JSONArray();
        for (HashMap map : proInfo) {
            if (map.get("ISACTIVE").toString().equals("N")) {
                proIdEcodes.put(map.get("ID").toString(), map.get("ECODE"));
                proEcodes.add(map.get("ECODE"));
            } else {
                JSONObject errorInfoObj = new JSONObject();
                errorInfoObj.put("objid", map.get("ID"));
                errorInfoObj.put("ecode", -1);
                errorInfoObj.put("message", "商品" + map.get("ECODE") + "已启用，不允许启用！");
                errorInfo.add(errorInfoObj);
            }
        }
        if (proEcodes.size() > 0) {
            // 商品启用
            JSONObject setKeys = new JSONObject();
            setKeys.put("ISACTIVE", "Y");
            setKeys.put("MODIFIERID", querySession.getUser().getId());
            setKeys.put("MODIFIERNAME", querySession.getUser().getName());
            setKeys.put("MODIFIERENAME", querySession.getUser().getEname());
            setKeys.put("MODIFIEDDATE", timestamp);
            JSONObject whereKeys = new JSONObject();
            whereKeys.put("ECODE", proEcodes);
            whereKeys.put("is_in", true);
            mapper.updateTable("PS_C_PRO", setKeys, whereKeys);
            // 根据款号查询标准及配销所有商品ID
            List<Long> idsList = mapper.getIdsByEcode(proEcodes);

            JSONArray idsArray = new JSONArray();
            idsArray.addAll(idsList);
            // 启用该商品下所有条码
            setKeys = new JSONObject();
            setKeys.put("ISACTIVE", "Y");
            setKeys.put("MODIFIERID", querySession.getUser().getId());
            setKeys.put("MODIFIERNAME", querySession.getUser().getName());
            setKeys.put("MODIFIERENAME", querySession.getUser().getEname());
            setKeys.put("MODIFIEDDATE", timestamp);
            whereKeys = new JSONObject();
            whereKeys.put("PS_C_PRO_ID", idsArray);
            whereKeys.put("is_in", true);
            mapper.updateTable("PS_C_SKU", setKeys, whereKeys);

        }
        resultJson.put("proEcodes", proEcodes);
        resultJson.put("proIdEcodes", proIdEcodes);
        resultJson.put("errorInfo", errorInfo);
        return resultJson;
    }
}
