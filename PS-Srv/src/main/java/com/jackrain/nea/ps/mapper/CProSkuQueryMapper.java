package com.jackrain.nea.ps.mapper;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.jdbc.SQL;

import java.util.HashMap;
import java.util.List;

/**
 * Created by zhousl on 2018-08-15.
 */
@Mapper
public interface CProSkuQueryMapper {
    public final static String SKU_FIELDS = " ID, ECODE, CP_C_DISTRIB_ID, PS_C_PRO_ID, PS_C_BRAND_ID, PS_C_SPEC1OBJ_ID, PS_C_SPEC2OBJ_ID, ISACTIVE ";

    public final static String PRO_FIELDS = " ID, ECODE, ENAME, PRICELIST, PRICELOWER, TXTDIM3, FABDESC, PROBAND, NUMDIM3, PROYEAR, PROMOTIONTYPE, NUMDIM5, NUMDIM8, PROSEA, PRONATURE, NUMDIM9, LARGECLASS, NUMDIM6, NUMDIM11, PRICEBAND, SERIES, SEX, PRICELIST, PS_C_SPECOBJ_IDS, CP_C_DISTRIB_ID, ISACTIVE";

    /**
     * 根据配销中心和编号查询条码记录
     *
     * @param ecode
     * @param distribID
     * @return
     */
    @Select("SELECT " + SKU_FIELDS + " FROM PS_C_SKU WHERE ECODE = #{ecode} AND CP_C_DISTRIB_ID = #{distribID}")
    List<HashMap> listSkuByEcodeAndDistribID(@Param("ecode") String ecode, @Param("distribID") Long distribID);

    /**
     * 根据配销中心和款号查询商品记录
     *
     * @param eCode
     * @param distribId
     * @return
     */
    @Select("SELECT " + PRO_FIELDS + " FROM PS_C_PRO WHERE ECODE = #{eCode} AND CP_C_DISTRIB_ID = #{distribId}")
    List<HashMap> listProByEcodeAndDistribID(@Param("eCode") String eCode, @Param("distribId") Long distribId);

    @Select("SELECT WARE_TYPE FROM PS_C_SKU WHERE ECODE=#{ecode}")
    Integer getWareTypeByEcode(@Param("ecode") String ecode);

    @Select("SELECT ID FROM PS_C_SKU WHERE ECODE=#{ECODE}")
    Long getSKuIdByEcode(@Param("ecode") String ecode);


    class CSkuProSqlProvider {

        public String selectSkusql(JSONArray array, String distribId) {
            return new SQL() {
                {
                    SELECT(SKU_FIELDS);
                    FROM(" PS_C_SKU ");
                    StringBuffer condtion = new StringBuffer();
                    for (int i = 0; i < array.size(); i++) {
                        JSONObject product = array.getJSONObject(i);
                        String proSku = product.getString("pro_sku");
                        condtion.append("'").append(proSku).append("',");
                    }
                    condtion = condtion.deleteCharAt(condtion.length() - 1);
                    WHERE("ECODE IN (" + condtion.toString() + ")");
                    WHERE("CP_C_DISTRIB_ID = " + distribId);
                }
            }.toString();
        }

        public String selectSpecSql(HashMap param, String mode) {
            JSONArray spec = (JSONArray) param.get("spec");
            if ("spec1".equals(mode)) {
                return new SQL() {
                    {
                        SELECT(" ID, ECODE, ENAME");
                        FROM(" PS_C_SPECOBJ ");
                        StringBuffer condtion = new StringBuffer();
                        for (int i = 0; i < spec.size(); i++) {
                            String id = spec.getString(i);
                            condtion.append(id).append(",");
                        }
                        condtion = condtion.deleteCharAt(condtion.length() - 1);
                        WHERE(" ID IN (" + condtion.toString() + ")");
                        WHERE(" ISACTIVE = 'Y' ");
                        ORDER_BY(" ECODE ASC");
                    }
                }.toString();
            }
            if ("spec2".equals(mode)) {
                return new SQL() {
                    {
                        SELECT(" ID, ECODE, ENAME, MATRIXCOLNO");
                        FROM(" PS_C_SPECOBJ ");
                        StringBuffer condtion = new StringBuffer();
                        for (int i = 0; i < spec.size(); i++) {
                            String id = spec.getString(i);
                            condtion.append(id).append(",");
                        }
                        condtion = condtion.deleteCharAt(condtion.length() - 1);
                        WHERE(" ID IN (" + condtion.toString() + ")");
                        WHERE(" ISACTIVE = 'Y'");
                        ORDER_BY(" MATRIXCOLNO ASC");
                    }
                }.toString();
            }
            return "";
        }

        public String selectProdimSql(HashMap param) {
            JSONArray ids = (JSONArray) param.get("ids");
            return new SQL() {
                {
                    SELECT(" ID, ECODE, ENAME");
                    FROM(" PS_C_PRODIM_ITEM ");
                    String condtion = StringUtils.join(ids.toArray(), ",");
                    WHERE(" ID IN (" + condtion + ")");
                    WHERE(" ISACTIVE = 'Y' ");
                }
            }.toString();
        }
    }

    /**
     * 根据配销中心和多个编号查询条码记录
     */
    @SelectProvider(type = CSkuProSqlProvider.class, method = "selectSkusql")
    List<HashMap> listSkus(JSONArray array, String distribId);

    @Select("SELECT " + PRO_FIELDS + " FROM PS_C_PRO WHERE ID = #{id} AND ISACTIVE = 'Y'")
    HashMap<String, Object> getProById(@Param("id") Long id);

    /**
     * 根据多个id查询商品编码和名称
     *
     * @param param
     * @return
     */
    @SelectProvider(type = CSkuProSqlProvider.class, method = "selectProdimSql")
    List<HashMap> listProdimCodeAndNameByIds(HashMap param);

    /**
     * 查询自身编号和名称
     *
     * @param id
     * @return
     */
    @Select("SELECT ECODE, ENAME FROM CP_C_STOREORG WHERE ID = #{id} AND ISACTIVE = 'Y'")
    HashMap getStoreCodeAndNameById(@Param("id") Long id);

    /**
     * 根据id查询品牌编码和名称
     *
     * @param id
     * @return
     */
    @Select("SELECT ENAME, ECODE FROM PS_C_BRAND WHERE ID = #{id} AND ISACTIVE = 'Y'")
    HashMap getBrandCodeAndNameById(@Param("id") Long id);

    /**
     * 根据id查询编码和名称 --规格实例
     *
     * @param id
     * @return
     */
    @Select("SELECT ECODE,ENAME FROM PS_C_SPECOBJ WHERE ID = #{id} AND ISACTIVE = 'Y'")
    HashMap<String, Object> getSpecCodeAndNameById(@Param("id") Long id);

    /**
     * 根据多个id查询的编码和名称 --规格实例
     *
     * @param param
     * @return
     */
    @SelectProvider(type = CSkuProSqlProvider.class, method = "selectSpecSql")
    List<HashMap> listSpecsByids(HashMap param, String mode);

    /**
     * 根据款号和配销中心查询颜色、尺寸信息
     *
     * @param id
     * @return
     */
    @Select("SELECT ID,ECODE,PS_C_SPEC1OBJ_ID,PS_C_SPEC2OBJ_ID FROM PS_C_SKU WHERE PS_C_PRO_ID = #{id} and CP_C_DISTRIB_ID = #{distribId} AND ISACTIVE = 'Y'")
    List<HashMap> listSkuByProIdAnddistribId(@Param("id") Long id, @Param("distribId") String distribId);

    /**
     * 根据颜色ID查询ps_c_specobj表信息
     *
     * @param clrId
     * @return
     */
    @Select("SELECT * FROM PS_C_SPECOBJ WHERE ID = #{clrId} AND ISACTIVE = 'Y'")
    HashMap selectPsCSpecobjByClrId(@Param("clrId") Long clrId);
}
