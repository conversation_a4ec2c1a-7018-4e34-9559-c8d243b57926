package com.jackrain.nea.ps.mapper;
/**
 * create by tzp 2017/9/28
 */

import com.alibaba.fastjson.JSONObject;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.jdbc.SQL;

import java.util.Set;

@Mapper
public interface CgbruleMapper {

    class CgbruleProvider {
        public String update(final JSONObject jsonObject) {
            return new SQL() {
                {
                    Set<String> keySet = jsonObject.keySet();
                    UPDATE("PS_C_GBRULE");
                    for (int i = 0; i < jsonObject.size(); i++) {
                        String key = (String) keySet.toArray()[i];
                        if ("ID".equals(key)) {
                            WHERE("ID=#{" + key + "}");
                        } else {
                            SET(key + "= #{" + key + "}");
                        }
                    }
                }
            }.toString();
        }

        public String insert(final JSONObject jsonObject) {
            return new SQL() {
                {
                    Set<String> keySet = jsonObject.keySet();
                    INSERT_INTO("PS_C_GBRULE");
                    for (int i = 0; i < jsonObject.size(); i++) {
                        String key = (String) keySet.toArray()[i];
                        VALUES(key, "#{" + key + "}");
                    }
                }
            }.toString();
        }
    }

    //判断码段是否存在
    @Select("SELECT COUNT(1) FROM PS_C_GBRULE WHERE CODESEG=#{codeSeg} AND ID!=#{id}")
    int selectCodeSeg(@Param("codeSeg") String codeSeg, @Param("id") Long id);

    @Select("SELECT COUNT(1) FROM PS_C_GBRULE WHERE CODESEG=#{codeSeg}")
    int selectinsertCodeSeg(@Param("codeSeg") String codeSeg);

    @Select("SELECT CODESEG FROM PS_C_GBRULE WHERE ID=#{id}")
    String selCodeSeg(@Param("id") Long id);

    //新增国标码
    @InsertProvider(type = CgbruleProvider.class, method = "insert")
    int insertGbrule(JSONObject jsonObject);

    //判断该id的国标码状态
    @Select("SELECT STATUS FROM PS_C_GBRULE WHERE ID=#{id}")
    int selectStatus(@Param("id") Long id);

    //判断当前国标码是否可用
    @Select("SELECT ISACTIVE FROM PS_C_GBRULE WHERE ID=#{id}")
    String selectIsActive(@Param("id") Long id);

    //更新国标码规则表
    @UpdateProvider(type = CgbruleProvider.class, method = "update")
    int updateGbrule(JSONObject jsonObject);

    //获取编号
    @Select("SELECT DOCNO FROM PS_C_GBRULE WHERE ID=#{id}")
    String selectDocno(@Param("id") Long id);

    @Update("UPDATE PS_C_GBRULE SET DOCNO=#{sequence} WHERE id=#{id}")
    void updateSequence(@Param("sequence") String sequence, @Param("id") Long id);


}
