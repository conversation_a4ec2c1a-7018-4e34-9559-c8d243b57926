package com.jackrain.nea.ps.mapper;

import com.alibaba.fastjson.JSONObject;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.HashMap;
import java.util.List;

@Mapper
public interface CprospecLoadMapper {
    //判断传入的标准商品档案ID是否存在
    @Select("SELECT COUNT(1) FROM PS_C_PRO WHERE ID=#{id}")
    int selectProCount(@Param("id") Long id);

    /**
     * 根据标准商品档案ID以及配销中心ID确定该配销中心是否有该商品
     *
     * @param eCode     商品档案编码
     * @return count
     */
    @Select("SELECT ID,ISACTIVE,IS_GROUP,PS_C_SHAPEGROUP_ID FROM PS_C_PRO WHERE ECODE=#{eCode}")
    HashMap findDistribPro(@Param("eCode") String eCode);

    @Select("SELECT ECODE,ENAME FROM CP_C_STOREORG WHERE ID=#{distribId}")
    HashMap getDistribInfo(@Param("distribId") Long distribId);

    /**
     * 根据标准商品档案ID以及配销中心ID确定该配销中心是否有该商品
     *
     * @param eCode 商品档案编码
     * @return count
     */
    @Select("SELECT ID FROM PS_C_PRO WHERE ECODE=#{eCode}")
    Long findNormPro(@Param("eCode") String eCode);


    @Select("SELECT COUNT(1) FROM PS_C_SKU WHERE ECODE=#{eCode} AND CP_C_DISTRIB_ID = 0")
    Long findNormSku(@Param("eCode") String eCode);

    /**
     * 根据条码编码或者国标码匹配条码数（用于单据明细商品录入判断）
     * @param id
     * @return
     */
    @Select("SELECT COUNT(1) FROM PS_C_SKU WHERE (ECODE=#{eCode} OR GBCODE=#{eCode}) AND CP_C_DISTRIB_ID = 0")
    Long findSkuByEcodeOrGB(@Param("eCode") String eCode);

    /**
     * 根据条码编码或者国标码匹配条码档案（用于单据明细商品录入判断）
     * @param id
     * @return
     */
    @Select("SELECT * FROM PS_C_SKU WHERE (ECODE=#{eCode} OR GBCODE=#{eCode}) AND CP_C_DISTRIB_ID=#{distribId}")
    HashMap findSkuInfoByEcodeOrGB(@Param("eCode") String eCode, @Param("distribId") Long distribId);

    /**
     * 根据条码编码或者国标码匹配条码档案（用于单据明细商品录入判断）
     * @param id
     * @return
     */
    @Select("SELECT * FROM PS_C_SKU WHERE GBCODE=#{eCode} AND CP_C_DISTRIB_ID=#{distribId}")
    HashMap findSkuInfoByGB(@Param("eCode") String eCode, @Param("distribId") Long distribId);

    @Select("SELECT ID,ECODE FROM PS_C_SKU WHERE Id=#{id}")
    HashMap getSkuById(@Param("id") Long id);

    /**
     * 根据标准商品档案ID以及配销中心ID确定该配销中心是否有该SKU
     *
     * @param eCode     商品档案编码
     * @param distribId 配销中心ID
     * @return count
     */
    @Select("SELECT * FROM PS_C_SKU WHERE ECODE=#{eCode} AND CP_C_DISTRIB_ID=#{distribId}")
    HashMap findDistribSku(@Param("eCode") String eCode, @Param("distribId") Long distribId);

    @Select("SELECT DISTINCT(PS_C_SPEC1OBJ_ID),MAINCOLOR,FABCOLOR FROM PS_C_SKU WHERE PS_C_PRO_ID=#{ps_c_pro_id}")
    List<HashMap> selectMainAndFab(@Param("ps_c_pro_id") Long ps_c_pro_id);

    @Select("SELECT DISTINCT(PS_C_SPEC2OBJ_ID) FROM PS_C_SKU WHERE PS_C_PRO_ID=#{ps_c_pro_id}")
    List<HashMap> selectSize(@Param("ps_c_pro_id") Long ps_c_pro_id);

    @Select("SELECT ID,ENAME,ECODE FROM PS_C_SPECOBJ WHERE ID in (SELECT DISTINCT(PS_C_SPEC1OBJ_ID) FROM PS_C_SKU WHERE PS_C_PRO_ID=#{proId}) AND ENAME = #{ename} and ISACTIVE='Y' limit 1 ")
    HashMap selectColorByProIDAndEname(@Param("proId") Long proId, @Param("ename") String ename);

    @Select("SELECT ENAME,ECODE FROM PS_C_SPECOBJ WHERE ID=#{id}")
    HashMap selectcolorNameAndEcode(@Param("id") Long id);

    @Select("SELECT ENAME,ECODE,MATRIXCOLNO FROM PS_C_SPECOBJ WHERE ID=#{id}")
    HashMap selectSizeNameAndEcode(@Param("id") Long id);

    @Select("SELECT ID,ENAME,ECODE,MATRIXCOLNO FROM PS_C_SPECOBJ WHERE ID in (${ids})  order by MATRIXCOLNO,ECODE asc")
    List<JSONObject> selectSizeNameAndIds(@Param("ids") String ids);
}
