package com.jackrain.nea.ps.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.common.ReferenceUtil;
import com.jackrain.nea.core.schema.Column;
import com.jackrain.nea.core.web.interpreter.ColumnInterpreter;
import com.jackrain.nea.exception.ColumnInterpretException;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.CproColumnCmd;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Locale;
@Slf4j
@Component
@Service(protocol = "dubbo" , validation = "true" , version = "1.0" , group = "ps")
public class CproCommonServerImp implements ColumnInterpreter {

    @Override
    public String parseValue(Object value, Locale locale, Column column) throws ColumnInterpretException {
            JSONObject objdata = (JSONObject)value;
            String service="com.jackrain.nea.ps.api.CproColumnCmd";
            String center  = column.getReferenceTable().getCategory().getSubSystem().getCenter();
            String[] gv = center.split(":");
            if(gv.length != 2){
                throw new NDSException("center is error");
            }
            Object o = ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(), service,gv[0],gv[1]);
            HashMap parm=new HashMap();
            long id=0;
            if(objdata.containsKey("id")){
                try{
                    id = objdata.getLong("id");
                }catch (Exception e){
                    return  "";
                }
            }else {
                return "";
            }
            parm.put("id",id);
            ValueHolder val = ((CproColumnCmd) o).execute(parm);
            return  (String) val.get("data");

    }

    @Override
    public Object getValue(String s, Locale locale, Column column) throws ColumnInterpretException {
        return null;
    }


    @Override
    public String changeValue(String s, Locale locale, Column column) throws ColumnInterpretException {
        return null;
    }


    @Override
    public JSONArray parseValues(Object o, Locale locale, Column column) throws ColumnInterpretException {
        return null;
    }

}
