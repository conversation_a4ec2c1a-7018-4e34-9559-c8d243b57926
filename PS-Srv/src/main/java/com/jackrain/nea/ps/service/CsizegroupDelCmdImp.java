package com.jackrain.nea.ps.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.CsizegroupDelCmd;
import com.jackrain.nea.ps.mapper.CsizegroupMapper;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;

/**
 * <AUTHOR> on 2017/9/29.
 * 尺寸组及尺寸删除service
 */

@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps")
public class CsizegroupDelCmdImp extends CommandAdapter implements CsizegroupDelCmd {
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {

        CsizegroupDelCmdImp csizegroupDelCmd = ApplicationContextHandle.getBean(CsizegroupDelCmdImp.class);
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
        String isDel = param.getString("isdelmtable");
        Long specId = param.getLong("objid");
        JSONObject fixColumn = param.getJSONObject("tabitem");
        String isdel = "true";
        if (isdel.equals(isDel)) {
            //删除主表
            vh = csizegroupDelCmd.delMain(specId, querySession);
        } else {
            //删除明细
            if (fixColumn.size() != 0) {
                JSONArray deleteArray = fixColumn.getJSONArray("PS_C_SIZE");
                vh = delsubTableDetail(deleteArray, querySession, specId);
            }
        }
        return vh;
    }

    /***
     * 删除主表
     * @param speceId
     * @param querySession
     * @return ValueHolder
     */
    public ValueHolder delMain(Long speceId, QuerySession querySession) {

        ValueHolder valueHolder = new ValueHolder();
        CsizegroupMapper csizegroupMapper = ApplicationContextHandle.getBean(CsizegroupMapper.class);
        int specIdCount = csizegroupMapper.findSpaceDelId(speceId);
        if (specIdCount <= 0) {
            throw new NDSException(Resources.getMessage("当前记录已不存在！", querySession.getLocale()));
        }
        // 判断是否存在标准商品档案表中
        int colproCount = csizegroupMapper.findColproDelId(speceId);
        if (colproCount > 0) {
            String specName = csizegroupMapper.findSpaceEname(speceId);
            throw new NDSException(Resources.getMessage(specName + "已有商品记录，不允许删除！", querySession.getLocale()));
        }
        // 判断是否存在于尺寸表中
        int sizeDelCount = csizegroupMapper.findColDelId(speceId);
        if (sizeDelCount > 0) {
            String specName = csizegroupMapper.findSpaceEname(speceId);
            throw new NDSException(Resources.getMessage(specName + "已有尺寸记录，不允许删除！", querySession.getLocale()));
        }
        int deleteCount = csizegroupMapper.deleteSpece(speceId);
        if (deleteCount <= 0) {
            throw new NDSException(Resources.getMessage("删除失败", querySession.getLocale()));
        } else {
            valueHolder.put("code", 0);
            valueHolder.put("message", "删除成功!");
        }
        return valueHolder;
    }

    /***
     * 删除子表
     * @param deleteArray
     * @param querySession
     * @param speceId
     * @return ValueHolder
     */
    public ValueHolder delsubTableDetail(JSONArray deleteArray, QuerySession querySession, Long speceId) {

        ValueHolder valueHolder = new ValueHolder();
        JSONObject jsonObject = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        CsizegroupMapper csizegroupMapper = ApplicationContextHandle.getBean(CsizegroupMapper.class);
        //查询出条码表中所有的尺寸和颜色ID
        int colproCount = csizegroupMapper.findColskuDelId(speceId);
        int index = 0;
        int successCount = 0;
        for (int i = 0; i < deleteArray.size(); i++) {

            long delId = deleteArray.getLong(i);
            try {

                int delColHave = csizegroupMapper.findSizeifHave(delId);
                if (delColHave <= 0) {
                    throw new NDSException(Resources.getMessage("当前记录已不存在！", querySession.getLocale()));
                }
                if (colproCount > 0) {
                    String colName = csizegroupMapper.findcolEname(delId);
                    String colEcode = csizegroupMapper.findcolEcode(delId);
                    throw new NDSException(Resources.getMessage("【" + colEcode + "】" + colName + "已有条码记录，不允许删除！", querySession.getLocale()));
                }
                int deleteCount = csizegroupMapper.deleteCol(delId);
                //小于0表示数据已经被删除
                if (deleteCount <= 0) {
                    throw new NDSException(Resources.getMessage("删除失败！", querySession.getLocale()));
                } else {
                    successCount++;
                }

            } catch (NDSException e) {
                //捕获删除异常时的数据
                jsonObject.put("id", delId);
                jsonObject.put("code", -1);
                jsonObject.put("message", e.toJsonException().get("msg"));
                jsonArray.set(index, jsonObject);
                index++;
            }
        }

        //更新主表
        if (successCount > 0) {

            Timestamp timestamp = new Timestamp(System.currentTimeMillis());
            JSONObject updateObj = new JSONObject();
            updateObj.put("MODIFIEDDATE", timestamp);
            updateObj.put("MODIFIERID", Integer.valueOf(querySession.getUser().getId()).longValue());
            updateObj.put("ID", speceId);
            updateObj.put("MODIFIERNAME", querySession.getUser().getName());
            updateObj.put("MODIFIERENAME", querySession.getUser().getEname());
            csizegroupMapper.updateSpecgroup(updateObj);

        }
        if (index > 0) {
            valueHolder.put("code", -1);
        } else {
            valueHolder.put("code", 0);
        }

        valueHolder.put("message", "删除成功的记录数是：" + (deleteArray.size() - index) + "删除失败的记录数：" + index + " ");
        valueHolder.put("data", jsonArray);
        return valueHolder;

    }
}
