package com.jackrain.nea.ps.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.core.db.Tools;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.CsizegroupUpdateCmd;
import com.jackrain.nea.ps.entity.CspecobjEntity;
import com.jackrain.nea.ps.mapper.CsizegroupMapper;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;

/**
 * <AUTHOR> on 2017/9/29.
 * 尺寸组及尺寸更新service
 */

@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps")
public class CsizegroupUpdateCmdImp extends CommandAdapter implements CsizegroupUpdateCmd {

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {

        CsizegroupMapper csizegroupMapper = ApplicationContextHandle.getBean(CsizegroupMapper.class);
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        JSONObject superTable = fixColumn.getJSONObject("PS_C_SIZEGROUP");
        long sizeSpecId = param.getLong("objid");
        int specIdCount = csizegroupMapper.findSpaceDelId(sizeSpecId);
        if (specIdCount <= 0) {
            throw new NDSException(Resources.getMessage("当前记录已不存在！", querySession.getLocale()));
        }

        JSONArray itemIdArray = new JSONArray();
        JSONArray subTableArry = fixColumn.getJSONArray("PS_C_SIZE");
        JSONArray dataArrayJson = new JSONArray();
        if (subTableArry != null && subTableArry.size() > 0) {
            for (int i = 0; i < subTableArry.size(); i++) {
                JSONObject subTable = subTableArry.getJSONObject(i);
                long detailID = subTable.getLongValue("ID");

                JSONObject subTableJob = new JSONObject();
                try {
                    if (detailID <= 0) {
                        if (subTable.getString("ECODE") == null) {
                            throw new NDSException(Resources.getMessage("尺寸编码不能为空", querySession.getLocale()));
                        }
                        if (subTable.getString("ENAME") == null) {
                            throw new NDSException(Resources.getMessage("尺寸名称不能为空", querySession.getLocale()));
                        }
                        int sizeCount = csizegroupMapper.findClrCount(sizeSpecId, subTable.getString("ECODE"));
                        if (sizeCount > 0) {
                            throw new NDSException(Resources.getMessage("输入的数据已存在：尺寸编码", querySession.getLocale()));
                        }
                        long specId = csizegroupMapper.findSpeceByFlag(2);
                        CspecobjEntity cspecobjEntity = new CspecobjEntity();
                        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
                        String eCodeInsert = subTable.getString("ECODE");
                        String eNameInsert = subTable.getString("ENAME");
                        String sizeName = csizegroupMapper.selectEnameById(sizeSpecId);
                        cspecobjEntity.setSizename(sizeName + "-" + eCodeInsert + "-" + eNameInsert);
                        cspecobjEntity.setAd_client_id(Integer.valueOf(querySession.getUser().getClientId()).longValue());
                        cspecobjEntity.setAd_org_id(Integer.valueOf(querySession.getUser().getOrgId()).longValue());
                        cspecobjEntity.setPs_c_spec_id(specId);
                        cspecobjEntity.setPs_c_specgroup_id(sizeSpecId);
                        cspecobjEntity.setEcode(eCodeInsert);
                        cspecobjEntity.setEname(eNameInsert);
                        cspecobjEntity.setMixname("[" + eCodeInsert + "]" + eNameInsert);
                        cspecobjEntity.setRemark(subTable.getString("REMARK"));
                        cspecobjEntity.setMatrixcolno(subTable.getLong("MATRIXCOLNO"));
                        cspecobjEntity.setOwnerid(Integer.valueOf(querySession.getUser().getId()).longValue());
                        cspecobjEntity.setModifierid(Integer.valueOf(querySession.getUser().getId()).longValue());
                        cspecobjEntity.setCreationdate(timestamp);
                        cspecobjEntity.setModifieddate(timestamp);
                        cspecobjEntity.setIsactive(subTable.getString("ISACTIVE"));
                        if (subTable.getString("ISACTIVE") == null || "".equals(subTable.getString("ISACTIVE"))) {
                            cspecobjEntity.setIsactive("Y");
                        }
                        cspecobjEntity.setBrandname(subTable.getString("BRAND_NAME"));
                        cspecobjEntity.setOwnername(querySession.getUser().getName());
                        cspecobjEntity.setOwnerename(querySession.getUser().getEname());
                        cspecobjEntity.setModifiername(querySession.getUser().getName());
                        cspecobjEntity.setModifierename(querySession.getUser().getEname());
                        Long id = Tools.getSequence("PS_C_SPECOBJ");
                        cspecobjEntity.setId(id);
                        int insertRes = csizegroupMapper.insertSize(cspecobjEntity);
                        if (insertRes > 0) {
                            itemIdArray.add(cspecobjEntity.getId());
                            JSONObject updateObj = new JSONObject();
                            updateObj.put("MODIFIEDDATE", timestamp);
                            updateObj.put("MODIFIERID", Integer.valueOf(querySession.getUser().getId()).longValue());
                            updateObj.put("ID", sizeSpecId);
                            updateObj.put("MODIFIERNAME", querySession.getUser().getName());
                            updateObj.put("MODIFIERENAME", querySession.getUser().getEname());
                            csizegroupMapper.updateSpecgroup(updateObj);

                        } else {
                            throw new NDSException(Resources.getMessage("尺寸明细插入失败", querySession.getLocale()));
                        }
                    } else {

                        int delColHave = csizegroupMapper.findSizeifHave(detailID);
                        String ecode = csizegroupMapper.findcolEcode(detailID);
                        String ename = csizegroupMapper.findcolEname(detailID);
                        if (delColHave <= 0) {
                            throw new NDSException(Resources.getMessage("当前记录已不存在！", querySession.getLocale()));
                        }

                        if (subTable.containsKey("ECODE")) {
                            if (subTable.getString("ECODE") == null) {
                                throw new NDSException(Resources.getMessage("尺寸编码不能为空", querySession.getLocale()));
                            }
                            String sizIdStr = csizegroupMapper.findClridStr(sizeSpecId, subTable.getString("ECODE"));
                            if (sizIdStr != null && !Long.valueOf(sizIdStr).equals(subTable.getLong("ID"))) {
                                throw new NDSException(Resources.getMessage("输入的数据已存在：尺寸编码", querySession.getLocale()));
                            }
                            subTableJob.put("ECODE", subTable.getString("ECODE"));
                            ecode = subTable.getString("ECODE");
                        }
                        if (subTable.containsKey("ENAME")) {
                            if (subTable.getString("ENAME") == null) {
                                throw new NDSException(Resources.getMessage("尺寸名称不能为空", querySession.getLocale()));
                            }
                            subTableJob.put("ENAME", subTable.getString("ENAME"));
                            ename = subTable.getString("ENAME");
                        }
                        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
                        String sizeName = csizegroupMapper.selectEnameById(sizeSpecId);
                        subTableJob.put("SIZE_NAME",sizeName + "-" + ecode + "-" + ename);
                        subTableJob.put("MIXNAME", "[" + ecode + "]" + ename);
                        subTableJob.put("OWNERID", Integer.valueOf(querySession.getUser().getId()).longValue());
                        subTableJob.put("MODIFIEDDATE", timestamp);
                        if (subTable.containsKey("ISACTIVE")) {
                            subTableJob.put("ISACTIVE", subTable.getString("ISACTIVE"));
                        }
                        if (subTable.containsKey("REMARK")) {
                            subTableJob.put("REMARK", subTable.getString("REMARK"));
                        }
                        if (subTable.containsKey("MATRIXCOLNO")) {
                            subTableJob.put("MATRIXCOLNO", subTable.getString("MATRIXCOLNO"));
                        }
                        if (subTable.containsKey("BRAND_NAME")) {
                            subTableJob.put("BRAND_NAME", subTable.getString("BRAND_NAME"));
                        }
                        subTableJob.put("MODIFIERNAME", querySession.getUser().getName());
                        subTableJob.put("MODIFIERENAME", querySession.getUser().getEname());
                        subTableJob.put("ID", subTable.getLong("ID"));
                        int updataSize = csizegroupMapper.updateColgroup(subTableJob);
                        if (updataSize > 0) {
                            itemIdArray.add(subTableJob.getLong("ID"));
                            JSONObject updateObj = new JSONObject();
                            updateObj.put("MODIFIEDDATE", timestamp);
                            updateObj.put("MODIFIERID", Integer.valueOf(querySession.getUser().getId()).longValue());
                            updateObj.put("ID", sizeSpecId);
                            updateObj.put("MODIFIERNAME", querySession.getUser().getName());
                            updateObj.put("MODIFIERENAME", querySession.getUser().getEname());
                            csizegroupMapper.updateSpecgroup(updateObj);
                        } else {
                            throw new NDSException(Resources.getMessage("当前记录已不存在！", querySession.getLocale()));
                        }
                    }
                } catch (NDSException e) {
                    dataArrayJson.add(getData(e, detailID));
                }
            }
        }

        if (superTable != null) {
            JSONObject superTableJob = new JSONObject();
            String ename = "ENAME";
            if (superTable.containsKey(ename)) {
                if (superTable.getString(ename) == null) {
                    throw new NDSException(Resources.getMessage("名称不能为空", querySession.getLocale()));
                } else {
                    String enameStr = superTable.getString("ENAME");
                    //查询规格表中的规格ID
                    long specId = csizegroupMapper.findSpeceByFlag(2);
                    //规格id和名称唯一
                    String specidRes = csizegroupMapper.findSpecid(specId, enameStr);
                    if (specidRes != null && Long.valueOf(specidRes) != sizeSpecId) {
                        throw new NDSException(Resources.getMessage("输入的数据已存在：名称", querySession.getLocale()));
                    }
                    superTableJob.put("ENAME", enameStr);
                }
            }
            String remark = "REMARK";
            if (superTable.containsKey(remark)) {
                superTableJob.put("REMARK", superTable.getString("REMARK"));
            }
            String isactive = "ISACTIVE";
            if (superTable.containsKey(isactive)) {
                superTableJob.put("ISACTIVE", superTable.getString("ISACTIVE"));
            }
            String orderno = "ORDERNO";
            if (superTable.containsKey(orderno)) {
                superTableJob.put("ORDERNO", superTable.getLong("ORDERNO"));
            }
            if (superTable.containsKey("PS_C_SPEC2_ECODE")) {
                superTableJob.put("PS_C_SPEC2_ECODE", superTable.getString("PS_C_SPEC2_ECODE"));
            }
            superTableJob.put("MODIFIERID", Integer.valueOf(querySession.getUser().getId()).longValue());
            superTableJob.put("MODIFIEDDATE", new Timestamp(System.currentTimeMillis()));
            superTableJob.put("MODIFIERNAME", querySession.getUser().getName());
            superTableJob.put("MODIFIERENAME", querySession.getUser().getEname());
            superTableJob.put("ID", sizeSpecId);
            int updataRes = csizegroupMapper.updateSpecgroup(superTableJob);
            if (updataRes > 0) {
                //更新成功
            } else {
                throw new NDSException(Resources.getMessage("当前记录已不存在！", querySession.getLocale()));
            }
        }

        JSONObject ret = new JSONObject();
        ret.put("objid", sizeSpecId);
        if (!itemIdArray.isEmpty()) {
            ret.put("PS_C_SIZE", itemIdArray);
        }
        vh.put("ret", ret);
        if (dataArrayJson.size() > 0) {
            vh.put("data", dataArrayJson);
            vh.put("code", -1);
            vh.put("message", Resources.getMessage("失败", querySession.getLocale()));

        } else {
            vh.put("code", 0);
            vh.put("message", Resources.getMessage("成功", querySession.getLocale()));
        }

        return vh;
    }

    /**
     * 获取明细表错误信息并插入dataJson
     *
     * @param e
     * @param itemId
     * @return
     */
    private JSONObject getData(NDSException e, Long itemId) {
        JSONObject dataJson = new JSONObject();
        dataJson.put("id", itemId);
        dataJson.put("code", -1);
        dataJson.put("message", e.getMessage());
        return dataJson;
    }


}
