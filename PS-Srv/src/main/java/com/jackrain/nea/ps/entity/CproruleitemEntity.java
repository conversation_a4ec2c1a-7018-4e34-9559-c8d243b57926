package com.jackrain.nea.ps.entity;

public class CproruleitemEntity {
  private Long id;
  private Long ad_client_id;
  private Long ad_org_id;
  private Long ps_c_prorule_id;
  private Long orderno;
  private String prefix;
  private Long ps_c_prodim_id;
  private String isactive;
  private Long ownerid;
  private Long modifierid;
  private java.sql.Timestamp creationdate;
  private java.sql.Timestamp modifieddate;

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public Long getAd_client_id() {
    return ad_client_id;
  }

  public void setAd_client_id(Long ad_client_id) {
    this.ad_client_id = ad_client_id;
  }

  public Long getAd_org_id() {
    return ad_org_id;
  }

  public void setAd_org_id(Long ad_org_id) {
    this.ad_org_id = ad_org_id;
  }

  public Long getPs_c_prorule_id() {
    return ps_c_prorule_id;
  }

  public void setPs_c_prorule_id(Long ps_c_prorule_id) {
    this.ps_c_prorule_id = ps_c_prorule_id;
  }

  public Long getOrderno() {
    return orderno;
  }

  public void setOrderno(Long orderno) {
    this.orderno = orderno;
  }

  public String getPrefix() {
    return prefix;
  }

  public void setPrefix(String prefix) {
    this.prefix = prefix;
  }

  public Long getPs_c_prodim_id() {
    return ps_c_prodim_id;
  }

  public void setPs_c_prodim_id(Long ps_c_prodim_id) {
    this.ps_c_prodim_id = ps_c_prodim_id;
  }

  public String getIsactive() {
    return isactive;
  }

  public void setIsactive(String isactive) {
    this.isactive = isactive;
  }

  public Long getOwnerid() {
    return ownerid;
  }

  public void setOwnerid(Long ownerid) {
    this.ownerid = ownerid;
  }

  public Long getModifierid() {
    return modifierid;
  }

  public void setModifierid(Long modifierid) {
    this.modifierid = modifierid;
  }

  public java.sql.Timestamp getCreationdate() {
    return creationdate;
  }

  public void setCreationdate(java.sql.Timestamp creationdate) {
    this.creationdate = creationdate;
  }

  public java.sql.Timestamp getModifieddate() {
    return modifieddate;
  }

  public void setModifieddate(java.sql.Timestamp modifieddate) {
    this.modifieddate = modifieddate;
  }
}
