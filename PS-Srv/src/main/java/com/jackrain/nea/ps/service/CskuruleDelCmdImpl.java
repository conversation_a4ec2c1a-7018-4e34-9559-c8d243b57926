package com.jackrain.nea.ps.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.CskuruleDelCmd;
import com.jackrain.nea.ps.mapper.CskuruleMapper;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2017/09/28
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps")
public class CskuruleDelCmdImpl extends CommandAdapter implements CskuruleDelCmd {

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {

        CskuruleDelCmdImpl cskuruleDelCmdImpl = ApplicationContextHandle.getBean(CskuruleDelCmdImpl.class);
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);

        Boolean isdelmtable = param.getBoolean("isdelmtable");
        Long objid = param.getLong("objid");
        if (objid == null || isdelmtable == null) {
            throw new NDSException(Resources.getMessage("请先选择需要删除的记录！", querySession.getLocale()));
        }

        try {
            if (!isdelmtable) {
                //删除明细
                vh = delsubTableDetail(param, querySession);

            } else {
                //删除主表
                vh = delMain(param, querySession);

            }
        } catch (Exception e) {
            throw new NDSException(e.getMessage());
        }
        return vh;


    }

    //删除主表
    @Transactional(rollbackFor = Exception.class)
    public ValueHolder delMain(JSONObject param, QuerySession querySession) {
        CskuruleMapper skuruleMapper = ApplicationContextHandle.getBean(CskuruleMapper.class);
        Long objid = param.getLong("objid");
        ValueHolder vh = new ValueHolder();

        JSONObject jo = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        // 删除成功条数
        int successCount = 0;

        //根据ID查询数据库中该条记录是否存在
        int scount = skuruleMapper.selectCountById(objid);
        if (scount <= 0) {
            throw new NDSException(Resources.getMessage("当前记录已不存在！", querySession.getLocale()));
        }
        //TODO 判断id是否存在于【标准商品档案】表中
        //查询条码生成规则ID对应的名称ename
        String ename = skuruleMapper.selectCskuruleEnameById(objid);
        //判断【条码生成规则】在【标准商品档案】表是否存在
        int tcount = skuruleMapper.selectCproExist(objid);
        if (tcount > 0) {
            throw new NDSException(ename + Resources.getMessage("已有商品记录，不允许删除！", querySession.getLocale()));
        }


        try {
            //根据主表的id删除关联的明细
            successCount = skuruleMapper.deleteCskuruleItemByPscSkurule(objid);
            //删除主表
            skuruleMapper.deleteCskurule(objid);


        } catch (Exception e) {
            throw new NDSException(Resources.getMessage("当前记录已不存在！", querySession.getLocale()));
        }
        if (successCount > 0) {
            vh.put("code", 0);
            vh.put("message", "删除成功的记录数是:" + successCount);
        } else {
            throw new NDSException(Resources.getMessage("当前记录已不存在！", querySession.getLocale()));
        }

        return vh;
    }

    /***
     * 删除明细
     * @param param
     * @param querySession
     * @return ValueHolder
     */
    public ValueHolder delsubTableDetail(JSONObject param, QuerySession querySession) {
        //获取当前操作的时间
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        CskuruleMapper skuruleMapper = ApplicationContextHandle.getBean(CskuruleMapper.class);
        ValueHolder vh = new ValueHolder();
        JSONObject tabitem = param.getJSONObject("tabitem");
        Long objid = param.getLong("objid");
        JSONArray deleteArray = null;
        int sum = 0;
        if (tabitem != null) {
            deleteArray = tabitem.getJSONArray("PS_C_SKURULE_ITEM");
            JSONArray ary = new JSONArray();

            // 删除成功条数
            int successCount = 0;
            // 删除失败条数
            int failCount = 0;
            if (deleteArray == null) {
                throw new NDSException(Resources.getMessage("请先选择需要删除的明细记录！", querySession.getLocale()));
            }
            if (deleteArray.size() == 0) {
                throw new NDSException(Resources.getMessage("请先选择需要删除的明细记录！", querySession.getLocale()));
            }
            if (deleteArray.size() > 0) {
                for (int i = 0; i < deleteArray.size(); i++) {
                    long delId = deleteArray.getLong(i);

                    try {

                        //查询该数据在数据库中是否存在
                        int count = skuruleMapper.selectById(delId);
                        if (count <= 0) {
                            throw new NDSException(Resources.getMessage("当前记录已不存在！", querySession.getLocale()));
                        }
                        //删除id对应的明细
                        sum = skuruleMapper.deleteCskuruleItem(delId);
                        if(sum<=0)
                        {
                            throw new NDSException(Resources.getMessage("当前记录已不存在",querySession.getLocale()));
                        }
                        ++successCount;

                    } catch (NDSException e) {
                        //捕获删除异常时的数据
                        failCount++;
                        JSONObject jo = new JSONObject();
                        jo.put("id", delId);
                        jo.put("code", -1);
                        jo.put("message", e.getMessage());
                        ary.add(jo);
                    }

                }

                if (successCount > 0) {

                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("MODIFIERID", Integer.valueOf(querySession.getUser().getId()).longValue());
                    jsonObject.put("MODIFIEDDATE", timestamp);
                    jsonObject.put("MODIFIERNAME", querySession.getUser().getName());
                    jsonObject.put("MODIFIERENAME", querySession.getUser().getEname());
                    jsonObject.put("ID", objid);
                    skuruleMapper.update(jsonObject);

                    vh.put("code", 0);
                    vh.put("message", Resources.getMessage("删除成功的记录是：" + successCount));
                    if (ary.size() > 0) {
                        vh.put("code", -1);
                        vh.put("message", Resources.getMessage("删除成功的记录数是:", querySession.getLocale()) + successCount + "\n" + Resources.getMessage("删除失败的记录数是:", querySession.getLocale()) + failCount);
                        vh.put("data", ary);
                        return vh;
                    }

                } else {
                    vh.put("code", -1);
                    vh.put("message", Resources.getMessage("删除成功的记录数是:", querySession.getLocale()) + successCount + "\n" + Resources.getMessage("删除失败的记录数是:", querySession.getLocale()) + failCount);
                    vh.put("data", ary);
                    return vh;
                }

            }

        }

        return vh;

    }

}
