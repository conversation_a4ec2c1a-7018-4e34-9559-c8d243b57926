package com.jackrain.nea.ps.mapper;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.ps.entity.CbrandEntity;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.jdbc.SQL;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> zhang
 * @date 2017/9/24
 */


//品牌定义
@Mapper
public interface CbrandMapper {

    class CbrandSqlProvider {

        public String insertDySql(JSONObject jsonObject) {
            return new SQL() {
                {
                    INSERT_INTO("PS_C_BRAND");
                    for (String key : jsonObject.keySet()) {
                        VALUES(key, "#{" + key + "}");
                    }
                }
            }.toString();
        }

        public String updateSql(JSONObject map) {
            return new SQL() {
                {
                    UPDATE("PS_C_BRAND");
                    for (String key : map.keySet()) {
                        String id = "ID";
                        if (!id.equals(key)) {
                            SET(key + "= #{" + key + "}");
                        }
                    }
                    WHERE("ID = #{ID}");
                }
            }.toString();
        }

        /**
         * 向 用户组-品牌权限表 插入数据
         *
         * @param jo
         * @return
         */
        public String insertSql(JSONObject jo) {
            return new SQL() {
                {
                    INSERT_INTO("CP_C_HRUSERS_BRAND");
                    for (String key : jo.keySet()) {
                        VALUES(key, "#{" + key + "}");
                    }
                }
            }.toString();
        }

    }

    @UpdateProvider(type = CbrandSqlProvider.class, method = "updateSql")
    /**
     * 获取JSONObject，执行更新操作
     *
     * @param map
     * @return int
     */
    int update(JSONObject map);

    /**
     * 统计 用户组表 的id集合
     *
     * @return int
     */
    @Select("select id from GROUPS")
    ArrayList<Long> getGroups();

    /**
     * 向 用户组-品牌权限表 插入数据
     *
     * @param jsonObject
     * @return int
     */
    @InsertProvider(type = CbrandSqlProvider.class, method = "insertSql")
    int insertGroupBrand(@Param("jsonObject") JSONObject jsonObject);

    /**
     * 获取实体类，执行增加操作
     *
     * @param brand
     * @return int
     */
    @InsertProvider(type = CbrandSqlProvider.class, method = "insertDySql")
    int insert(JSONObject brand);

    /**
     * 获取ename，统计记录数
     *
     * @param ename
     * @return int
     */
    @Select("SELECT COUNT(*) FROM PS_C_BRAND WHERE ENAME=#{ename}")
    int selectEname(@Param("ename") String ename);

    /**
     * 获取ecode，统计记录数
     *
     * @param ecode
     * @return int
     */
    @Select("SELECT COUNT(*) FROM PS_C_BRAND WHERE ECODE=#{ecode}")
    int selectEcode(@Param("ecode") String ecode);

    /**
     * 获取ename，获取id值
     *
     * @param ename
     * @return Long
     */
    @Select("select id from ps_c_brand where ename=#{ename}")
    Long selectEnameById(@Param("ename") String ename);

    /**
     * 获取ecode，获取id值
     *
     * @param ecode
     * @return Long
     */
    @Select("select id from ps_c_brand where ecode=#{ecode}")
    Long selectEcodeById(@Param("ecode") String ecode);

    /**
     * 获取id值，执行删除操作
     *
     * @param id
     * @return long
     */
    @Delete("DELETE FROM PS_C_BRAND WHERE ID=#{id}")
    long del(@Param("id") long id);

    /**
     * 删除【用户组-品牌权限】表中的记录
     *
     * @param id
     * @return
     */
    @Delete("delete from CP_C_HRUSERS_BRAND where ps_c_brand_id=#{id}")
    int delGroupBrand(@Param("id") long id);

    /**
     * 获取id值，获取记录
     *
     * @param id
     * @return CbrandEntity
     */
    @Select("SELECT ISACTIVE,ENAME,ECODE,REMARK,MODIFIERID,MODIFIEDDATE FROM PS_C_BRAND " +
            "WHERE ID=#{id}")
    CbrandEntity findById(@Param("id") long id);

    /**
     * 查询在标准商品档案组记录
     *
     * @param id
     * @return int
     */
    @Select("SELECT COUNT(PS_C_BRAND_ID) FROM PS_C_PRO " +
            "WHERE PS_C_BRAND_ID=#{id}")
    int findByProId(@Param("id") long id);

    /**
     * 查询在颜色组记录
     *
     * @param id
     * @return int
     */
    @Select("SELECT COUNT(PS_C_BRAND_ID) FROM PS_C_SPECGROUP " +
            "WHERE PS_C_BRAND_ID=#{id}")
    int findByClrId(@Param("id") long id);

    /**
     * 获取id值，查询品牌名
     *
     * @param id
     * @return String
     */
    @Select("SELECT ENAME FROM PS_C_BRAND WHERE ID=#{id}")
    String findEnameById(@Param("id") long id);

    @Select("Select ID, ENAME, ECODE from PS_C_BRAND where ENAME like CONCAT('%',#{name},'%') ORDER BY ENAME")
    List<Map<String, Object>> listByEname(@Param("name") String name);

    @Select("Select ID, ENAME, ECODE from PS_C_BRAND ORDER BY ENAME")
    List<Map<String, Object>> listAll();

    @Select("SELECT ENAME FROM PS_C_BRAND WHERE ID = #{brandId}")
    String selectEnameByBrandId(@Param("brandId") Long brandId);

}



