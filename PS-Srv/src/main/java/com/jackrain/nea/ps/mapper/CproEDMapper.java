package com.jackrain.nea.ps.mapper;

import com.alibaba.fastjson.JSONObject;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.HashMap;

/**
 * <AUTHOR>
 * 启用停用选款
 */
@Mapper
public interface CproEDMapper {

    @Select("SELECT * from ps_c_pro where CP_C_DISTRIB_ID=#{distribID} and ECODE=#{proecode} limit 1")
    HashMap querySKuByDistribAndProEcode(@Param("distribID") Long distribID, @Param("proecode") String proecode);

    /**
     * 判断此商品是否存在
     *
     * @param id
     * @return
     */
    @Select("SELECT ECODE,ISACTIVE,ISUP,ISSELECTION FROM PS_C_PRO WHERE ID=#{id}")
    JSONObject repeatPro(@Param("id") Long id);

    /**
     * 启用选款
     *
     * @param proJson
     * @return
     */
    @Update("UPDATE PS_C_PRO SET ISSELECTION='Y',MODIFIEDDATE=#{MODIFIEDDATE},MODIFIERID=#{MODIFIERID},MODIFIERNAME=#{MODIFIERNAME},MODIFIERENAME=#{MODIFIERENAME} WHERE ID = #{ID}")
    Integer selectionPro(JSONObject proJson);

    /**
     * 停用选款
     *
     * @param proJson
     * @return
     */
    @Update("UPDATE PS_C_PRO SET ISSELECTION='N',MODIFIEDDATE=#{MODIFIEDDATE},MODIFIERID=#{MODIFIERID},MODIFIERNAME=#{MODIFIERNAME},MODIFIERENAME=#{MODIFIERENAME} WHERE ID = #{ID}")
    Integer selectionDPro(JSONObject proJson);
}
