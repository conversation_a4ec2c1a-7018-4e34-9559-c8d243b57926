package com.jackrain.nea.ps.service;

import com.jackrain.nea.common.ValueHolderUtils;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.CproImageQueryCmd;
import com.jackrain.nea.ps.mapper.CproMapper;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * <AUTHOR>
 * 2018-3-1
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps")
public class CproImageQueryCmdImpl extends CommandAdapter implements CproImageQueryCmd {
    @Autowired
    private CproMapper cproMapper;

    @Override
    public ValueHolder execute(HashMap map) throws NDSException {


        ValueHolder valueHolder = ValueHolderUtils.success();
        valueHolder.put("data",cproMapper.getListByEcodes(map.get("ecodes").toString().split(",")));

        return valueHolder;
    }
}
