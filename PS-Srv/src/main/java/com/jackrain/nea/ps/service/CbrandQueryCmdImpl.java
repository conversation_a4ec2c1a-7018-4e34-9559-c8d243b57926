package com.jackrain.nea.ps.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jackrain.nea.common.ValueHolderUtils;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.CbrandQueryCmd;
import com.jackrain.nea.ps.api.table.PsCBrand;
import com.jackrain.nea.ps.mapper.CbrandMapper;
import com.jackrain.nea.ps.mapper.PsCBrandMapper;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 商品定义查询
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps")
public class CbrandQueryCmdImpl extends CommandAdapter implements CbrandQueryCmd {

    @Autowired
    private PsCBrandMapper psCBrandMapper;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);

        String name = param.getString("ENAME");
        CbrandMapper mapper = ApplicationContextHandle.getBean(CbrandMapper.class);
        if (name != null && !"".equals(name)) {
            return ValueHolderUtils.success("查询成功！", mapper.listByEname(name));
        }
        return ValueHolderUtils.success("查询成功！", mapper.listAll());
    }
    /**
     * 根据品牌编码查询品牌信息
     * @param eCode
     * @return
     */
    @Override
    public PsCBrand queryCBrandByECode(String eCode) {
        LambdaQueryWrapper<PsCBrand> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PsCBrand::getEcode, eCode);
        return psCBrandMapper.selectOne(queryWrapper);
    }
}
