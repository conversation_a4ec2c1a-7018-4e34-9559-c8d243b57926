package com.jackrain.nea.ps.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.ProListCmd;
import com.jackrain.nea.ps.api.request.ProListCmdRequest;
import com.jackrain.nea.ps.api.request.ProSelectPageRequest;
import com.jackrain.nea.ps.api.table.PsCPro;
import com.jackrain.nea.ps.mapper.PsCProMapper;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @author: pankai
 * @since: 2019-03-14
 * create at : 2019-03-14 13:32
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps")
public class ProListCmdImpl extends CommandAdapter implements ProListCmd {

    @Autowired
    private PsCProMapper psCProMapper;

    @Override
    public ValueHolder execute(ProListCmdRequest proListCmdRequest) throws NDSException {
        if(log.isDebugEnabled()){
            log.debug(this.getClass().getName()+" 入参："+proListCmdRequest);
        }
        ValueHolder valueHolder = new ValueHolder();
        //空指针判断
        if (proListCmdRequest == null || (CollectionUtils.isEmpty(proListCmdRequest.getProids()) && CollectionUtils.isEmpty(proListCmdRequest.getProecodes()))) {
            valueHolder.put("code", -1);
            valueHolder.put("message", "PARAM FAIL");
            return valueHolder;
        }
        List<Long> ids = proListCmdRequest.getProids();
        List<String> ecodeList = proListCmdRequest.getProecodes();
        List<PsCPro> psCPros = new ArrayList<PsCPro>();

        if (!CollectionUtils.isEmpty(ids)) {
            JSONArray proIds = new JSONArray();
            for (Long id : ids) {
                proIds.add(id);
            }
            psCPros = psCProMapper.proQuery(proIds);
        } else {
            JSONArray proEcodes = new JSONArray();
            for (String ecode : ecodeList) {
                proEcodes.add(ecode);
            }
            psCPros = psCProMapper.proQueryEcode(proEcodes);
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("result：") + psCPros);
        }
        valueHolder.put("data", psCPros);
        valueHolder.put("code", 0);
        valueHolder.put("message", "PROLISTCMD SUCCESS");
        return valueHolder;

    }

    @Override
    public ValueHolderV14<PageInfo<PsCPro>> selectPage(ProSelectPageRequest request) {
        if (log.isDebugEnabled()) {
            log.debug(this.getClass().getName()+"：{}", JSONObject.toJSONString(request));
        }
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.FAIL,"");
        try {
            if (Objects.isNull(request)||Objects.isNull(request.getPageNum())||Objects.isNull(request.getPageSize())) {
                v14.setMessage("请求对象为空或分页参数为空！");
            }
            if (request.getModifieddate()==null) {
                Date yesterday = DateUtils.addDays(new Date(), -1);
                request.setModifieddate(yesterday);
            }
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            String dateStr = format.format(request.getModifieddate());
            LambdaQueryWrapper<PsCPro> wrapper = new LambdaQueryWrapper<>();
            if (!request.getFullData()) {
                wrapper.apply("date_format(p.modifieddate,'%Y-%m-%d')={0}", dateStr);
            }
            PageHelper.startPage(request.getPageNum(), request.getPageSize());
            List<PsCPro> products = psCProMapper.getList(wrapper);
            PageInfo pageInfo = new PageInfo(products);
            v14.setCode(ResultCode.SUCCESS);
            v14.setMessage("查询商品打标数据成功！");
            v14.setData(pageInfo);
        } catch (Exception e) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(this.getClass().getName() + ": " + e.getMessage());
        }
        return v14;
    }
}
