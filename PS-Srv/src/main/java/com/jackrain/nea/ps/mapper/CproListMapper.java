package com.jackrain.nea.ps.mapper;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.web.utils.ArrayToSUtil;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * 商品查询
 */
@Mapper
public interface CproListMapper {
    @SelectProvider(type = CproListSql.class, method = "proList")
    List<HashMap> proList(JSONArray searchtype, JSONObject fixedcolumns, JSONArray orderby, Long qty, String date, List<Long> familyIds);

    @Select("SELECT DISTINCT\n" +
            "\t(a.QTY_RETAILW) AS QTY_RETAILW\n" +
            "FROM\n" +
            "\tps_c_pro_stock a\n" +
            "LEFT JOIN ps_c_pro b ON a.cp_c_pro_id = b.id\n" +
            "WHERE\n" +
            "\ta.CP_C_DISTRIB_ID = #{distirbId}\n" +
            "AND b.ISUP = 'Y'\n" +
            "AND b.ISACTIVE = 'Y'\n" +
            "ORDER BY\n" +
            "\ta.QTY_RETAILW DESC\n" +
            "LIMIT 3")
    List<Long> qtyQuery(@Param("distirbId") Long distirbId);

    /**
     * 查询家庭款ID
     *
     * @return
     */
    @Select("SELECT a.ID from ps_c_prodim_item a LEFT JOIN ps_c_prodim b on a.PS_C_PRODIM_ID = b.id WHERE b.ENAME='产品线' AND a.ENAME in('情侣','家庭','儿童')")
    List<Long> queryFamily();

    class CproListSql {
        public String proList(JSONArray searchtype, JSONObject fixedcolumns, JSONArray orderby, Long qty, String date, List<Long> familyIds) {
            return new SQL() {
                {
                    SELECT("a.ID,a.ENAME,a.ECODE,a.PRICELIST,a.PROMOTION_PRICE,a.PRONATURE,c.STARTSCORETOTAL,b.QTY_RETAILW,a.IMAGE,a.ISUP,a.ATTENTION");
                    SELECT("CASE WHEN b.QTY_RETAILW>=" + qty + " THEN 'Y' ELSE 'N' END AS HOT,CASE WHEN a.DATEMARKET >= " + date + " THEN 'Y' ELSE 'N' END AS NEW,a.ISSELECTION");
                    FROM("PS_C_PRO a");
                    LEFT_OUTER_JOIN("PS_C_PRO_STOCK b ON a.ID=b.CP_C_PRO_ID");
                    LEFT_OUTER_JOIN("PS_C_EVATOTALSCORE c ON  a.ID=c.PS_C_PRO_ID");
                    WHERE("a.ISUP='Y' AND a.ISACTIVE='Y'");
                    if (null != searchtype && searchtype.size() > 0) {
                        for (int i = 0; i < searchtype.size(); i++) {
                            JSONObject search = searchtype.getJSONObject(i);
                            if (search != null) {
                                String name = search.getString("name");
                                String type = search.getString("type");
                                if (null != fixedcolumns) {
                                    String value = fixedcolumns.getString(name);
                                    if (null != value) {
                                        if ("index".equals(type)) {
                                            if ("SEX".equals(name) && JSONArray.parseArray(value).contains(606)) {
                                                WHERE("(" + name + " in (" + org.apache.commons.lang3.StringUtils.join((JSONArray.parseArray(value)).toArray(), ",") + ") OR PROLINE in (" + org.apache.commons.lang3.StringUtils.join(familyIds.toArray(), ",") + "))");
                                            } else if ("ISSELECTION".equals(name)) {
                                                WHERE(name + " in (" + ArrayToSUtil.join((JSONArray.parseArray(value)).toArray(), ",") + ")");
                                            } else {
                                                WHERE(name + " in (" + org.apache.commons.lang3.StringUtils.join((JSONArray.parseArray(value)).toArray(), ",") + ")");
                                            }
                                        } else if ("filter".equals(type)) {
                                            String[] values = value.split("~");
                                            String begin = null;
                                            String end = null;
                                            if (values.length > 0) {
                                                begin = values[0];
                                                if (values.length == 2) {
                                                    end = values[1];
                                                }
                                            }
                                            if (!StringUtils.isEmpty(begin) && StringUtils.isEmpty(end)) {
                                                //44代表是清理款
                                                WHERE("(( PRONATURE=44 AND IF(PROMOTION_PRICE IS NULL,pricelist,PROMOTION_PRICE)" + ">=" + begin + ") OR (PRONATURE<>44 AND " + name + ">=" + begin + "))");
                                            } else if (!StringUtils.isEmpty(end) && StringUtils.isEmpty(begin)) {
                                                //44代表是清理款
                                                WHERE("(( PRONATURE=44 AND IF(PROMOTION_PRICE IS NULL,pricelist,PROMOTION_PRICE)" + "<=" + end + ") OR (PRONATURE<>44 AND " + name + "<=" + end + "))");
                                            } else if (!StringUtils.isEmpty(end) && !StringUtils.isEmpty(begin)) {
                                                //44代表是清理款
                                                WHERE("(( PRONATURE=44 AND IF(PROMOTION_PRICE IS NULL,pricelist,PROMOTION_PRICE)" + ">=" + begin + " AND IF(PROMOTION_PRICE IS NULL,pricelist,PROMOTION_PRICE) <=" + end + ") OR (PRONATURE<>44 AND " + name + " BETWEEN " + begin + " AND " + end + "))");
                                            }
                                        } else if ("like".equals(type)) {
                                            WHERE("(ENAME" + " like " + "'" + value + "%' OR ECODE like " + "'" + value + "%')");
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if (null != orderby && orderby.size() > 0) {
                        for (int i = 0; i < orderby.size(); i++) {
                            JSONObject order = orderby.getJSONObject(i);
                            if (null != order) {
                                String name = order.getString("name");
                                Boolean asc = order.getBooleanValue("asc");

                                if ("normal".equals(name)) {
                                    //44代表是清理款
                                    if (null != fixedcolumns.getJSONArray("PRONATURE") && fixedcolumns.getJSONArray("PRONATURE").contains(44) && fixedcolumns.getJSONArray("PRONATURE").size() == 1) {
                                        if (asc) {
                                            ORDER_BY("QTY_CAN_SUPPLY");
                                        } else {
                                            ORDER_BY("QTY_CAN_SUPPLY desc ");
                                        }
                                    } else if (null != fixedcolumns.getJSONArray("PRONATURE") && fixedcolumns.getJSONArray("PRONATURE").size() > 0) {
                                        if (asc) {
                                            ORDER_BY("DATEONSHELF");
                                        } else {
                                            ORDER_BY("DATEONSHELF desc ");
                                        }
                                    } else {
                                        if (asc) {
                                            ORDER_BY("DATEONSHELF,QTY_CAN_SUPPLY");
                                        } else {
                                            ORDER_BY("DATEONSHELF desc,QTY_CAN_SUPPLY desc ");
                                        }
                                    }
                                } else if ("PRICELIST".equals(name)) {
                                    if (asc) {
                                        ORDER_BY(" if(PRONATURE=44,IF(PROMOTION_PRICE is NULL,pricelist,PROMOTION_PRICE),pricelist)");
                                    } else {
                                        ORDER_BY(" if(PRONATURE=44,IF(PROMOTION_PRICE is NULL,pricelist,PROMOTION_PRICE),pricelist) desc ");
                                    }
                                }else if ("stock".equals(name)){
                                    continue;
                                }else {
                                    if (asc) {
                                        ORDER_BY(name);
                                    } else {
                                        ORDER_BY(name + " desc ");
                                    }
                                }
                            }
                        }
                    }
                    ORDER_BY("DATEMARKET desc ");
                }
            }.toString();
        }
    }
}
