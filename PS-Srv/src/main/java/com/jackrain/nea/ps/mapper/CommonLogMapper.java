package com.jackrain.nea.ps.mapper;

import com.alibaba.fastjson.JSONObject;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.jdbc.SQL;

import static org.apache.ibatis.mapping.StatementType.STATEMENT;

@Mapper
public interface CommonLogMapper {

    @InsertProvider(type = CommonLogSql.class, method = "insert")
    int insert( JSONObject jsonObject);

    @Select("select CP_C_DISTRIB_ID from ${table} where id = ${id}")
    @Options(statementType = STATEMENT)
    Long getCpCDistribIdById(@Param("table") String table, @Param("id") Long id);

    class CommonLogSql {
        public String insert(JSONObject jsonObject){
            return new SQL(){
                {
                    INSERT_INTO(jsonObject.getString("tableName"));
                    for (String key : jsonObject.keySet()){
                        if(!"tableName".equalsIgnoreCase(key)){
                            VALUES(key, "#{" + key + "}");
                        }
                    }
                }
            }.toString();
        }
    }
}
