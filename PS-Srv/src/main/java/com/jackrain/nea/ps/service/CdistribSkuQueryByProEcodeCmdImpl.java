package com.jackrain.nea.ps.service;

import com.jackrain.nea.common.StringUtils;
import com.jackrain.nea.common.ValueHolderUtils;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.exception.NDSRuntimeException;
import com.jackrain.nea.ps.api.CdistribSkuQueryByProEcodeCmd;
import com.jackrain.nea.ps.mapper.CskuMapper;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * <AUTHOR>
 * 2018-4-24
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps")
public class CdistribSkuQueryByProEcodeCmdImpl extends CommandAdapter implements CdistribSkuQueryByProEcodeCmd {

    @Autowired
    private CskuMapper cskuMapper;

    @Override
    public ValueHolder execute(HashMap map) throws NDSException {

        String ecode = map.get("ecode").toString();
        if(map.get("distribID")==null ||  !StringUtils.isNumeric(map.get("distribID").toString())){
            throw new NDSRuntimeException("不正确的配销中心ID");
        }
        Long distribID = Long.valueOf(map.get("distribID").toString());

        return ValueHolderUtils.success("成功",cskuMapper.queryByProEcodeAndDistribID(ecode,distribID));
    }
}
