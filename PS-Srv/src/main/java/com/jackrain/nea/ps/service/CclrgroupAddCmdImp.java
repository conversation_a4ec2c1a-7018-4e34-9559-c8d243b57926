package com.jackrain.nea.ps.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.core.db.Tools;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.CclrgroupAddCmd;
import com.jackrain.nea.ps.entity.CbrandEntity;
import com.jackrain.nea.ps.entity.CspecgroupEntity;
import com.jackrain.nea.ps.entity.CspecobjEntity;
import com.jackrain.nea.ps.mapper.CbrandMapper;
import com.jackrain.nea.ps.mapper.CclrgroupMapper;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * 颜色组及颜色新增service
 */

@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps")
public class CclrgroupAddCmdImp extends CommandAdapter implements CclrgroupAddCmd {
    @Autowired
    private CbrandMapper cbrandMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {

        CclrgroupMapper cclrgroupMapper = ApplicationContextHandle.getBean(CclrgroupMapper.class);
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        JSONObject superTable = fixColumn.getJSONObject("PS_C_CLRGROUP");
        JSONArray subTableArry = fixColumn.getJSONArray("PS_C_CLR");
        //主表不为空
        long colSpecId = param.getLong("objid");
        JSONObject jo = new JSONObject();
        //新增判断ID
        if (colSpecId <= 0) {

            Long brandId = superTable.getLong("PS_C_BRAND_ID");
            //品牌字段是否存在品牌表中
            int brandCount = cclrgroupMapper.findBrandCount(brandId);
            if (brandCount > 0) {
                CbrandEntity cbrandEntity = cclrgroupMapper.findBrandByName(brandId);
                String isactive = "N";
                if (isactive.equals(cbrandEntity.getIsactive())) {
                    throw new NDSException(Resources.getMessage("输入的品牌已停用", querySession.getLocale()));
                }
            } else {
                throw new NDSException(Resources.getMessage("输入的品牌不存在", querySession.getLocale()));
            }
            //控制规格ID+名称唯一
            long specId = cclrgroupMapper.findSpeceByFlag(1);
            int enameCount = cclrgroupMapper.findenameCount(specId, superTable.getString("ENAME"));
            if (enameCount > 0) {
                throw new NDSException(Resources.getMessage("输入的数据已存在：名称", querySession.getLocale()));
            }
            //规格id+品牌id唯一
            int specCount = cclrgroupMapper.findSpecCount(specId, brandId);
            if (specCount > 0) {
                //只含有主表状态时
                throw new NDSException(Resources.getMessage("输入的数据已存在：品牌", querySession.getLocale()));
            } else {

                Timestamp timestamp = new Timestamp(System.currentTimeMillis());
                CspecgroupEntity specgroupEntity = new CspecgroupEntity();
                specgroupEntity.setAd_client_id(Integer.valueOf(querySession.getUser().getClientId()).longValue());
                specgroupEntity.setAd_org_id(Integer.valueOf(querySession.getUser().getOrgId()).longValue());
                specgroupEntity.setPs_c_spec_id(specId);
                specgroupEntity.setPs_c_brand_id(brandId);
                specgroupEntity.setEname(superTable.getString("ENAME"));
                specgroupEntity.setRemark(superTable.getString("REMARK"));
                specgroupEntity.setOwnerid(Integer.valueOf(querySession.getUser().getId()).longValue());
                specgroupEntity.setCreationdate(timestamp);
                specgroupEntity.setModifierid(Integer.valueOf(querySession.getUser().getId()).longValue());
                specgroupEntity.setModifieddate(timestamp);
                specgroupEntity.setIsactive(superTable.getString("ISACTIVE"));
                String isactive = superTable.getString("ISACTIVE");
                if (isactive == null || "".equals(isactive)) {
                    specgroupEntity.setIsactive("Y");
                }
                specgroupEntity.setOwnername(querySession.getUser().getName());
                specgroupEntity.setOwnerename(querySession.getUser().getEname());
                specgroupEntity.setModifiername(querySession.getUser().getName());
                specgroupEntity.setModifierename(querySession.getUser().getEname());
                colSpecId = Tools.getSequence("PS_C_SPECGROUP");
                specgroupEntity.setId(colSpecId);
                specgroupEntity.setEcode(superTable.getString("ECODE"));

                int insertRes = cclrgroupMapper.insertSpec(specgroupEntity);

                if (insertRes > 0) {
                    //插入成功
                    jo.put("tablename", "PS_C_CLRGROUP");
                    jo.put("objid", colSpecId);
                    vh.put("data", jo);

                } else {
                    throw new NDSException(Resources.getMessage("颜色组表插入失败", querySession.getLocale()));
                }
            }
        }
        //新增子表
        JSONArray itemIdArray = new JSONArray();
        JSONArray dataArrayJson = new JSONArray();
        if (subTableArry != null && subTableArry.size() > 0) {

            for (int i = 0; i < subTableArry.size(); i++) {
                Long itemId = null;
                try {
                    //新增明细
                    JSONObject subTable = subTableArry.getJSONObject(i);
                    if (subTable.getString("ECODE") == null) {
                        //颜色编码不能为空
                        throw new NDSException(Resources.getMessage("编码不能为空", querySession.getLocale()));
                    }
                    if (subTable.getString("ENAME") == null) {
                        // 颜色名称不能为空
                        throw new NDSException(Resources.getMessage("名称不能为空", querySession.getLocale()));
                    }
                    List<String> cloCount = cclrgroupMapper.findClrStr(colSpecId, subTable.getString("ECODE"));
                    if (CollectionUtils.isNotEmpty(cloCount)) {
                        //输入的数据已存在：颜色编码
                        throw new NDSException(Resources.getMessage("输入的数据已存在：颜色编码", querySession.getLocale()));
                    }
                    //查询规格表中的规格ID
                    Long specId = cclrgroupMapper.findSpeceByFlag(1);

                    String eCode = subTable.getString("ECODE");
                    String eName = subTable.getString("ENAME");

                    CspecobjEntity cspecobjEntity = new CspecobjEntity();
                    Timestamp timestamp = new Timestamp(System.currentTimeMillis());
                    Long brandId = superTable.getLong("PS_C_BRAND_ID");
                    //获取颜色组编码
                    String cgroupEcode = superTable.getString("ECODE");
                    String brandName = this.cbrandMapper.selectEnameByBrandId(brandId);
                    cspecobjEntity.setBrandname(brandName + "-" + cgroupEcode + "-" + eCode + "-" + eName);
                    cspecobjEntity.setAd_client_id(Integer.valueOf(querySession.getUser().getClientId()).longValue());
                    cspecobjEntity.setAd_org_id(Integer.valueOf(querySession.getUser().getOrgId()).longValue());
                    cspecobjEntity.setPs_c_spec_id(specId);
                    cspecobjEntity.setPs_c_specgroup_id(colSpecId);
                    cspecobjEntity.setEcode(eCode);
                    cspecobjEntity.setEname(eName);
                    cspecobjEntity.setMixname("[" + eCode + "]" + eName);
                    cspecobjEntity.setRemark(subTable.getString("REMARK"));
                    cspecobjEntity.setOwnerid(Integer.valueOf(querySession.getUser().getId()).longValue());
                    cspecobjEntity.setModifierid(Integer.valueOf(querySession.getUser().getId()).longValue());
                    cspecobjEntity.setCreationdate(timestamp);
                    cspecobjEntity.setModifieddate(timestamp);
                    cspecobjEntity.setIsactive(subTable.getString("ISACTIVE"));
                    cspecobjEntity.setPs_c_clrseries_id(subTable.getLong("PS_C_CLRSERIES_ID"));
                    if (subTable.getString("ISACTIVE") == null || "".equals(subTable.getString("ISACTIVE"))) {
                        cspecobjEntity.setIsactive("Y");
                    }
                    cspecobjEntity.setOwnername(querySession.getUser().getName());
                    cspecobjEntity.setOwnerename(querySession.getUser().getEname());
                    cspecobjEntity.setModifiername(querySession.getUser().getName());
                    cspecobjEntity.setModifierename(querySession.getUser().getEname());
                    Long id = Tools.getSequence("PS_C_SPECOBJ");
                    cspecobjEntity.setId(id);
                    int insertRes = cclrgroupMapper.insertColor(cspecobjEntity);
                    if (insertRes > 0) {
                        itemIdArray.add(cspecobjEntity.getId());
                        //更新主表
                        JSONObject updateObj = new JSONObject();
                        updateObj.put("MODIFIEDDATE", timestamp);
                        updateObj.put("MODIFIERID", Integer.valueOf(querySession.getUser().getId()).longValue());
                        updateObj.put("MODIFIERNAME", querySession.getUser().getName());
                        updateObj.put("MODIFIERENAME", querySession.getUser().getEname());
                        updateObj.put("ID", colSpecId);
                        cclrgroupMapper.updateSpecgroup(updateObj);

                    } else {
                        throw new NDSException(Resources.getMessage("颜色明细插入失败", querySession.getLocale()));
                    }

                } catch (NDSException e) {
                    // 将错误信息存入dataArrayJson中
                    dataArrayJson.add(getData(e, itemId));
                }
            }
        }

        JSONObject ret = new JSONObject();
        ret.put("objid", colSpecId);
        if (!itemIdArray.isEmpty()) {
            ret.put("PS_C_CLR", itemIdArray);
        }
        vh.put("ret", ret);
        if (dataArrayJson.size() > 0) {
            Long parmid = param.getLong("objid");
            if (parmid < 0) {
                jo.put("tablename", "PS_C_CLRGROUP");
                jo.put("objid", colSpecId);
                jo.put("error", dataArrayJson);
                vh.put("data", jo);
            } else {
                vh.put("data", dataArrayJson);
            }
            vh.put("code", -1);
            vh.put("message", Resources.getMessage("失败", querySession.getLocale()));

        } else {

            vh.put("code", 0);
            vh.put("message", Resources.getMessage("成功", querySession.getLocale()));

        }
        return vh;
    }

    /**
     * 获取明细表错误信息并插入dataJson
     *
     * @param e
     * @param itemId
     * @return
     */
    private JSONObject getData(NDSException e, Long itemId) {
        JSONObject dataJson = new JSONObject();
        dataJson.put("id", itemId);
        dataJson.put("code", -1);
        dataJson.put("message", e.getMessage());
        return dataJson;
    }

}
