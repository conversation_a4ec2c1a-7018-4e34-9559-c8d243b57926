package com.jackrain.nea.ps.mapper;

import com.alibaba.fastjson.JSONObject;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * <AUTHOR>
 * 商品上下架
 */
@Mapper
public interface CproUpDownMapper {

    /**
     * 判断此商品是否存在
     *
     * @param id
     * @return
     */
    @Select("SELECT ECODE FROM PS_C_PRO WHERE ID=#{id}")
    String repeatPro(@Param("id") Long id);

    /**
     * 标准商品下架操作
     *
     * @param proJson
     * @return
     */
    @Update("UPDATE PS_C_PRO SET ISUP='N',MODIFIEDDATE=#{MODIFIEDDATE},MODIFIERID=#{MODIFIERID},MODIFIERNAME=#{MODIFIERNAME},MODIFIERENAME=#{MODIFIERENAME} WHERE ID=#{ID} AND ISUP!='N'")
    Integer down(JSONObject proJson);

    /**
     * 配销商品下架操作
     *
     * @param proJson
     * @return
     */
    @Update("UPDATE PS_C_PRO SET ISUP='N',ISSELECTION='N',MODIFIEDDATE=#{MODIFIEDDATE},MODIFIERID=#{MODIFIERID},MODIFIERNAME=#{MODIFIERNAME},MODIFIERENAME=#{MODIFIERENAME} WHERE ECODE=#{ECODE} AND CP_C_DISTRIB_ID !=0")
    Integer downDis(JSONObject proJson);


    /**
     * 标准商品上架操作
     *
     * @param proJson
     * @return
     */
    @Update("UPDATE PS_C_PRO SET ISUP='Y',MODIFIEDDATE=#{MODIFIEDDATE},MODIFIERID=#{MODIFIERID},MODIFIERNAME=#{MODIFIERNAME},MODIFIERENAME=#{MODIFIERENAME},DATEONSHELF=#{DATEONSHELF} WHERE ID=#{ID} AND ISUP!='Y'")
    Integer up(JSONObject proJson);

    /**
     * 配销商品上架操作
     *
     * @param proJson
     * @return
     */
    @Update("UPDATE PS_C_PRO SET ISUP='Y',MODIFIEDDATE=#{MODIFIEDDATE},MODIFIERID=#{MODIFIERID},MODIFIERNAME=#{MODIFIERNAME},MODIFIERENAME=#{MODIFIERENAME},DATEONSHELF=#{DATEONSHELF} WHERE ECODE=#{ECODE} AND CP_C_DISTRIB_ID !=0")
    Integer upDis(JSONObject proJson);

}
