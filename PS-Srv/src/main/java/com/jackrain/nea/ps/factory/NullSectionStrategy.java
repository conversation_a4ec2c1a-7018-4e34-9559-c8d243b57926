package com.jackrain.nea.ps.factory;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.web.face.User;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 2018-2-12
 */
@Component
public class NullSectionStrategy implements SectionStrategy {
    @Override
    public Object queryData(String param) {
        return null;
    }

    @Override
    public void saveSection(JSONObject csectionEntity, Long objid, User user) {

    }
}
