package com.jackrain.nea.ps.mapper;

import com.alibaba.fastjson.JSONObject;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.jdbc.SQL;

import java.util.HashMap;
import java.util.Set;

/**
 * 号型组的Mapper
 *
 * <AUTHOR>
 * @date 2017/9/26.
 */
@Mapper
public interface CshapegroupMapper {


    class CshapegroupProvider {
        public String updategroup(final JSONObject jsonObject) {
            return new SQL() {
                {
                    Set<String> keySet = jsonObject.keySet();
                    UPDATE("PS_C_SHAPEGROUP ");
                    for (int i = 0; i < jsonObject.size(); i++) {
                        String key = (String) keySet.toArray()[i];
                        if ("ID".equals(key)) {
                            WHERE("ID=#{" + key + "}");
                        } else {
                            SET(key + "= #{" + key + "}");
                        }
                    }
                }
            }.toString();
        }

        public String pshapegroupinsert(JSONObject jo) {
            return new SQL() {
                {
                    INSERT_INTO("PS_C_SHAPEGROUP ");
                    for (String key : jo.keySet()) {
                        VALUES(key, "#{" + key + "}");
                    }
                }
            }.toString();
        }


    }

    @InsertProvider(type = CshapegroupMapper.CshapegroupProvider.class, method = "pshapegroupinsert")
    int insertgroup(JSONObject jsonObject);

    @UpdateProvider(type = CshapegroupProvider.class, method = "updategroup")
    int groupupdate(JSONObject jsonObject);

    @Select("select ENAME,PS_C_SIZEGROUP_ID,ISACTIVE from PS_C_SHAPEGROUP  where ID=#{id}")
    HashMap findgroupcolumn(@Param("id") Long id);

    @Delete("DELETE FROM PS_C_SHAPEGROUP WHERE ID=#{id}")
    int deletecshapegroup(@Param("id") Long id);

    @Select("select count(ID) from PS_C_PRO  where PS_C_SHAPEGROUP_ID=#{id}")
    Integer findprogroupnum(@Param("id") Long id);

    @Select("select Count(ID) from PS_C_SHAPEGROUP where ENAME=#{ename}  and AD_CLIENT_ID=#{ad_Client_Id}")
    Integer findgroupenamecount(@Param("ename") String ename, @Param("ad_Client_Id") Long ad_Client_Id);

    @Select("select PS_C_SIZEGROUP_ID from ps_c_shapegroup where ID=#{id}  and AD_CLIENT_ID=#{ad_Client_Id}")
    HashMap findgroudsizegroudid(@Param("id") Long id, @Param("ad_Client_Id") Long ad_Client_Id);

    @Select("select  ISACTIVE from ps_c_shapegroup where id=#{id}")
    HashMap findisavtive(@Param("id") Long id);

    @Select("select count(ID) from ps_c_shapegroup where id=#{id}")
    Integer findgroupcountall(@Param("id") Long id);

    @Select("select count(ID) from ps_c_shapegroup where id!=#{id} and AD_CLIENT_ID=#{ad_Client_Id} and ENAME=#{ename}")
    Integer groupshselcount(@Param("id") Long id, @Param("ad_Client_Id") Long ad_Client_Id, @Param("ename") String ename);

    @Select("select count(ID) from ps_c_shapegroup where id!=#{id} and AD_CLIENT_ID=#{ad_Client_Id} and ENAME=#{ename}")
    Integer groupenamecount(@Param("id") Long id, @Param("ad_Client_Id") Long ad_Client_Id, @Param("ename") String ename);

    @Select("select count(ID) from ps_c_specgroup where id=#{id} ")
    Integer pspecgroupcount(@Param("id") Long id);

    @Select("select ENAME from ps_c_shapegroup where id=#{id}")
    HashMap findnamegroupch(@Param("id") Long id);

    @Select("SELECT ISACTIVE FROM PS_C_SHAPEGROUP WHERE ID=#{id}")
    JSONObject  getPscShapeGroupById(@Param("id") Long id);

    @Select("SELECT COUNT(1) FROM PS_C_SHAPE WHERE PS_C_SHAPEGROUP_ID = #{cshapegroupid}")
    long selectNumByShapeGrpopId(@Param("cshapegroupid") Long cshapegroupid);
}
