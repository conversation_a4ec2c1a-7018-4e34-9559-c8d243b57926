package com.jackrain.nea.ps.mapper;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.jdbc.SQL;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 商品明细搭配Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CcollocationItemMapper {

    String FIELDS = "ID, AD_CLIENT_ID, AD_ORG_ID, ISACTIVE, PS_C_COLLOCATION_ID, " +
            "CODERULE, ECODE, ENAME, ISSYSTEM, COLLOCATION, " +
            "IMAGE, TYPE, CATEGORY, GENDER, SEASON, " +
            "FAB, OWNERID, OWNERNAME, OWNERENAME, CREATIONDATE, " +
            "MODIFIERID, MODIFIERNAME, MODIFIERENAME, MODIFIEDDATE, PS_C_PRO_ID";

    /**
     * 新增
     *
     * @param jsonObject 参数对象
     */
    @InsertProvider(type = CcollocationItemSql.class, method = "insert")
    int insert(@Param("jsonObject") JSONObject jsonObject);

    /**
     * 修改
     *
     * @param jsonObject 参数对象
     */
    @InsertProvider(type = CcollocationItemSql.class, method = "update")
    int update(@Param("jsonObject") JSONObject jsonObject);

    /**
     * 修改当前编码的数据为非强制搭配
     *
     * @param code 参数对象
     * @param id   商品搭配ID
     */
    @Update("UPDATE PS_C_COLLOCATION_ITEM SET COLLOCATION = 'Y' WHERE ECODE = #{code} AND PS_C_COLLOCATION_ID = #{id}")
    int updateCollocationByCode(@Param("code") String code, @Param("id") Long id);

    /**
     * 删除
     *
     * @param id 主键ID
     */
    @Delete("DELETE FROM PS_C_COLLOCATION_ITEM WHERE ID = #{id}")
    int delete(@Param("id") Long id);

    /**
     * 删除所有系统生成款
     */
    @Delete("DELETE FROM PS_C_COLLOCATION_ITEM WHERE ISSYSTEM = 'Y'")
    int deleteBySystem();

    /**
     * 删除某系统生成款
     */
    @Delete("DELETE FROM PS_C_COLLOCATION_ITEM WHERE ISSYSTEM = 'Y' AND ECODE = #{code}")
    int deleteBySystemAndCode(@Param("code") String code);

    /**
     * 删除搭配款下所有数据
     */
    @Delete("DELETE FROM PS_C_COLLOCATION_ITEM WHERE PS_C_COLLOCATION_ID = #{id}")
    int deleteByCollocationId(@Param("id") Long id);

    /**
     * 根据ID查询
     *
     * @param id 主键ID
     */
    @Select("SELECT " + FIELDS + " FROM PS_C_COLLOCATION_ITEM WHERE ID = #{id}")
    HashMap<String, Object> getById(@Param("id") Long id);

    /**
     * 根据商品编码和强制搭配查询
     *
     * @param code 商品编码
     */
    @Select("SELECT " + FIELDS + " FROM PS_C_COLLOCATION_ITEM WHERE ECODE = #{code} AND COLLOCATION = 'Y'")
    HashMap<String, Object> getByCodeAndCollocation(@Param("code") String code);

    /**
     * 根据商品搭配查询商品编码
     *
     * @param id 商品搭配主键ID
     */
    @Select("SELECT ECODE FROM PS_C_COLLOCATION_ITEM WHERE PS_C_COLLOCATION_ID = #{id}")
    List<String> getCodeByFId(@Param("id") Long id);

    /**
     * 根据商品搭配查询商品编码
     *
     * @param id 商品搭配主键ID
     */
    @Select("SELECT item.ID,ECODE,ENAME,PS_C_PRO_ID,PS_C_COLLOCATION_ID,item.ISSYSTEM FROM PS_C_COLLOCATION_ITEM as item " +
            "LEFT JOIN PS_C_COLLOCATION as main ON main.ISACTIVE = 'Y' and main.ID = item.PS_C_COLLOCATION_ID WHERE PS_C_COLLOCATION_ID = #{id}")
    ArrayList<HashMap<String, Object>> listItemInfoByFIdAndActive(@Param("id") Long id);

    /**
     * 根据商品搭配规则查询商品编码，产品线
     */
    String PROFIELDS = "ID,ECODE,ENAME,PROLINE,IMAGE,NUMDIM6,LARGECLASS,SEX,PROSEA,FABDESC";
    String GETCODESQL = "SELECT " + PROFIELDS + " " +
            "FROM PS_C_PRO " +
            "where LENGTH(ECODE) in(6,7) " +
            "AND CP_C_DISTRIB_ID = 0 " +
            "AND PROLINE in (" +
            "Select ID from PS_C_PRODIM_ITEM where exists (" +
            "select 1 from PS_C_PRODIM " +
            "where PS_C_PRODIM_ITEM.PS_C_PRODIM_ID=PS_C_PRODIM.ID AND PS_C_PRODIM.AD_PROCOLUMN_NAME='PROLINE' " +
            "AND ps_c_prodim_item.ENAME in ('情侣', '家庭'))" +
            ")" +
            "ORDER BY ID DESC;";

    @Select(GETCODESQL)
    List<HashMap<String, Object>> getProCodeOnBaseRule();

    /**
     * 查询产品线编码为情侣|家庭的产品线信息
     */
    String GETPROLINESQL = "Select ID,ENAME " +
            "from PS_C_PRODIM_ITEM where exists (" +
            "select 1  from PS_C_PRODIM where " +
            "PS_C_PRODIM_ITEM.PS_C_PRODIM_ID=PS_C_PRODIM.ID " +
            "AND PS_C_PRODIM.AD_PROCOLUMN_NAME='PROLINE' " +
            "AND ps_c_prodim_item.ENAME in ('情侣', '家庭'))";

    @Select(GETPROLINESQL)
    List<HashMap<String, Object>> getProLine();

    /**
     * 根据商品规则查询强制搭配款的ID
     *
     * @param key 商品搭配
     */
    @Select("SELECT DISTINCT PS_C_COLLOCATION_ID FROM PS_C_COLLOCATION_ITEM WHERE ISSYSTEM = 'Y' AND CODERULE = #{key} limit 0,1")
    Long getCollocationIdByKey(@Param("key") String key);

    /**
     * 根据商品规则查询强制搭配款的ID
     *
     * @param code 商品编码
     */
    @Select("SELECT DISTINCT PS_C_COLLOCATION_ID FROM PS_C_COLLOCATION_ITEM WHERE ECODE = #{code}")
    List<Long> listCollocationIdsByCode(@Param("code") String code);

    /**
     * 根据商品编码查询强制搭配款的ID
     *
     * @param code 商品编码
     */
    @Select("SELECT DISTINCT PS_C_COLLOCATION_ID FROM PS_C_COLLOCATION_ITEM WHERE ECODE like CONCAT('%',#{code},'%')")
    ArrayList<Long> listCollocationIdsByCodeLike(@Param("code") String code);

    /**
     * 根据商品名臣称查询强制搭配款的ID
     *
     * @param code 商品编码
     */
    @Select("SELECT DISTINCT PS_C_COLLOCATION_ID FROM PS_C_COLLOCATION_ITEM WHERE ECODE like CONCAT('%',#{name},'%')")
    ArrayList<Long> listCollocationIdsByNameLike(@Param("code") String code);

    @Select("SELECT DISTINCT PS_C_COLLOCATION_ID,CODERULE FROM PS_C_COLLOCATION_ITEM WHERE ISSYSTEM = 'Y'")
    ArrayList<HashMap<String, Object>> listDistribIdOnSystem();

    @Select("SELECT ID,PS_C_COLLOCATION_ID,ECODE,ENAME FROM PS_C_COLLOCATION_ITEM WHERE COLLOCATION = 'Y'")
    ArrayList<HashMap<String, Object>> listOnCollocation();

    @Select("SELECT PS_C_PRO_ID FROM PS_C_COLLOCATION_ITEM WHERE PS_C_COLLOCATION_ID = #{id}")
    List<Long> listProIdsByCollocationId(@Param("id") Long id);

    @Select("SELECT " + FIELDS + " FROM PS_C_COLLOCATION_ITEM WHERE PS_C_COLLOCATION_ID = #{id}")
    List<HashMap<String, Object>> listByCollocationId(@Param("id") Long id);

    @Select("SELECT COUNT(ID) FROM PS_C_COLLOCATION_ITEM WHERE PS_C_COLLOCATION_ID = #{id}")
    int getCountByCollocationId(@Param("id") Long id);

    @Select("SELECT COUNT(ID) FROM PS_C_COLLOCATION_ITEM WHERE PS_C_COLLOCATION_ID = #{collocationId} AND PS_C_PRO_ID = #{proId}")
    int getCountByCollocationIdAndProId(@Param("collocationId") Long collocationId, @Param("proId") Long proId);

    @Select("SELECT DISTINCT PS_C_PRO_ID FROM PS_C_COLLOCATION_ITEM WHERE ISSYSTEM='Y'")
    List<Long> listProIdBySystem();

    @UpdateProvider(type = CcollocationItemSql.class, method = "executeSql")
    int executeSql(@Param("sql") String sql);

    @SelectProvider(type = CcollocationItemSql.class, method = "executeSql")
    ArrayList<Long> querySql(@Param("sql") String sql);

    @Select("SELECT DISTINCT PS_C_COLLOCATION_ID FROM PS_C_COLLOCATION_ITEM WHERE PS_C_PRO_ID IN (#{ids})")
    List<Long> listCollocationIdsByProIds(@Param("ids") String ids);

    @InsertProvider(type = CcollocationItemSql.class, method = "saveBatch")
    void saveBatch(@Param("data") JSONArray array);

    @Select("SELECT " + FIELDS + " FROM PS_C_COLLOCATION_ITEM WHERE PS_C_COLLOCATION_ID = #{ids} LIMIT 0,1")
    HashMap<String, Object> getByCollocationId(@Param("id") Long id);

    @SelectProvider(type = CcollocationItemSql.class, method = "findByProList")
    List<HashMap> findByProList(@Param("jsonObject") JSONObject jsonObject);

    class CcollocationItemSql {
        public String findByProList(JSONObject jsonObject) {
            return new SQL() {
                {
                    StringBuilder proEcodeSb = new StringBuilder();
                    JSONArray proEcodeList = jsonObject.getJSONArray("proEcodeList");
                    int size = CollectionUtils.size(proEcodeList);
                    for (int i = 0; i < size; i++) {
                        proEcodeSb.append("'");
                        proEcodeSb.append(proEcodeList.getString(i));
                        proEcodeSb.append("'");
                        if (i < size - 1) {
                            proEcodeSb.append(",");
                        }
                    }

                    SELECT("PS_C_COLLOCATION_ID, " +
                            "  ID, " +
                            "  ECODE ");
                    FROM("PS_C_COLLOCATION_ITEM");
                    WHERE("ECODE IN (" + proEcodeSb.toString() + ")");
                }
            }.toString();
        }

        public String update(JSONObject jsonObject) {
            return new SQL() {
                {
                    UPDATE("PS_C_COLLOCATION_ITEM");
                    for (String key : jsonObject.keySet()) {
                        if (!"ID".equals(key)) {
                            SET(key + " = #{" + key + "}");
                        }
                    }
                    WHERE("ID=#{ID}");
                }
            }.toString();
        }

        public String insert(JSONObject jsonObject) {
            return new SQL() {
                {
                    INSERT_INTO("PS_C_COLLOCATION_ITEM");
                    for (String key : jsonObject.keySet()) {
                        VALUES(key, "#{" + key + "}");
                    }
                }
            }.toString();
        }

        public String executeSql(String sql) {
            return sql;
        }

        public String saveBatch(Map map) {
            JSONArray array = (JSONArray) map.get("data");
            // 插入字段
            JSONArray keyArr = new JSONArray();
            keyArr.add("ID");
            keyArr.add("AD_CLIENT_ID");
            keyArr.add("AD_ORG_ID");
            keyArr.add("PS_C_COLLOCATION_ID");
            keyArr.add("PS_C_PRO_ID");
            keyArr.add("ECODE");
            keyArr.add("ENAME");
            keyArr.add("ISSYSTEM");
            keyArr.add("COLLOCATION");
            keyArr.add("CODERULE");
            keyArr.add("TYPE");
            keyArr.add("CATEGORY");
            keyArr.add("GENDER");
            keyArr.add("SEASON");
            keyArr.add("IMAGE");
            keyArr.add("FAB");
            keyArr.add("OWNERID");
            keyArr.add("OWNERNAME");
            keyArr.add("OWNERENAME");
            keyArr.add("CREATIONDATE");
            keyArr.add("MODIFIERID");
            keyArr.add("MODIFIERNAME");
            keyArr.add("MODIFIERENAME");
            keyArr.add("MODIFIEDDATE");

            StringBuilder sb = new StringBuilder();
            sb.append("INSERT INTO PS_C_COLLOCATION_ITEM");
            sb.append("(ID");
            for (Object key : keyArr) {
                if (!"ID".equals(key.toString())) {
                    sb.append("," + key.toString());
                }
            }
            sb.append(")");
            sb.append("VALUES");
            for (Object o : array) {
                if (sb.toString().endsWith(")")) {
                    sb.append(",");
                }
                sb.append("(");
                JSONObject object = JSON.parseObject(o.toString());
                sb.append(object.getString("ID"));
                for (Object key : keyArr) {
                    if (!"ID".equals(key.toString())) {
                        if (object.containsKey(key.toString())) {
                            sb.append(",'" + object.getString(key.toString()) + "'");
                        } else {
                            sb.append(",NULL");
                        }
                    }
                }
                sb.append(")");
            }
            sb.append(" ON DUPLICATE KEY UPDATE ENAME=values(ENAME),IMAGE=values(IMAGE),TYPE=values(TYPE)," +
                    "CATEGORY=values(CATEGORY),GENDER=values(GENDER),SEASON=values(SEASON),FAB=values(FAB)," +
                    "MODIFIERID=values(MODIFIERID),MODIFIERNAME=values(MODIFIERNAME),MODIFIERENAME=values(MODIFIERENAME)," +
                    "MODIFIEDDATE=values(MODIFIEDDATE) ");
            return sb.toString();
        }

    }
}
