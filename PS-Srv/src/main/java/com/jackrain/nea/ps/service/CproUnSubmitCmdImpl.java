package com.jackrain.nea.ps.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.CproUnSubmitCmd;
import com.jackrain.nea.ps.mapper.CproMapper;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;

/**
 * 商品中心-12标准商品档案-基本信息维护-取消提交
 * 功能已作废 20180103
 *
 * <AUTHOR>
 * @create 2017/10/9
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps")
public class CproUnSubmitCmdImpl extends CommandAdapter implements CproUnSubmitCmd {
    @Override
    public ValueHolder execute(QuerySession querySession) {
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        // Mapper
        CproMapper cproMapper = ApplicationContextHandle.getBean(CproMapper.class);

        // 查询【标准商品档案】表的当前记录是否存在
        int count = cproMapper.isExist(param.getLong("objid"));
        if (count == 0) {
            throw new NDSException(Resources.getMessage("当前记录已不存在！", querySession.getLocale()));
        }

        // 查询当前记录【状态】
        Long status = cproMapper.getStatus(param.getLong("objid"));
        if (status == 1) {
            throw new NDSException(Resources.getMessage("当前记录未提交，不允许取消提交！", querySession.getLocale()));
        }

        // 取消提交本记录
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("ID", param.getLong("objid"));
        jsonObject.put("STATUS", 1);
        jsonObject.put("STATUSERID", null);
        jsonObject.put("STATUSTIME", null);
        jsonObject.put("STATUSERNAME", null);
        jsonObject.put("STATUSERENAME", null);
        jsonObject.put("MODIFIERID", querySession.getUser().getId());
        jsonObject.put("MODIFIERNAME", querySession.getUser().getName());
        jsonObject.put("MODIFIERENAME", querySession.getUser().getEname());
        jsonObject.put("MODIFIEDDATE", new Timestamp(System.currentTimeMillis()));
        cproMapper.updateCpro(jsonObject);

        // 查询取消提交成功款号
        String ecode = cproMapper.getEcode(param.getLong("objid"));

        vh.put("code", 0);
        vh.put("message", ecode + Resources.getMessage("已取消提交成功！", querySession.getLocale()));
        return vh;
    }
}
