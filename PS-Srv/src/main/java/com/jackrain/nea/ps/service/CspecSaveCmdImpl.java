package com.jackrain.nea.ps.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.core.db.Tools;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.CspecSaveCmd;
import com.jackrain.nea.ps.mapper.CspecMapper;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2017/10/16
 */
@Slf4j
@Component
@Service(protocol="dubbo", validation="true", version="1.0", group="ps")
public class CspecSaveCmdImpl extends CommandAdapter implements CspecSaveCmd {
    @Autowired
    private PropertiesConf propertiesConf;

    @Override
    public ValueHolder execute(HashMap map) throws NDSException {
        ValueHolder vh = new ValueHolder();
        vh.put("code",0);
        vh.put("vhmessage","Hello EDAS Test");
        return vh;
    }

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        //获取对应mapper对象
        CspecMapper cspecMapper = ApplicationContextHandle.getBean(CspecMapper.class);
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        //转换时间格式参数
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),"yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),Feature.OrderedField);
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        //以jsonobject形式获取表内容
        JSONObject jo = fixColumn.getJSONObject("PS_C_SPEC");
        //主表不为空
        if(jo != null){
            //封装实体类对象

            //判断名称是否存在
            Long enameId=cspecMapper.selectIdByEname(jo.getString("ENAME"));

            if(param.getLong("objid") != null && param.getLong("objid") > 0){
                //判断该条记录是否不存在了
                long id=param.getLong("objid");
                int count2=cspecMapper.selectid(id);
                if(count2==0){
                    throw new NDSException(Resources.getMessage("当前记录已不存在！",querySession.getLocale()));

                }

                //如果这条记录的flag不为0的话，提示不允许修改这条记录
                long flag=cspecMapper.selectflag(param.getLong("objid"));
                if(flag!=0){
                    String ename=cspecMapper.selectenamebyid(param.getLong("objid"));
                    throw new NDSException(Resources.getMessage(ename+"为系统保留数据，不允许修改！",querySession.getLocale()));
                }
                if(enameId!=null&&!enameId.equals(param.getLong("objid"))){
                    throw new NDSException(Resources.getMessage("输入的数据已存在：名称",querySession.getLocale()));

                }
                boolean f=jo.containsKey("FLAG")&&(jo.getLong("FLAG")==1||jo.getLong("FLAG")==2);
                if(f){
                    throw new NDSException(Resources.getMessage("FALG不允许修改为1或2",querySession.getLocale()));

                }

                jo.put("ID",param.getLong("objid"));
                jo.put("MODIFIERID",Integer.valueOf(querySession.getUser().getId()).longValue());
                jo.put("MODIFIERNAME",querySession.getUser().getName());
                jo.put("MODIFIERENAME",querySession.getUser().getEname());
                jo.put("MODIFIEDDATE",new Timestamp(System.currentTimeMillis()));
                //执行更新操作
               cspecMapper.update(jo);

            }else if(param.getLong("objid")==-1){
                long id=Tools.getSequence("PS_C_SPEC");

                int count=cspecMapper.selectename(jo.getString("ENAME"));
                String ename=jo.getString("ENAME");

                if(count>0){
                    throw new NDSException(Resources.getMessage("输入的数据已存在：名称",querySession.getLocale()));

                }
                //执行插入操作
                jo.put("ID",id);
                jo.put("AD_CLIENT_ID",Integer.valueOf(querySession.getUser().getClientId()).longValue());
                jo.put("AD_ORG_ID",Integer.valueOf(querySession.getUser().getOrgId()).longValue());
                jo.put("MODIFIEDDATE",new Timestamp(System.currentTimeMillis()));
                jo.put("MODIFIERID",Integer.valueOf(querySession.getUser().getId()).longValue());
                jo.put("CREATIONDATE",new Timestamp(System.currentTimeMillis()));
                jo.put("OWNERID",Integer.valueOf(querySession.getUser().getId()).longValue());
                jo.put("OWNERNAME",querySession.getUser().getName());
                jo.put("OWNERENAME",querySession.getUser().getEname());
                jo.put("MODIFIERNAME",querySession.getUser().getName());
                jo.put("MODIFIERENAME",querySession.getUser().getEname());
                cspecMapper.insert(jo);
                JSONObject data=new JSONObject();
                data.put("objid",id);
                data.put("tablename","PS_C_SPEC");
                vh.put("data",data);
            }
        }

        //封装valueholder
        vh.put("code",0);
        vh.put("message",Resources.getMessage("成功",querySession.getLocale()));
        return vh;
    }

}
