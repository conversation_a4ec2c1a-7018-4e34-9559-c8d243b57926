package com.jackrain.nea.ps.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.common.ValueHolderUtils;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.core.db.Tools;
import com.jackrain.nea.data.basic.common.BasicConstants;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.CskuSaveCmd;
import com.jackrain.nea.ps.mapper.CProSkuQueryMapper;
import com.jackrain.nea.ps.mapper.CpCPhyWarehouseMapper;
import com.jackrain.nea.ps.mapper.CproMapper;
import com.jackrain.nea.ps.mapper.CshopMapper;
import com.jackrain.nea.ps.mapper.CskuMapper;
import com.jackrain.nea.ps.mapper.PsCSkuMapper;
import com.jackrain.nea.ps.mapper.PsCSkuPushWmsMidMapper;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * 标准商品档案-条码维护-新建
 * 接口
 *
 * <AUTHOR>
 * @create 2019/01/03
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps")
public class CskuSaveCmdImpl extends CommandAdapter implements CskuSaveCmd {
    @Autowired
    private CshopMapper cshopMapper;

    @Autowired
    private CproMapper cproMapper;

    @Autowired
    CProSkuQueryMapper proSkuQueryMapper;

    @Autowired
    PsCSkuPushWmsMidMapper psCSkuPushWmsMidMapper;

    @Autowired
    CpCPhyWarehouseMapper cpCPhyWarehouseMapper;

    @Autowired
    PsCSkuMapper psCSkuMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("标准商品档案保存入参：{}", "CskuSaveCmdImpl.execute"), param);
        }

        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("fixColumn：") + fixColumn);
        }

        // Mapper
        CskuMapper cskuMapper = ApplicationContextHandle.getBean(CskuMapper.class);
        Long objid = param.getLong("objid");
        // 判断大于0
        JSONObject jo = fixColumn.getJSONObject("PS_C_SKU");
        if (Objects.nonNull(jo)) {
            if (jo.containsKey("QTY_LOWSTOCK")) {
                Long qtyLowstock = jo.getLong("QTY_LOWSTOCK");
                if (qtyLowstock < 0) {
                    throw new NDSException(Resources.getMessage("输入的数值必须大于0！", querySession.getLocale()));
                }
            }
            if (jo.containsKey("QTY_SAFENUM")) {
                Long qtySafenum = jo.getLong("QTY_SAFENUM");
                if (qtySafenum < 0) {
                    throw new NDSException(Resources.getMessage("输入的数值必须大于0！", querySession.getLocale()));
                }
            }

            // 获取objid 并更新
            if (objid > 0) {
                // 更新
                updateTable(jo, querySession, cskuMapper, objid);
            } else {
                // 新建
                //判断颜色和尺寸
                checkClr(jo, querySession);
                checkSize(jo, querySession);
                ValueHolder valueHolder = insertTable(jo, querySession, cskuMapper, objid);
                objid = ((JSONObject) valueHolder.get("data")).getLong("objid");
            }
        }

        //新增第三方条码明细
        JSONArray thirdItemJos = fixColumn.getJSONArray("PS_C_SKU_THIRD_ITEM");
        insertThirdItem(thirdItemJos, querySession, cskuMapper, objid);

        //新增平台编码映射
        JSONArray platformCodes = fixColumn.getJSONArray("PS_C_SKU_PLATFORM_CODE");
        insertPlatformCode(platformCodes, querySession, cskuMapper, objid);

        return ValueHolderUtils.success("新增成功！",
                ValueHolderUtils.createAddErrorData("PS_C_SKU", objid, null));
    }

    /**
     * 新增平台编码映射
     * @param
     * @param querySession
     * @param cskuMapper
     * @param objid
     * @return
     */
    private void insertPlatformCode(JSONArray joArray, QuerySession querySession, CskuMapper cskuMapper, Long objid) {
        if (CollectionUtils.isNotEmpty(joArray)) {
            for (Object obj : joArray) {
                JSONObject jo = (JSONObject) obj;
                String tradeStoreEcode = jo.getString("TRADE_STORE_CODE");
                Long shopId = jo.getLong("CP_C_SHOP_ID");

                if(StringUtils.isEmpty(tradeStoreEcode) && Objects.isNull(shopId)){
                    throw new NDSException("请输入商家编码或店铺信息！");
                }
                //查询是否有重复
                JSONObject platformCode = cskuMapper.selectPlatformCodeByCondition(objid,tradeStoreEcode,shopId);
                if (Objects.nonNull(platformCode)) {
                    throw new NDSException("店铺和商家编码已存在，请勿重复新增！");
                }

                Long itemId = jo.getLong("ID");
                if (itemId < 0) {
                    Long id = Tools.getSequence("PS_C_SKU_PLATFORM_CODE");
                    Timestamp timestamp = new Timestamp(System.currentTimeMillis());
                    jo.put("ID", id);
                    jo.put("PS_C_SKU_ID", objid);
                    jo.put("OWNERID", querySession.getUser().getId().longValue());
                    jo.put("OWNERNAME", querySession.getUser().getName());
                    jo.put("CREATIONDATE", timestamp);
                    jo.put("MODIFIERID", querySession.getUser().getId().longValue());
                    jo.put("MODIFIERNAME", querySession.getUser().getName());
                    jo.put("MODIFIEDDATE", timestamp);
                    HashMap cpCShop = cshopMapper.findShopByName(jo.getLong("CP_C_SHOP_ID"));
                    jo.put("CP_C_SHOP_CODE", cpCShop.get("ecode"));
                    jo.put("CP_C_SHOP_TITLE", cpCShop.get("cp_c_shop_title"));
                    cskuMapper.insertSkuPlatformCode(jo);
                } else {
                    jo.put("MODIFIERID", querySession.getUser().getId().longValue());
                    jo.put("MODIFIERNAME", querySession.getUser().getName());
                    jo.put("MODIFIEDDATE", new Timestamp(System.currentTimeMillis()));
                    cskuMapper.updateSkuPlatformCode(jo);
                }
            }
        }
    }

    /**
     * 检查尺寸的合法性
     *
     * @param jo
     * @param querySession
     */
    private void checkSize(JSONObject jo, QuerySession querySession) {
        Long proId = jo.getLong("PS_C_PRO_ID");
        Long sizeId = jo.getLong("PS_C_SPEC2OBJ_ID");

        List<Long> sizeIds = this.cproMapper.selectSizeIdsByProId(proId);

        if (!(CollectionUtils.isNotEmpty(sizeIds) && sizeIds.contains(sizeId))) {
            throw new NDSException(Resources.getMessage("该商品编码对应的尺寸组中不存在此尺寸！", querySession.getLocale()));
        }
    }

    /**
     * 检查颜色的合法性
     *
     * @param jo
     * @param querySession
     */
    private void checkClr(JSONObject jo, QuerySession querySession) {
        Long proId = jo.getLong("PS_C_PRO_ID");
        Long clrId = jo.getLong("PS_C_SPEC1OBJ_ID");

        List<Long> clrIds = this.cproMapper.selectColrIdsByProId(proId);

        if (!(CollectionUtils.isNotEmpty(clrIds) && clrIds.contains(clrId))) {
            throw new NDSException(Resources.getMessage("(颜色维护到品牌)该颜色值不存在！", querySession.getLocale()));
        }
    }

    /**
     * 更新
     *
     * @param jo
     * @param querySession
     * @param cskuMapper
     * @param objid
     */
    private ValueHolder updateTable(JSONObject jo, QuerySession querySession, CskuMapper cskuMapper, Long objid) {
        // 常量
        String cpcDistribIdStr = "CP_C_DISTRIB_ID";
        String eCode = "ECODE";
        String shop = "CP_C_SHOP_ID";
        String gbcode = "GBCODE";

        //判断【国标码】键值是否存在,则为修改国标码 需重传WMS
        if (jo.containsKey(gbcode)) {
            jo.put("IS_TOWMS", 0);
            jo.put("IS_TO_DEMOGIC", "N");
            /*修改时也要判断国标码是否存在*/
            checkGBCode(objid, jo.getString(gbcode), cskuMapper, querySession);
        }

        // 判断【条码】键值是否存在
        if (jo.containsKey(eCode)) {
            // 判断【配销中心】+【条码】字段唯一
            checkEcode(jo.getString("ECODE"), cskuMapper, querySession);
        }

        // 获取平台店铺信息
        if (jo.containsKey(shop) && !StringUtils.isEmpty(jo.containsKey(shop))) {
            HashMap shopResult = cshopMapper.findShopByName(jo.getLong(shop));
            if (shopResult != null) {
                jo.put("CP_C_SHOP_ID", jo.getLong(shop));
                jo.put("CP_C_SHOP_TITLE", shopResult.get("cp_c_shop_title"));
            }
        } else if (jo.containsKey(shop) && StringUtils.isEmpty(jo.containsKey(shop))) {
            jo.put("CP_C_SHOP_ID", "");
            jo.put("CP_C_SHOP_TITLE", "");
        }

        //先清除redis缓存
        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
        List<String> keys = new ArrayList<>();
        JSONObject csku = cskuMapper.selectSku(objid);
        if (csku != null) {
            String key = BasicConstants.CP_SKU_ID_KEY + objid;
            String ecodeKey = BasicConstants.CP_SKU_ECODE_KEY + csku.get("ECODE");
            String proKey = BasicConstants.CP_PRO_ID_KEY + csku.get("PS_C_PRO_ID");
            String proEcodeKey = BasicConstants.CP_PRO_ECODE_KEY + csku.get("PS_C_PRO_ECODE");
            String skuIdKey = BasicConstants.REDIS_PS_PRODUCT_SKUID + objid;
            String skuCodeKey = BasicConstants.REDIS_PS_PRODUCT_SKU + csku.get("ECODE");
            keys.add(key);
            keys.add(ecodeKey);
            keys.add(proKey);
            keys.add(proEcodeKey);
            keys.add(skuIdKey);
            keys.add(skuCodeKey);

            String skuForCodeKey = "";
            if (csku.getString("FORCODE") != null) {
                skuForCodeKey = BasicConstants.REDIS_PS_PRODUCT_SKUFORCODE + csku.getString("FORCODE");
                keys.add(skuForCodeKey);
            }
            if (csku.getString("GBCODE") != null) {
                skuForCodeKey = BasicConstants.REDIS_PS_PRODUCT_SKUFORCODE + csku.getString("GBCODE");
                keys.add(skuForCodeKey);
            }
        }
        redisTemplate.delete(keys);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("jo：") + jo.getDouble("WEIGHT"));
        }

        //修改保存时  若重量为空 赋值0
        //jo.put("WEIGHT", jo.getDouble("WEIGHT") == null ? 0 : jo.getDouble("WEIGHT"));
        // 添加修改人
        jo.put("MODIFIERID", querySession.getUser().getId().longValue());
        jo.put("MODIFIERNAME", querySession.getUser().getName());
        jo.put("MODIFIERENAME", querySession.getUser().getEname());
        // 添加修改时间
        jo.put("MODIFIEDDATE", new Timestamp(System.currentTimeMillis()));
        // 插入id
        jo.put("ID", objid);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("jo：") + jo);
        }
        // 执行mapper更新
        int count = cskuMapper.updateCsku(jo);

        if (count == 0) {
            throw new NDSException(Resources.getMessage("当前记录已不存在！", querySession.getLocale()));
        }

        return ValueHolderUtils.success();
    }

    /**
     * 新建
     *
     * @param jo
     * @param querySession
     * @param cskuMapper
     * @param objid
     */
    private ValueHolder insertTable(JSONObject jo, QuerySession querySession, CskuMapper cskuMapper, Long objid) {
        // 常量
        String eCode = "ECODE";
        String clrs = "PS_C_SPEC1OBJ_ID";
        String sizes = "PS_C_SPEC2OBJ_ID";
        String psCProIdKey = "PS_C_PRO_ID";
        String shop = "CP_C_SHOP_ID";
        String gbCode = "GBCODE";

        CproMapper cproMapper = ApplicationContextHandle.getBean(CproMapper.class);

        // 当前系统时间
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());

        Long psCProId = jo.getLong(psCProIdKey);
        HashMap<String, Object> cProInfoMap = cproMapper.getById(psCProId);

        // 判断【条码】字段唯一
        checkEcode(jo.getString(eCode), cskuMapper, querySession);

        if (jo.containsKey(gbCode) && !StringUtils.isEmpty(jo.containsKey(gbCode))) {
            // 判断【国际码】字段唯一
            checkGBCode(null, jo.getString(gbCode), cskuMapper, querySession);
        }

        // 【商品】【颜色】【尺寸】字段唯一，如【商品】【颜色】【尺寸】字段的值在表中已存在则提示：输入的数据已存在：【颜色】【尺寸】
        // 判断【颜色】和【尺寸】是否同时存在
        if (jo.containsKey(clrs) && jo.containsKey(sizes)) {
            // 判断【颜色】+【尺寸】字段唯一
            checkClrsSizes(psCProId, jo.getLong(clrs), jo.getLong(sizes),
                    cskuMapper, querySession);
        }

        JSONObject insertJo = new JSONObject();

        // 获取平台店铺信息
        if (jo.containsKey(shop) && !StringUtils.isEmpty(jo.containsKey(shop))) {
            HashMap shopResult = cshopMapper.findShopByName(jo.getLong(shop));

            if (shopResult != null) {
                insertJo.put("CP_C_SHOP_ID", jo.getLong(shop));
                insertJo.put("CP_C_SHOP_TITLE", shopResult.get("cp_c_shop_title"));
            }

        }
        Long clrId = jo.getLong(clrs);
        HashMap psCSpecobjInfo = proSkuQueryMapper.selectPsCSpecobjByClrId(clrId);
        if (psCSpecobjInfo.size() == 0) {
            throw new NDSException(Resources.getMessage("根据颜色ID【" + clrId + "】查询ps_c_specobj表，无当前颜色信息！", querySession.getLocale()));
        }
        String ename = psCSpecobjInfo.get("ENAME").toString();
        insertJo.put("PRO_ECODE_AND_CLR", cProInfoMap.get("ECODE") + ename);
        Long id = Tools.getSequence("PS_C_SKU");
        insertJo.put("ID", id);
        insertJo.put("AD_CLIENT_ID", (Long) cProInfoMap.get("AD_CLIENT_ID"));
        insertJo.put("AD_ORG_ID", (Long) cProInfoMap.get("AD_ORG_ID"));
        insertJo.put("CP_C_DISTRIB_ID", 0L);
        insertJo.put("PS_C_PRO_ID", psCProId);
        insertJo.put("PS_C_PRO_ECODE", cProInfoMap.get("ECODE"));
        insertJo.put("PS_C_PRO_ENAME", cProInfoMap.get("ENAME"));
        insertJo.put("QTY_LOWSTOCK", jo.getDouble("QTY_LOWSTOCK"));
        insertJo.put("QTY_SAFENUM", jo.getDouble("QTY_SAFENUM"));
        insertJo.put("LENGTH", jo.getDouble("LENGTH"));
        insertJo.put("WIDTH", jo.getDouble("WIDTH"));
        insertJo.put("HEIGHT", jo.getDouble("HEIGHT"));
        //保存时  若重量为空 赋值0

        insertJo.put("WEIGHT", jo.getDouble("WEIGHT"));
        insertJo.put("NET_WEIGHT", jo.getDouble("NET_WEIGHT"));
        insertJo.put("WEIGHT_UNIT", jo.getString("WEIGHT_UNIT"));
        insertJo.put("SPEC_MODEL", jo.getString("SPEC_MODEL"));
        insertJo.put("REMARK", jo.getString("REMARK"));
        insertJo.put("PS_C_BRAND_ID", (Long) cProInfoMap.get("PS_C_BRAND_ID"));
        insertJo.put("ECODE", jo.getString("ECODE"));
        insertJo.put("PS_C_SPEC1OBJ_ID", jo.getLong(clrs));
        insertJo.put("PS_C_SPEC2OBJ_ID", jo.getLong(sizes));
        insertJo.put("GBCODE", jo.getString("GBCODE"));
        insertJo.put("WARE_TYPE", jo.getInteger("WARE_TYPE") == null ? 0 : jo.getInteger("WARE_TYPE"));
        insertJo.put("IS_TOWMS", 0L);
        insertJo.put("FORCODE1", jo.getString("FORCODE1"));
        insertJo.put("PRONATURE", (Long) cProInfoMap.get("PRONATURE"));
        insertJo.put("PURCHASEMODE", (Long) cProInfoMap.get("PURCHASEMODE"));
        insertJo.put("OWNERID", querySession.getUser().getId().longValue());
        insertJo.put("OWNERNAME", querySession.getUser().getName());
        insertJo.put("OWNERENAME", querySession.getUser().getEname());
        insertJo.put("CREATIONDATE", timestamp);
        insertJo.put("MODIFIERID", querySession.getUser().getId().longValue());
        insertJo.put("MODIFIERNAME", querySession.getUser().getName());
        insertJo.put("MODIFIERENAME", querySession.getUser().getEname());
        insertJo.put("MODIFIEDDATE", timestamp);
        insertJo.put("IS_TO_MID_TABLE", 1);
        insertJo.put("BULK", jo.get("BULK") == null ? 0 : jo.get("BULK"));
        insertJo.put("SHELF_LIFE", jo.get("SHELF_LIFE") == null ? 0 : jo.get("SHELF_LIFE"));
        insertJo.put("IS_PUSH_WMS", jo.getString("IS_PUSH_WMS"));
        insertJo.put("IS_PUSH_JDYC", jo.getString("IS_PUSH_JDYC"));
        insertJo.put("IS_PUSH_NK", jo.getString("IS_PUSH_NK"));
        insertJo.put("IS_PUSH_FULE", jo.getString("IS_PUSH_FULE"));
        //插入记录
        int res = cskuMapper.insertSku(insertJo);
        if (res > 0) {
            return ValueHolderUtils.success("新增成功！",
                    ValueHolderUtils.createAddErrorData("PS_C_SKU", id, null));
        }
        throw new NDSException("当前记录已不存在！");

    }

    /**
     * 新增第三方条码明细
     *
     * @param joArray
     * @param querySession
     * @param cskuMapper
     * @param objid
     */
    private void insertThirdItem(JSONArray joArray, QuerySession querySession, CskuMapper cskuMapper, Long objid) {
        if (CollectionUtils.isNotEmpty(joArray)) {
            for (Object obj : joArray) {
                JSONObject jo = (JSONObject) obj;
                String ducode = jo.getString("DUCODE");

                if (org.apache.commons.lang3.StringUtils.isNotBlank(ducode)) {
                    //查询是否有重复的ducode
                    JSONObject thirdItem = cskuMapper.selectThirdItemByDucode(ducode);
                    if (Objects.nonNull(thirdItem)) {
                        throw new NDSException("第三方条码[" + ducode + "]已存在，不允许重复");
                    }

                    Long itemId = jo.getLong("ID");
                    if (itemId < 0) {
                        Long id = Tools.getSequence("PS_C_SKU_THIRD_ITEM");
                        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
                        jo.put("ID", id);
                        jo.put("PS_C_SKU_ID", objid);
                        jo.put("OWNERID", querySession.getUser().getId().longValue());
                        jo.put("OWNERNAME", querySession.getUser().getName());
                        jo.put("CREATIONDATE", timestamp);
                        jo.put("MODIFIERID", querySession.getUser().getId().longValue());
                        jo.put("MODIFIERNAME", querySession.getUser().getName());
                        jo.put("MODIFIEDDATE", timestamp);
                        cskuMapper.insertSkuThirdItem(jo);
                    } else {
                        jo.put("MODIFIERID", querySession.getUser().getId().longValue());
                        jo.put("MODIFIERNAME", querySession.getUser().getName());
                        jo.put("MODIFIEDDATE", new Timestamp(System.currentTimeMillis()));
                        cskuMapper.updateSkuThirdItem(jo);
                    }

                }
            }
        }
    }

    /**
     * 判断【款号】字段唯一
     *
     * @param ecode
     * @param cskuMapper
     * @param querySession
     */
    private void checkEcode(String ecode, CskuMapper cskuMapper, QuerySession querySession) {
        int count = cskuMapper.checkByEcode(ecode);
        if (count > 0) {
            throw new NDSException(Resources.getMessage("当前条码已存在！", querySession.getLocale()));
        }
    }

    /**
     * 【国际码】字段唯一
     *
     * @param id           修改时传这个
     * @param gbcode
     * @param cskuMapper
     * @param querySession
     */
    private void checkGBCode(Long id, String gbcode, CskuMapper cskuMapper, QuerySession querySession) {
        if (StringUtils.isEmpty(gbcode)) {
            return;
        }

        int count = cskuMapper.checkGBCode(gbcode, id);
        if (count > 0) {
            log.warn(LogUtil.format("当前国标码已存在，国标码：{},ID:{}",
                    "CskuSaveCmdImpl.checkGBCode"), gbcode, id);
            throw new NDSException(Resources.getMessage("当前国标码已存在！", querySession.getLocale()));
        }
    }

    /**
     * 判断【商品】+【颜色】+【尺寸】字段唯一
     *
     * @param objId
     * @param clrsId
     * @param sizesId
     * @param cskuMapper
     * @param querySession
     */
    private void checkClrsSizes(Long objId, Long clrsId, Long sizesId, CskuMapper cskuMapper, QuerySession querySession) {
        int count = cskuMapper.checkColorSizeByProId(objId, clrsId, sizesId);
        if (count > 0) {
            throw new NDSException(Resources.getMessage("当前商品编码+颜色+尺码已存在条码！", querySession.getLocale()));
        }
    }
}
