package com.jackrain.nea.ps.mapper;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.web.utils.ArrayToSUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.jdbc.SQL;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2018-05-31 17:48
 * @remark 标准商品停用
 */
@Mapper
public interface CproIsActiveMapper {
    class CproIsActiveSelect{
        public String selectSql(Map map){
            String sql = new SQL(){
                String tableName = (String)map.get("tableName");
                Long id = (Long) map.get("ID");
                {
                    SELECT("BILL_NO");
                    FROM(tableName);
                    WHERE("ID = "+id);
                }
            }.toString();
            return sql;
        }

        public String updateSql(Map map){
            String sql = new SQL(){
                String tableName = (String)map.get("tableName");
                JSONObject setKeys = (JSONObject)map.get("setKeys");
                JSONObject whereKeys = (JSONObject)map.get("whereKeys");
                boolean isIn = whereKeys.getBoolean("is_in") != null;
                {
                    UPDATE(tableName);
                    for (String key : setKeys.keySet()) {
                        if ("id".equalsIgnoreCase(key)) {
                            continue;
                        }
                        SET(key + "= #{setKeys." + key + "}");
                    }
                    if (isIn){
                        Set<String> keySet = whereKeys.keySet();
                        for (String key : keySet) {
                            if (!Objects.equals(key,"is_in")){
                                JSONArray retailIds = whereKeys.getJSONArray(key);
                                WHERE(key+" in (" + ArrayToSUtil.join(retailIds.toArray(), ",") + ")");
                            }
                        }
                    }else {
                        for (String key : whereKeys.keySet()) {
                            if (whereKeys.getString(key) == null) {
                                continue;
                            }
                            WHERE(key + "= #{whereKeys." + key + "}");
                        }
                    }
                }
            }.toString();
            return sql;
        }

        public String getEcodeByIds(Map map){
            String sql = new SQL(){
                JSONArray ids = (JSONArray) map.get("ids");
                String tableName = (String)map.get("tableName");
                {
                    SELECT("ID,ECODE,ISACTIVE");
                    FROM(tableName);
                    WHERE("ID in (" + StringUtils.join(ids.toArray(), ',') + ")");
                }
            }.toString();
            return sql;
        }

        public String getIdsByEcode(Map map){
            String sql = new SQL(){
                JSONArray proEcodes = (JSONArray) map.get("proEcodes");
                {
                    SELECT("ID");
                    FROM("PS_C_PRO");
                    WHERE("ECODE in (" + ArrayToSUtil.join(proEcodes.toArray(), ",") + ")");
                }
            }.toString();
            return sql;
        }
    }

    /**
     * 根据IDS查询ID、ECODE、ISACTIVE
     * @param ids ID集合
     * @param tableName 表名
     * @return String
     */
    @SelectProvider(type = CproIsActiveSelect.class,
            method = "getEcodeByIds")
    List<HashMap> getEcodeByIds(@Param("ids") JSONArray ids, @Param("tableName") String tableName);

    /**
     * 更新
     * @param tableName 表名
     * @param setKeys 更新参数
     * @param whereKeys 条件参数
     */
    @UpdateProvider(type = CproIsActiveSelect.class,
            method = "updateSql")
    int updateTable(@Param("tableName") String tableName, @Param("setKeys") JSONObject setKeys, @Param("whereKeys") JSONObject whereKeys);

    /**
     * 根据款号查询标准及配销所有商品ID
     * @param proEcodes 标准商品款号集合
     * @return List<Long>
     */
    @SelectProvider(type = CproIsActiveSelect.class,
            method = "getIdsByEcode")
    List<Long> getIdsByEcode(@Param("proEcodes") JSONArray proEcodes);

    /**
     * 根据objid查询条码编码
     * @param proId 商品ID
     * @return HashMap
     */
    @Select("SELECT ECODE,ISACTIVE FROM PS_C_PRO WHERE ID = #{proId}")
    HashMap getEcode(@Param("proId") Long proId);

    /**
     * 根据头表ID查询明细是否都为停用
     * @param proId 商品ID
     * @return int
     */
    @Select("SELECT COUNT(1) FROM PS_C_SKU WHERE PS_C_PRO_ID = #{proId} AND ISACTIVE = 'Y'")
    int getYItem(@Param("proId") Long proId);

}
