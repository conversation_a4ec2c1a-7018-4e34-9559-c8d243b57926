package com.jackrain.nea.ps.service;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.CproMinRecycleQueryCmd;
import com.jackrain.nea.ps.mapper.CproRecycleMapper;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

/**
 * <p>Title: CproMinRecycleQueryCmdImpl</p>
 * <p>Date: 2018/5/2 </p>
 * <p>Description: </p>
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps")
public class CproMinRecycleQueryCmdImpl extends CommandAdapter implements CproMinRecycleQueryCmd {

    @Autowired
    private CproRecycleMapper cproRecycleMapper;

    @Override
    public ValueHolder execute(HashMap map) throws NDSException {
        ValueHolder valueHolder = new ValueHolder();
        valueHolder.put("code", 0);
        valueHolder.put("message", "Success");

        JSONObject param = new JSONObject(map);

        Object ids = param.get("eCodes");
        List<Long> idList = (List<Long>) ids;

//        Long distribId = param.getLong("distribId");

        // 考虑到性能问题，如果传入空的ID列表，直接返回0
        Integer minRecycle = 0;
        if (CollectionUtils.isNotEmpty(idList)) {
            minRecycle = cproRecycleMapper.queryMinRecycle(param);
        }
        if (minRecycle == null) {
            minRecycle = 0;
        }

        valueHolder.put("minRecycle", minRecycle);
        return valueHolder;
    }
}
