package com.jackrain.nea.ps.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.ps.api.PsCProdimItemQueryCmd;
import com.jackrain.nea.ps.api.request.PsCProdimItemRequest;
import com.jackrain.nea.ps.api.table.PsCProdimItem;
import com.jackrain.nea.ps.mapper.PsCProdimItemMapper;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> lin yu
 * @since 2019/7/22
 * create at : 2019/7/22 10:40
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps")
public class PsCProdimItemQueryCmdImpl implements PsCProdimItemQueryCmd {
    @Autowired
    private PsCProdimItemMapper psCProdimItemMapper;

    @Override
    public ValueHolderV14<Map<Long, PsCProdimItem>> queryPsCProdimItem(PsCProdimItemRequest psCProdimItemRequest) {
        ValueHolderV14<Map<Long, PsCProdimItem>> holder;
        //入参日志记录
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start.PsCProdimItemQueryService.queryPsCProdimItem." +
                    "ReceiveParams:psCProdimItemRequest：")
                    + JSONObject.toJSONString(psCProdimItemRequest));
        }

        //检查入参
        holder = checkServiceParam(psCProdimItemRequest);
        if (ResultCode.FAIL == holder.getCode()) {
            return holder;
        }

        List<PsCProdimItem> psCProdimItems = this.psCProdimItemMapper.selectBatchIds(psCProdimItemRequest.getIdList());

        if (CollectionUtils.isEmpty(psCProdimItems)) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("未查到任何数据");
        } else {
            HashMap<Long, PsCProdimItem> resultMap = new HashMap<>();
            psCProdimItems.forEach(x -> resultMap.put(x.getId(), x));
            holder.setMessage("查询成功");
            holder.setData(resultMap);
        }

        return holder;
    }

    @Override
    public ValueHolderV14<Map<Long, PsCProdimItem>> queryAllSex() {
        ValueHolderV14<Map<Long, PsCProdimItem>> holderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, "");

        List<PsCProdimItem> psCProdimItems = this.psCProdimItemMapper.selectList(
                new QueryWrapper<PsCProdimItem>().lambda().eq(PsCProdimItem::getPsCProdimId,2027L));

        if (CollectionUtils.isEmpty(psCProdimItems)) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("未查到任何数据");
        } else {
            HashMap<Long, PsCProdimItem> resultMap = new HashMap<>();
            psCProdimItems.forEach(x -> resultMap.put(x.getId(), x));
            holderV14.setMessage("查询成功");
            holderV14.setData(resultMap);
        }
        return holderV14;
    }

    @Override
    public ValueHolderV14<List<PsCProdimItem>> queryPsCProdimItemListByNames(List<String> names,Long prodimId) {
        ValueHolderV14<List<PsCProdimItem>> holderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, "success");
        List<PsCProdimItem> psCProdimItems =
                psCProdimItemMapper.selectList(new QueryWrapper<PsCProdimItem>().lambda()
                        .eq(PsCProdimItem::getPsCProdimId, prodimId)
                        .in(PsCProdimItem::getEname,names)
                        .eq(PsCProdimItem::getIsactive, YesNoEnum.Y.getKey()));
        holderV14.setData(psCProdimItems);
        return holderV14;

    }

    @Override
    public ValueHolderV14<List<PsCProdimItem>> queryPsCProdimItemListByCodes(List<String> codes,Long prodimId) {
        ValueHolderV14<List<PsCProdimItem>> holderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, "success");
        List<PsCProdimItem> psCProdimItems =
                psCProdimItemMapper.selectList(new QueryWrapper<PsCProdimItem>().lambda()
                        .eq(PsCProdimItem::getPsCProdimId, prodimId)
                        .in(PsCProdimItem::getEcode,codes)
                        .eq(PsCProdimItem::getIsactive, YesNoEnum.Y.getKey()));
        holderV14.setData(psCProdimItems);
        return holderV14;

    }

    private ValueHolderV14<Map<Long, PsCProdimItem>> checkServiceParam(PsCProdimItemRequest psCProdimItemRequest) {
        ValueHolderV14<Map<Long, PsCProdimItem>> holderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, "");

        if (psCProdimItemRequest == null) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("请求体不能为空!");
        }

        if (psCProdimItemRequest != null && CollectionUtils.isEmpty(psCProdimItemRequest.getIdList())) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("传入id为空");
        }
        return holderV14;
    }
}
