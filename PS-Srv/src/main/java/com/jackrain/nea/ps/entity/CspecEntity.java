package com.jackrain.nea.ps.entity;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2017/10/16
 */
public class CspecEntity {
    private Long id;
    private Long ad_client_id;
    private Long ad_org_id;
    private String ename;
    private String remark;
    private Long flag;
    private Long ownerid;
    private Long modifierid;
    private Timestamp creationdate;
    private Timestamp modifieddate;
    private String isactive;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getAd_client_id() {
        return ad_client_id;
    }

    public void setAd_client_id(Long ad_client_id) {
        this.ad_client_id = ad_client_id;
    }

    public Long getAd_org_id() {
        return ad_org_id;
    }

    public void setAd_org_id(Long ad_org_id) {
        this.ad_org_id = ad_org_id;
    }

    public String getEname() {
        return ename;
    }

    public void setEname(String ename) {
        this.ename = ename;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getFlag() {
        return flag;
    }

    public void setFlag(Long flag) {
        this.flag = flag;
    }

    public Long getOwnerid() {
        return ownerid;
    }

    public void setOwnerid(Long ownerid) {
        this.ownerid = ownerid;
    }

    public Long getModifierid() {
        return modifierid;
    }

    public void setModifierid(Long modifierid) {
        this.modifierid = modifierid;
    }

    public Timestamp getCreationdate() {
        return creationdate;
    }

    public void setCreationdate(Timestamp creationdate) {
        this.creationdate = creationdate;
    }

    public Timestamp getModifieddate() {
        return modifieddate;
    }

    public void setModifieddate(Timestamp modifieddate) {
        this.modifieddate = modifieddate;
    }

    public String getIsactive() {
        return isactive;
    }

    public void setIsactive(String isactive) {
        this.isactive = isactive;
    }
}
