package com.jackrain.nea.ps.service;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.CproColumnCmd;
import com.jackrain.nea.ps.mapper.CproColumnMapper;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import java.util.HashMap;

@Slf4j
@Component
@Service(protocol = "dubbo" , validation = "true" , version = "1.0" , group = "ps")
public class CproColumnCmdImpl extends CommandAdapter implements CproColumnCmd{

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return null;
    }

    @Override
    public ValueHolder execute(HashMap hashMap) throws NDSException {
        ValueHolder result = new ValueHolder();
        long id=0;
        if(hashMap.containsKey("id")){
            try{
                id = Long.parseLong(hashMap.get("id").toString());
            }catch (Exception e){
                return  result;
            }
        }else {
            return result;
        }
        CproColumnMapper cproColumnMapper = ApplicationContextHandle.getBean(CproColumnMapper.class);
        String ecodeName=cproColumnMapper.getCodeName(id);
        result.put("date",ecodeName);
        return  result;
    }
}
