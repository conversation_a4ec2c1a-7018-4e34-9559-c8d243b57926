package com.jackrain.nea.ps.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.core.db.Tools;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.CsizegroupAddCmd;
import com.jackrain.nea.ps.entity.CspecgroupEntity;
import com.jackrain.nea.ps.entity.CspecobjEntity;
import com.jackrain.nea.ps.mapper.CsizegroupMapper;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;

/**
 * <AUTHOR> on 2017/9/29
 * 尺寸组及尺寸新增service
 */

@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "ps")
public class CsizegroupAddCmdImp extends CommandAdapter implements CsizegroupAddCmd {

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {

        CsizegroupMapper csizegroupMapper = ApplicationContextHandle.getBean(CsizegroupMapper.class);
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        JSONObject superTable = fixColumn.getJSONObject("PS_C_SIZEGROUP");
        JSONArray subTableArry = fixColumn.getJSONArray("PS_C_SIZE");
        JSONObject jo = new JSONObject();
        long sizeSpesId = param.getLong("objid");
        if (sizeSpesId <= 0) {
            //查询规格表中的flag位2的ID
            long specId = csizegroupMapper.findSpeceByFlag(2);
            int specCount = csizegroupMapper.findSpecCount(specId, superTable.getString("ENAME"));
            if (specCount > 0) {
                throw new NDSException(Resources.getMessage("输入的数据已存在：名称", querySession.getLocale()));
            } else {
                CspecgroupEntity specgroupEntity = new CspecgroupEntity();
                specgroupEntity.setAd_client_id(Integer.valueOf(querySession.getUser().getClientId()).longValue());
                specgroupEntity.setAd_org_id(Integer.valueOf(querySession.getUser().getOrgId()).longValue());
                specgroupEntity.setPs_c_spec_id(specId);
                specgroupEntity.setEname(superTable.getString("ENAME"));
                specgroupEntity.setEcode(superTable.getString("ECODE"));
                specgroupEntity.setRemark(superTable.getString("REMARK"));
                specgroupEntity.setOrderno(superTable.getLong("ORDERNO"));
                specgroupEntity.setOwnerid(Integer.valueOf(querySession.getUser().getId()).longValue());
                specgroupEntity.setModifierid(Integer.valueOf(querySession.getUser().getId()).longValue());
                specgroupEntity.setIsactive(superTable.getString("ISACTIVE"));
                String isactive = "ISACTIVE";
                if (superTable.getString(isactive) == null || "".equals(superTable.getString(isactive))) {
                    specgroupEntity.setIsactive("Y");
                }
                specgroupEntity.setPsCSpec2Ecode(superTable.getString("PS_C_SPEC2_ECODE"));
                specgroupEntity.setOwnername(querySession.getUser().getName());
                specgroupEntity.setOwnerename(querySession.getUser().getEname());
                specgroupEntity.setModifiername(querySession.getUser().getName());
                specgroupEntity.setModifierename(querySession.getUser().getEname());
                specgroupEntity.setCreationdate(new Timestamp(System.currentTimeMillis()));
                specgroupEntity.setModifieddate(new Timestamp(System.currentTimeMillis()));

                sizeSpesId = Tools.getSequence("PS_C_SPECGROUP");
                specgroupEntity.setId(sizeSpesId);
                int insertRes = csizegroupMapper.insertSpec(specgroupEntity);
                if (insertRes > 0) {
                    jo.put("tablename", "PS_C_SIZEGROUP");
                    jo.put("objid", sizeSpesId);
                    vh.put("data", jo);
                } else {
                    throw new NDSException(Resources.getMessage("颜色组插入失败", querySession.getLocale()));
                }
            }
        }

        JSONArray itemIdArray = new JSONArray();
        JSONArray dataArrayJson = new JSONArray();
        if (subTableArry != null && subTableArry.size() > 0) {

            for (int i = 0; i < subTableArry.size(); i++) {

                Long itemId = null;

                try {

                    JSONObject subTable = subTableArry.getJSONObject(i);
                    if (subTable.getString("ECODE") == null) {
                        //尺寸编码不能为空
                        throw new NDSException(Resources.getMessage("尺寸编码不能为空", querySession.getLocale()));
                    }
                    if (subTable.getString("ENAME") == null) {
                        // 颜色名称不能为空
                        throw new NDSException(Resources.getMessage("尺寸名称不能为空", querySession.getLocale()));
                    }
                    int sizeCount = csizegroupMapper.findClrCount(sizeSpesId, subTable.getString("ECODE"));
                    if (sizeCount > 0) {
                        //输入的数据已存在：颜色编码
                        throw new NDSException(Resources.getMessage("输入的数据已存在：尺寸编码", querySession.getLocale()));
                    }
                    //查询规格表中的规格ID
                    long specId = csizegroupMapper.findSpeceByFlag(2);
                    CspecobjEntity cspecobjEntity = new CspecobjEntity();

                    String eCode = subTable.getString("ECODE");
                    String eName = subTable.getString("ENAME");

                    String sizeName = csizegroupMapper.selectEnameById(sizeSpesId);
                    cspecobjEntity.setSizename(sizeName + "-" + eCode + "-" + eName);
                    cspecobjEntity.setAd_client_id(Integer.valueOf(querySession.getUser().getClientId()).longValue());
                    cspecobjEntity.setAd_org_id(Integer.valueOf(querySession.getUser().getOrgId()).longValue());
                    cspecobjEntity.setPs_c_spec_id(specId);
                    cspecobjEntity.setPs_c_specgroup_id(sizeSpesId);
                    cspecobjEntity.setEcode(eCode);
                    cspecobjEntity.setEname(eName);
                    cspecobjEntity.setMixname("[" + eCode + "]" + eName);
                    cspecobjEntity.setMatrixcolno(subTable.getLong("MATRIXCOLNO"));
                    cspecobjEntity.setRemark(subTable.getString("REMARK"));
                    cspecobjEntity.setIsactive(subTable.getString("ISACTIVE"));
                    if (subTable.getString("ISACTIVE") == null || "".equals(subTable.getString("ISACTIVE"))) {

                        cspecobjEntity.setIsactive("Y");

                    }
                    cspecobjEntity.setBrandname(subTable.getString("BRAND_NAME"));
                    cspecobjEntity.setOwnerid(Integer.valueOf(querySession.getUser().getId()).longValue());
                    cspecobjEntity.setCreationdate(new Timestamp(System.currentTimeMillis()));
                    cspecobjEntity.setModifierid(Integer.valueOf(querySession.getUser().getId()).longValue());
                    cspecobjEntity.setModifieddate(new Timestamp(System.currentTimeMillis()));
                    cspecobjEntity.setOwnername(querySession.getUser().getName());
                    cspecobjEntity.setOwnerename(querySession.getUser().getEname());
                    cspecobjEntity.setModifiername(querySession.getUser().getName());
                    cspecobjEntity.setModifierename(querySession.getUser().getEname());
                    Long id = Tools.getSequence("PS_C_SPECOBJ");
                    cspecobjEntity.setId(id);

                    int insertRes = csizegroupMapper.insertSize(cspecobjEntity);
                    if (insertRes > 0) {
                        itemIdArray.add(cspecobjEntity.getId());
                        //更新主表
                        JSONObject updateObj = new JSONObject();
                        updateObj.put("MODIFIEDDATE", new Timestamp(System.currentTimeMillis()));
                        updateObj.put("MODIFIERID", Integer.valueOf(querySession.getUser().getId()).longValue());
                        updateObj.put("ID", sizeSpesId);
                        updateObj.put("MODIFIERNAME", querySession.getUser().getName());
                        updateObj.put("MODIFIERENAME", querySession.getUser().getEname());
                        csizegroupMapper.updateSpecgroup(updateObj);

                    } else {
                        //插入失败
                        throw new NDSException(Resources.getMessage("尺寸明细插入失败", querySession.getLocale()));
                    }

                } catch (NDSException e) {
                    // 将错误信息存入dataArrayJson中
                    dataArrayJson.add(getData(e, itemId));

                }

            }

        }

        JSONObject ret = new JSONObject();
        ret.put("objid", sizeSpesId);
        if (!itemIdArray.isEmpty()) {
            ret.put("PS_C_SIZE", itemIdArray);
        }
        vh.put("ret", ret);
        if (dataArrayJson.size() > 0) {
            long parmid = param.getLong("objid");
            if (parmid <= 0) {
                jo.put("error", dataArrayJson);
                jo.put("tablename", "PS_C_SIZEGROUP");
                jo.put("objid", sizeSpesId);
                vh.put("data", jo);
            } else {
                vh.put("data", dataArrayJson);
            }
            vh.put("code", -1);
            vh.put("message", Resources.getMessage("失败", querySession.getLocale()));
        } else {
            vh.put("code", 0);
            JSONObject data = new JSONObject();
            data.put("tablename", "PS_C_SIZEGROUP");
            data.put("objid", sizeSpesId);
            vh.put("data", data);
            vh.put("message", Resources.getMessage("成功", querySession.getLocale()));
        }

        return vh;

    }

    /**
     * 获取明细表错误信息并插入dataJson
     *
     * @param e
     * @param itemId
     * @return
     */
    private JSONObject getData(NDSException e, Long itemId) {
        JSONObject dataJson = new JSONObject();
        dataJson.put("id", itemId);
        dataJson.put("code", -1);
        dataJson.put("message", e.getMessage());
        return dataJson;
    }

}