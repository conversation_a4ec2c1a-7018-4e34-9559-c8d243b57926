package com.jackrain.nea.ps.mapper;

import com.alibaba.fastjson.JSONObject;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.jdbc.SQL;

import java.util.Set;

/**
 * 商品中心-品类限制天数定义
 * mapper
 *
 * <AUTHOR>
 * @create 2018/03/05
 */
@Mapper
public interface CproClassLimitDayMapper {

    class InsertSql {
        public String insert(final JSONObject jsonObject) {
            return new SQL() {
                {
                    INSERT_INTO("PS_C_CLASSLIMITDAY");
                    Set<String> keySet = jsonObject.keySet();
                    for (String key : keySet) {
                        VALUES(key, "#{" + key + "}");
                    }
                }
            }.toString();
        }
    }

    /**
     * 新增
     *
     * @param jsonObject json参数
     */
    @InsertProvider(type = CproClassLimitDayMapper.InsertSql.class,
            method = "insert")
    void insert(@Param("jsonObject") JSONObject jsonObject);

    class UpdateSql {
        public String update(final JSONObject jsonObject) {
            return new SQL() {
                {
                    UPDATE("PS_C_CLASSLIMITDAY");
                    Set<String> keySet = jsonObject.keySet();
                    for (String key : keySet) {
                        if (!"ID".equals(key)) {
                            SET(key + "=#{" + key + "}");
                        }
                    }
                    WHERE("ID=#{ID}");
                }
            }.toString();
        }
    }

    /**
     * 更新
     *
     * @param jsonObject json参数
     */
    @UpdateProvider(type = CproClassLimitDayMapper.UpdateSql.class,
            method = "update")
    void update(@Param("jsonObject") JSONObject jsonObject);

    /**
     * 控制【性别】+【大类】不允许重复
     *
     * @param sex        性别
     * @param largeClass 大类
     * @return int
     */
    @Select("SELECT COUNT(1) FROM PS_C_CLASSLIMITDAY WHERE SEX = #{sex} AND LARGECLASS = #{largeClass}")
    int check(@Param("sex") int sex, @Param("largeClass") int largeClass);

    /**
     * 根据ID查询SEX及LARGECLASS
     *
     * @param objid id
     * @return JSONObject
     */
    @Select("SELECT SEX,LARGECLASS FROM PS_C_CLASSLIMITDAY WHERE ID = #{objid}")
    JSONObject getSexAndLarge(@Param("objid") Long objid);

    /**
     * 根据objid查询是否有对应记录
     *
     * @param objid id
     * @return int
     */
    @Select("SELECT COUNT(1) FROM PS_C_CLASSLIMITDAY WHERE ID = #{objid}")
    int count(@Param("objid") Long objid);

    /**
     * 根据objid删除该记录
     *
     * @param objid id
     */
    @Delete("DELETE FROM PS_C_CLASSLIMITDAY WHERE ID = #{objid}")
    void delete(@Param("objid") Long objid);

}
