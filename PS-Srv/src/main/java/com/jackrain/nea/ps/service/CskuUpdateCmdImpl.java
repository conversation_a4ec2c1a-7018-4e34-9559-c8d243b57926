package com.jackrain.nea.ps.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.CskuUpdateCmd;
import com.jackrain.nea.ps.mapper.CskuMapper;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;

/**
 * 标准商品档案-条码维护-更新
 *
 * <AUTHOR>
 * @create 2017/10/11
 */
@Slf4j
@Component
@Service(protocol = "dubbo" , validation = "true" , version = "1.0" , group = "ps")
public class CskuUpdateCmdImpl extends CommandAdapter implements CskuUpdateCmd {

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        // Mapper
        CskuMapper cskuMapper = ApplicationContextHandle.getBean(CskuMapper.class);

        // 获取主表json
        JSONObject jo = fixColumn.getJSONObject("PS_C_SKU");

        // 获取objid 并更新
        Long objid = param.getLong("objid");
        if (objid > 0) {
            // 更新
            updateTable(jo, querySession, cskuMapper, objid);
        }

        vh.put("code",0);
        vh.put("message", Resources.getMessage("成功",querySession.getLocale()));
        JSONObject ret = new JSONObject();
        ret.put("PS_C_SKU",objid);
        vh.put("ret",ret);
        return vh;
    }

    /**
     * 更新
     * @param jo
     * @param querySession
     * @param cskuMapper
     * @param timestamp
     * @param objid
     */
    private void updateTable(JSONObject jo, QuerySession querySession, CskuMapper cskuMapper , Long objid){
        // 常量
        String cpcDistribIdStr = "CP_C_DISTRIB_ID";
        String eCode = "ECODE";

        // 【配销中心】【条码】字段唯一，如【配销中心】【条码】字段的值在表中已存在则提示：输入的数据已存在：条码
        // 判断【配销中心】和【条码】是否同时存在
        if (jo.containsKey(cpcDistribIdStr) && jo.containsKey(eCode)){
            // 判断【配销中心】+【条码】字段唯一
            checkEcode(jo.getLong("CP_C_DISTRIB_ID"), jo.getString("ECODE"), cskuMapper, querySession);
        }else {
            // 判断【配销中心】键值是否存在
            if (jo.containsKey(cpcDistribIdStr)) {
                // 查询【条码】
                String ecode = cskuMapper.selectEcode(objid);
                // 判断【配销中心】+【条码】字段唯一
                checkEcode(jo.getLong("CP_C_DISTRIB_ID"), ecode, cskuMapper, querySession);
            }
            // 判断【条码】键值是否存在
            if (jo.containsKey(eCode)) {
                // 查询【配销中心】
                Long cpcDistribId = cskuMapper.selectCpcdistribid(objid);
                // 判断【配销中心】+【条码】字段唯一
                checkEcode(cpcDistribId, jo.getString("ECODE"), cskuMapper, querySession);
            }
        }
        // 添加修改人
        jo.put("MODIFIERID", querySession.getUser().getId().longValue());
        jo.put("MODIFIERNAME", querySession.getUser().getName());
        jo.put("MODIFIERENAME", querySession.getUser().getEname());
        // 添加修改时间
        jo.put("MODIFIEDDATE",new Timestamp(System.currentTimeMillis()));
        // 插入id
        jo.put("ID",objid);
        // 执行mapper更新
        int count = cskuMapper.updateCsku(jo);
        if (count == 0){
            throw new NDSException(Resources.getMessage("当前记录已不存在！",querySession.getLocale()));
        }
    }

    /**
     * 判断【配销中心】+【款号】字段唯一
     * @param cpcDistribId
     * @param ecode
     * @param cskuMapper
     * @param querySession
     */
    private void checkEcode(Long cpcDistribId, String ecode, CskuMapper cskuMapper, QuerySession querySession){
        int count = cskuMapper.checkEcode(cpcDistribId,ecode);
        if (count >0){
            throw new NDSException(Resources.getMessage("输入的数据已存在：条码", querySession.getLocale()));
        }
    }
}
