package com.jackrain.nea.ps.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.jdbc.SQL;

import java.util.HashMap;
import java.util.List;

@Mapper
public interface CproLikeQueryMapper {
    class ChruserssuppProvider {

        public String queryLikeProdim(final String query){
            return new SQL(){
                {
                    SELECT("ID,ECODE,ENAME");
                    FROM("PS_C_PRO");
                    if(query!=null){
                        WHERE("(ECODE LIKE '%"+query+"%' OR ENAME LIKE '%"+query+"%') AND CP_C_DISTRIB_ID=0");
                    }
                }
            }.toString();
        }
    }


    //商品模糊查询
    @SelectProvider(type = ChruserssuppProvider.class,method = "queryLikeProdim")
    List<HashMap> proLikeQuery(String query);

}
