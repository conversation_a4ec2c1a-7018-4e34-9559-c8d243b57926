package com.jackrain.nea.ps.mapper;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.ps.entity.CproruleEntity;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.jdbc.SQL;

import java.util.Set;

@Mapper
public interface CproruleMapper {

    class CproruleProvider {
        public String update(final JSONObject jsonObject) {
            return new SQL() {
                {
                    Set<String> keySet = jsonObject.keySet();
                    UPDATE("PS_C_PRORULE");
                    for (int i = 0; i < jsonObject.size(); i++) {
                        String key = (String) keySet.toArray()[i];
                        if ("ID".equals(key)) {
                            WHERE("ID=#{" + key + "}");
                        } else {
                            SET(key + "= #{" + key + "}");
                        }
                    }
                }
            }.toString();
        }

        public String insert(final JSONObject jsonObject) {
            return new SQL() {
                {
                    Set<String> keySet = jsonObject.keySet();
                    INSERT_INTO("PS_C_PRORULE");
                    for (int i = 0; i < jsonObject.size(); i++) {
                        String key = (String) keySet.toArray()[i];
                        VALUES(key, "#{" + key + "}");
                    }
                }
            }.toString();
        }
    }

    @Select("SELECT COUNT(1) FROM PS_C_PRORULE WHERE ENAME=#{ename}")
    int selectProruleEname(@Param("ename") String ename);

    @InsertProvider(type = CproruleProvider.class, method = "insert")
    int insertProrule(JSONObject jsonObject);

    @UpdateProvider(type = CproruleProvider.class, method = "update")
    int updateProrule(JSONObject jsonObject);

    //判断是否存在于标准商品档案中
    @Select("SELECT COUNT(1) FROM PS_C_PRO WHERE PS_C_PRORULE_ID=#{ps_c_prorule_id}")
    int selectPro(@Param("ps_c_prorule_id") Long ps_c_prorule_id);

    @Update("UPDATE PS_C_PRORULE SET MODIFIERID=#{modifierid},MODIFIEDDATE=#{modifieddate} WHERE ID=#{id}")
    int updateRoruleAfterDel(CproruleEntity ps_c_proruleEntity);

    @Delete("DELETE FROM PS_C_PRORULE WHERE ID=#{id}")
    int delProrule(@Param("id") Long id);

    @Select("SELECT ENAME FROM PS_C_PRORULE WHERE ID=#{id}")
    String selectEname(@Param("id") Long id);

    @Select("SELECT COUNT(1) FROM PS_C_PRORULE WHERE ENAME=#{ename} AND ID!=#{id}")
    int selectEnameCount(@Param("ename") String ename, @Param("id") Long id);

    @Select("SELECT COUNT(1) FROM PS_C_PRORULE WHERE ID=#{id}")
    int selectobjid(@Param("id") Long id);

    @Update("UPDATE PS_C_PRORULE SET ISDEFAULT = 0 WHERE ID <> #{id}")
    void updateIsdefault(@Param("id") Long id);
}
