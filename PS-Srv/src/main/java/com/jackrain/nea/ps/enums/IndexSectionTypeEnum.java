package com.jackrain.nea.ps.enums;

/**
 * <AUTHOR>
 * 2018-2-12
 */
public enum IndexSectionTypeEnum {
    NULL_TYPE(-1),BANNER(1),PRODUCTLIST(2);
    private Integer value;

    IndexSectionTypeEnum(Integer value) {
        this.value = value;
    }

    public static IndexSectionTypeEnum valueOf(Integer value){
        for (IndexSectionTypeEnum type: IndexSectionTypeEnum.values()){
            if(type.getValue().equals(value)){
                return type;
            }
        }
        return NULL_TYPE;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }
}
