package com.jackrain.nea.ps.service;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.CproDimQueryCmd;
import com.jackrain.nea.ps.mapper.CproDimQueryMapper;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

/**
 * 性别大类季节查询
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true",version="1.0", group="ps")
public class CproDimQueryCmdImpl extends CommandAdapter implements CproDimQueryCmd {

    @Override
    public ValueHolder execute(HashMap hashMap) throws NDSException {
        ValueHolder valueHolder = new ValueHolder();
        Object ecode = hashMap.get("ECODE");
        Object ecodes = hashMap.get("ECODES");
        Object distribId = hashMap.get("CP_C_DISTRIB_ID");
        CproDimQueryMapper cproDimQueryMapper = ApplicationContextHandle.getBean(CproDimQueryMapper.class);
        if (null != ecode){
            com.alibaba.fastjson.JSONObject cproDim = cproDimQueryMapper.cproDim(String.valueOf(ecode));
            valueHolder.put("code",0);
            valueHolder.put("data",cproDim);
        }else if (null != ecodes){
            List<JSONObject> cproDims = cproDimQueryMapper.cproDims(String.valueOf(ecodes));
            valueHolder.put("code",0);
            valueHolder.put("data",cproDims);
        }else if (null != distribId){
            List<JSONObject> cproDims = cproDimQueryMapper.cproDimsBytribid(Long.valueOf(distribId.toString()));
            valueHolder.put("code",0);
            valueHolder.put("data",cproDims);
        }

        return valueHolder;
    }

}
