package com.jackrain.nea.ps.entity;

import java.io.Serializable;

/**
 * 号型实体类
 * <AUTHOR>
 * @date 2017/9/26.
 */

public class CshapeEntity implements Serializable {

  private static final long serialVersionUID = -7203364807079441034L;
  private Long id;
  private Long ad_client_id;
  private Long ad_org_id;
  private Long ps_c_shapegroup_id;
  private Long ps_c_size_id;
  private String ename;
  private String isactive;
  private Long ownerid;
  private Long modifierid;
  private Long matrixcolno;
  private java.sql.Timestamp creationdate;
  private java.sql.Timestamp modifieddate;

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public Long getAd_client_id() {
    return ad_client_id;
  }

  public void setAd_client_id(Long ad_client_id) {
    this.ad_client_id = ad_client_id;
  }

  public Long getAd_org_id() {
    return ad_org_id;
  }

  public void setAd_org_id(Long ad_org_id) {
    this.ad_org_id = ad_org_id;
  }

  public Long getPs_c_shapegroup_id() {
    return ps_c_shapegroup_id;
  }

  public void setPs_c_shapegroup_id(Long ps_c_shapegroup_id) {
    this.ps_c_shapegroup_id = ps_c_shapegroup_id;
  }

  public Long getPs_c_size_id() {
    return ps_c_size_id;
  }

  public void setPs_c_size_id(Long ps_c_size_id) {
    this.ps_c_size_id = ps_c_size_id;
  }

  public String getEname() {
    return ename;
  }

  public void setEname(String ename) {
    this.ename = ename;
  }

  public String getIsactive() {
    return isactive;
  }

  public void setIsactive(String isactive) {
    this.isactive = isactive;
  }

  public Long getOwnerid() {
    return ownerid;
  }

  public void setOwnerid(Long ownerid) {
    this.ownerid = ownerid;
  }

  public Long getModifierid() {
    return modifierid;
  }

  public void setModifierid(Long modifierid) {
    this.modifierid = modifierid;
  }

  public java.sql.Timestamp getCreationdate() {
    return creationdate;
  }

  public void setCreationdate(java.sql.Timestamp creationdate) {
    this.creationdate = creationdate;
  }

  public java.sql.Timestamp getModifieddate() {
    return modifieddate;
  }

  public void setModifieddate(java.sql.Timestamp modifieddate) {
    this.modifieddate = modifieddate;
  }

  @Override
  public String toString() {
    return "CshapeEntity{" +
            "id=" + id +
            ", ad_client_id=" + ad_client_id +
            ", ad_org_id=" + ad_org_id +
            ", ps_c_shapegroup_id=" + ps_c_shapegroup_id +
            ", ps_c_size_id=" + ps_c_size_id +
            ", ename='" + ename + '\'' +
            ", isactive='" + isactive + '\'' +
            ", ownerid=" + ownerid +
            ", modifierid=" + modifierid +
            ", creationdate=" + creationdate +
            ", modifieddate=" + modifieddate +
            '}';
  }
}
