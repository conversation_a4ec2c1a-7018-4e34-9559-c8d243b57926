package com.jackrain.nea.psext.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/8/23
 */
@Data
@TableName(value = "ps_c_coll_shop_pro_mapping")
public class PsCCollShopProMapping extends BaseModel {

    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "cp_c_platform_id")
    private Long cpCPlatformId;

    @JSONField(name = "cp_c_platform_ecode")
    private String cpCPlatformEcode;

    @JSONField(name = "cp_c_platform_ename")
    private String cpCPlatformEname;

    @JSONField(name = "cp_c_shop_id")
    private Long cpCShopId;

    @JSONField(name = "cp_c_shop_ecode")
    private String cpCShopEcode;

    @JSONField(name = "cp_c_shop_title")
    private String cpCShopTitle;

    @JSONField(name = "cp_c_shop_child_id")
    private Long cpCShopChildId;

    @JSONField(name = "cp_c_shop_child_ecode")
    private String cpCShopChildEcode;

    @JSONField(name = "cp_c_shop_child_title")
    private String cpCShopChildTitle;

    @JSONField(name = "ps_c_sku_id")
    private Long psCSkuId;

    @JSONField(name = "ps_c_sku_ecode")
    private String psCSkuEcode;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

}
