package com.jackrain.nea.psext.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;

@TableName(value = "ps_c_unit_convert")
@Data
public class PsCUnitConvert extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "PS_C_SKU_ID")
    private Long psCSkuId;

    @JSONField(name = "PS_C_SKU_ECODE")
    private String psCSkuEcode;

    @JSONField(name = "PS_C_SKU_ENAME")
    private String psCSkuEname;

    @JSONField(name = "PS_C_PRO_ID")
    private Long psCProId;

    @JSONField(name = "PS_C_PRO_ECODE")
    private String psCProEcode;

    @JSONField(name = "PS_C_PRO_ENAME")
    private String psCProEname;

    @JSONField(name = "MEINS")
    private Long meins;

    @JSONField(name = "MEINH")
    private Long meinh;

    @JSONField(name = "UMREZ")
    private BigDecimal umrez;

    @JSONField(name = "UMREN")
    private BigDecimal umren;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    //是否传入wms
    @JSONField(name = "IS_TO_MID_TABLE")
    private Integer isToMidTable;
}