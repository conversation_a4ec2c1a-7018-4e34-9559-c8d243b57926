package com.jackrain.nea.psext.result;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName : PdaQueryProResult
 * @Description :
 * <AUTHOR> CD
 * @Date: 2021-08-12 16:31
 */
@Data
public class PdaQueryProResult implements Serializable {

    private Long psCProId;//商品id
    private String psCProEcode;//商品编码
    private String psCProEname;//商品名称
    private String gbcode;//国标码
    private String factorycode;//原厂编码
    private String psCProruleEname;//商品编码规则名称
    private String cpCSupplierEname;//供应商名称
    private String psCBrandEname;//电商品牌名称
    private String proyear;//年份
    private String image;//商品图片
    private String classificationSeason;//Classification季度
    private String classificationYear;//Classification年份
    private String sex;//性别
    private Date modifieddate;//修改时间
}
