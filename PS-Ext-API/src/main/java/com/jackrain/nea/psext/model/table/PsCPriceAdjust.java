package com.jackrain.nea.psext.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@TableName(value = "ps_c_price_adjust")
@Data
public class PsCPriceAdjust extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "BILL_NO")
    private String billNo;

    @JSONField(name = "BILL_DATE")
    private Date billDate;

    @JSONField(name = "PROGRAMME_NAME")
    private String programmeName;

    @JSONField(name = "START_DATE")
    private Date startDate;

    @JSONField(name = "END_DATE")
    private Date endDate;

    @JSONField(name = "CP_C_CUSTOMER_ID")
    private Long cpCCustomerId;

    @JSONField(name = "CP_C_CUSTOMER_ECODE")
    private String cpCCustomerEcode;

    @JSONField(name = "CP_C_CUSTOMER_ENAME")
    private String cpCCustomerEname;

    @JSONField(name = "PRICE_STANDARD")
    private Integer priceStandard;

    @JSONField(name = "COEFFICIENT_TYPE")
    private String coefficientType;

    @JSONField(name = "COEFFICIENT")
    private BigDecimal coefficient;

    @JSONField(name = "STATUS")
    private Integer status;

    @JSONField(name = "STATUS_ID")
    private Long statusId;

    @JSONField(name = "STATUS_ENAME")
    private String statusEname;

    @JSONField(name = "STATUS_NAME")
    private String statusName;

    @JSONField(name = "STATUS_TIME")
    private Date statusTime;

    @JSONField(name = "DELER_ID")
    private Long delerId;

    @JSONField(name = "DELER_NAME")
    private String delerName;

    @JSONField(name = "DELER_ENAME")
    private String delerEname;

    @JSONField(name = "DEL_TIME")
    private Date delTime;

    @JSONField(name = "CLOSER_ID")
    private Long closerId;

    @JSONField(name = "CLOSER_ENAME")
    private String closerEname;

    @JSONField(name = "CLOSER_NAME")
    private String closerName;

    @JSONField(name = "CLOSER_TIME")
    private Date closerTime;

    @JSONField(name = "REMARK")
    private String remark;

    @JSONField(name = "VERSION")
    private Long version;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "SALE_DIS_TYPE")
    private String saleDisType;
}