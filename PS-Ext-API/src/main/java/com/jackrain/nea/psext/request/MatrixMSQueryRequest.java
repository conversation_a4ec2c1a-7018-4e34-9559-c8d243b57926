package com.jackrain.nea.psext.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version :1.0
 * description ：
 * @date :2019/10/12 16:56
 */
@Data
public class MatrixMSQueryRequest implements Serializable {
    /**
     * 当前编码
     */
    private List<String> proECodes;

    /**
     * 全部编码
     */
    private List<String> allProECodes;

    /**
     * 需要查配码的编码
     */
    private List<Long> msIds;
}
