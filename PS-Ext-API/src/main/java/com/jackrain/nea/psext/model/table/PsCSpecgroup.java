package com.jackrain.nea.psext.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

/**
 * @Author: anna
 * @CreateDate: 2020/7/2$ 9:42$
 * @Description:
 */
@TableName(value = "ps_c_specgroup")
@Data
@Document(index = "ps_c_specgroup", type = "ps_c_specgroup")
public class PsCSpecgroup extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "ID", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "PS_C_SPEC_ID")
    private Long psCSpecId;

    @JSONField(name = "PS_C_BRAND_ID")
    private Long psCBrandId;

    @JSONField(name = "ENAME")
    private String ename;

    @JSONField(name = "REMARK")
    private String remark;

    @JSONField(name = "ORDERNO")
    private Long orderno;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "PS_C_SPEC1_ECODE")
    private String psCSpec1Ecode;

    @JSONField(name = "PS_C_SPEC2_ECODE")
    private String psCSpec2Ecode;

    @JSONField(name = "ECODE")
    private String ecode;
}
