package com.jackrain.nea.psext.tableExtend;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.psext.model.table.PsCPro;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

@Data
public class PsCProExtend extends PsCPro {

    private static final String ECODE = "ecode";
    /**
     * 辅助字段
     */
    private List<String> ecodeList;

    /**
     * 查询类生成工具
     *
     * @return
     */
    public QueryWrapper<PsCPro> createQueryWrapper(){
        QueryWrapper<PsCPro> queryWrapper = new QueryWrapper<PsCPro>();

        if(CollectionUtils.isNotEmpty(ecodeList)){
            queryWrapper.in(ECODE,this.getEcodeList());
        }
        return queryWrapper;
    }
}
