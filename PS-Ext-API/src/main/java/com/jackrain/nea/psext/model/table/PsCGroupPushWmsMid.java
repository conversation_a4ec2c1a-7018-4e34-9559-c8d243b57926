package com.jackrain.nea.psext.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022-05-23 23:23:45
 * @description: 组合商品推送wms中间表
 */
@TableName(value = "ps_c_group_push_wms_mid")
@Data
public class PsCGroupPushWmsMid extends BaseModel {
    /**
     * ID
     */
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 组合商品ID
     */
    @JSONField(name = "GROUP_PRO_ID")
    private Long groupProId;

    /**
     * 组合商品编码
     */
    @JSONField(name = "GROUP_PRO_ECODE")
    private String groupProEcode;

    /**
     * 组合商品名称
     */
    @JSONField(name = "GROUP_PRO_ENAME")
    private String groupProEname;

    /**
     * 组合条码ID
     */
    @JSONField(name = "GROUP_SKU_ID")
    private Long groupSkuId;

    /**
     * 组合条码编码
     */
    @JSONField(name = "GROUP_SKU_ECODE")
    private String groupSkuEcode;

    /**
     * 组合条码名称
     */
    @JSONField(name = "GROUP_SKU_ENAME")
    private String groupSkuEname;

    /**
     * 实体仓编码
     */
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE")
    private String cpCPhyWarehouseEcode;

    /**
     * WMS仓库编码
     */
    @JSONField(name = "WMS_WAREHOUSE_CODE")
    private String wmsWarehouseCode;

    /**
     * WMS账号
     */
    @JSONField(name = "WMS_ACCOUNT")
    private String wmsAccount;

    /**
     * 货主编码
     */
    @JSONField(name = "OWNER_CODE")
    private String ownerCode;

    /**
     * 传wms状态 0=未传wms,1=传wms中,2=传wms成功,3=传wms失败
     */
    @JSONField(name = "WMS_STATUS")
    private Integer wmsStatus;

    /**
     * 传WMS时间
     */
    @JSONField(name = "PASS_WMS_TIME")
    private Date passWmsTime;

    /**
     * 传WMS失败原因
     */
    @JSONField(name = "FAILURE_REASON")
    private String failureReason;

    /**
     * 传WMS失败次数
     */
    @JSONField(name = "FAILURE_NUM")
    private Integer failureNum;

    /**
     * 修改人
     */
    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    /**
     * 创建人
     */
    @JSONField(name = "OWNERENAME")
    private String ownerename;
}
