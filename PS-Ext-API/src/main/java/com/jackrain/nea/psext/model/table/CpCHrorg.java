package com.jackrain.nea.psext.model.table;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * @Author: jg.zhan
 * @Date:  2022/5/20 17:45
 * @Description:  
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "cp_c_hrorg")
public class CpCHrorg implements Serializable {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 所属公司
     */
    @TableField(value = "AD_CLIENT_ID")
    private Long adClientId;

    /**
     * 所属组织
     */
    @TableField(value = "AD_ORG_ID")
    private Long adOrgId;

    @TableField(value = "OWNERID")
    private Long ownerid;

    @TableField(value = "MODIFIERID")
    private Long modifierid;

    /**
     * 创建时间
     */
    @TableField(value = "CREATIONDATE")
    private Date creationdate;

    /**
     * 修改时间
     */
    @TableField(value = "MODIFIEDDATE")
    private Date modifieddate;

    /**
     * 可用
     */
    @TableField(value = "isactive")
    private String isactive;

    /**
     * 组织类型
     */
    @TableField(value = "orgtype")
    private String orgtype;

    /**
     * 编码名称
     */
    @TableField(value = "MIXNAME")
    private String mixname;

    /**
     * 编码
     */
    @TableField(value = "ecode")
    private String ecode;

    /**
     * 名称
     */
    @TableField(value = "ename")
    private String ename;

    /**
     * 上级组织
     */
    @TableField(value = "CP_C_ORGUP_ID")
    private Long cpCOrgupId;

    /**
     * 是否分公司
     */
    @TableField(value = "iscom")
    private String iscom;

    /**
     * 备注
     */
    @TableField(value = "REMARK")
    private String remark;

    @TableField(value = "LEFTVALUE")
    private Long leftvalue;

    @TableField(value = "RIGHTVALUE")
    private Long rightvalue;

    @TableField(value = "TREENO")
    private Long treeno;

    @TableField(value = "MODIFIERNAME")
    private String modifiername;

    @TableField(value = "OWNERNAME")
    private String ownername;

    /**
     * 修改人
     */
    @TableField(value = "MODIFIERENAME")
    private String modifierename;

    /**
     * 创建人
     */
    @TableField(value = "OWNERENAME")
    private String ownerename;

    /**
     * HR部门ID
     */
    @TableField(value = "HR002_ID")
    private String hr002Id;

    /**
     * HR组织ID
     */
    @TableField(value = "HR001_ID")
    private String hr001Id;

    @TableField(value = "issystem")
    private String issystem;

    /**
     * 店仓销售区域
     */
    @TableField(value = "CP_C_STOREORG_ID")
    private Long cpCStoreorgId;

    /**
     * HR接口返回的ID
     */
    @TableField(value = "HR_ID")
    private String hrId;

    /**
     * HR接口返回的排序
     */
    @TableField(value = "HR_SEQUENCE")
    private Integer hrSequence;

    /**
     * HR接口返回的父ID
     */
    @TableField(value = "HR_PARENT")
    private String hrParent;

    private static final long serialVersionUID = 1L;

    public static final String COL_ID = "id";

    public static final String COL_AD_CLIENT_ID = "AD_CLIENT_ID";

    public static final String COL_AD_ORG_ID = "AD_ORG_ID";

    public static final String COL_OWNERID = "OWNERID";

    public static final String COL_MODIFIERID = "MODIFIERID";

    public static final String COL_CREATIONDATE = "CREATIONDATE";

    public static final String COL_MODIFIEDDATE = "MODIFIEDDATE";

    public static final String COL_ISACTIVE = "isactive";

    public static final String COL_ORGTYPE = "orgtype";

    public static final String COL_MIXNAME = "MIXNAME";

    public static final String COL_ECODE = "ecode";

    public static final String COL_ENAME = "ename";

    public static final String COL_CP_C_ORGUP_ID = "CP_C_ORGUP_ID";

    public static final String COL_ISCOM = "iscom";

    public static final String COL_REMARK = "REMARK";

    public static final String COL_LEFTVALUE = "LEFTVALUE";

    public static final String COL_RIGHTVALUE = "RIGHTVALUE";

    public static final String COL_TREENO = "TREENO";

    public static final String COL_MODIFIERNAME = "MODIFIERNAME";

    public static final String COL_OWNERNAME = "OWNERNAME";

    public static final String COL_MODIFIERENAME = "MODIFIERENAME";

    public static final String COL_OWNERENAME = "OWNERENAME";

    public static final String COL_HR002_ID = "HR002_ID";

    public static final String COL_HR001_ID = "HR001_ID";

    public static final String COL_ISSYSTEM = "issystem";

    public static final String COL_CP_C_STOREORG_ID = "CP_C_STOREORG_ID";

    public static final String COL_HR_ID = "HR_ID";

    public static final String COL_HR_SEQUENCE = "HR_SEQUENCE";

    public static final String COL_HR_PARENT = "HR_PARENT";
}