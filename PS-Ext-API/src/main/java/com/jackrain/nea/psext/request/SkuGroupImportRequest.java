package com.jackrain.nea.psext.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: 汪聿森
 * @Date: Created in 2019-07-26 17:12
 * @Description : 组合商品导入实际条码（系统条码）
 */
@Data
public class SkuGroupImportRequest implements Serializable {
    /**
     * 条码
     */
    @JSONField(name = "PS_C_SKU_ECODE")
    private String psCSkuEcode;
    /**
     * 商品编码
     */
    @JSONField(name = "PS_C_PRO_ECODE")
    private String psCProEcode;
    /**
     * 商品名称
     */
    @JSONField(name = "PS_C_PRO_ENAME")
    private String psCProEname;
    /**
     * 颜色
     */
    @JSONField(name = "PS_C_CLR_ENAME")
    private String psCClrEname;
    /**
     * 尺寸
     */
    @JSONField(name = "PS_C_SIZE_ENAME")
    private String psCSizeEname;
    /**
     * 商品数量
     */
    @JSONField(name = "NUM")
    private BigDecimal num;
    /**
     * 分组
     */
    @JSONField(name = "GROUPNUM")
    private Integer groupNum;
    /**
     * 头子表关联列
     */
    @JSONField(name = "BK")
    private String bk;
    /**
     * 错误信息
     */
    @JSONField(name = "ERR_MSG")
    private String errMsg;

    /**
     * 吊牌价
     */
    @JSONField(name = "PRICELIST")
    private BigDecimal pricelist;

    /**
     * 规格
     */
    @JSONField(name = "SPEC")
    private String spec;

    /**
     * 是否赠品
     */
    @JSONField(name = "IS_GIFT")
    private String isGift;
}
