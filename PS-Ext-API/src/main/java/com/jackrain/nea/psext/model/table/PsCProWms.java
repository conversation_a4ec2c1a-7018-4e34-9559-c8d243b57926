package com.jackrain.nea.psext.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Description:
 *
 * @Author: guo.kw
 * @Since: 2022/7/5
 * create at: 2022/7/5 13:16
 */
@TableName(value = "ps_c_pro")
@Data
public class PsCProWms extends BaseModel {
    @TableId(value = "ID", type = IdType.AUTO)
    private Long id;

    @JSONField(name = "PS_C_BRAND_ID")
    private Long psCBrandId;

    @JSONField(name = "ECODE")
    private String ecode;

    //todo  箱规 没有字段
    /*@JSONField(name = "BOX_SPECS")
    private String boxSpecs;*/
// todo 暂无
    /*@JSONField(name = "MATERIELTYPE")
    private String materieltype;*/

    @JSONField(name = "IS_GIFT")
    private String isGift;

    /*@JSONField(name = "PRICELIST")
    private BigDecimal pricelist;*/

    // 是否启用批次效期管理
    @JSONField(name = "IS_ENABLE_EXPIRY")
    private String isEnableExpiry;

    // 是否序列号管理
    @JSONField(name = "IS_SERIAL_NUMBER")
    private String isSerialNumber;

    //是否启用溯源管理
    @JSONField(name = "IS_TRACE_SOURCE")
    private String isTraceSource;

    //计量单位
    @JSONField(name = "M_DIM3_ID")
    private Long mDim3Id;
    //物料组
    @JSONField(name = "M_DIM2_ID")
    private Long mDim2Id;
    //物料组
    @JSONField(name = "M_DIM8_ID")
    private Long mDim8Id;
    //商品简称
    @JSONField(name = "SIMPLE_ENAME")
    private String simpleEname;
    //规格幸好
    @JSONField(name = "SPEC_MODEL")
    private String specModel;
    //过量交货容量
    @JSONField(name = "DELIVERY_CAPACITY")
    private BigDecimal deliveryCapacity;
    //物料类型
    @JSONField(name = "M_DIM1_ID")
    private Long mDim1Id;
    //一级分类
    @JSONField(name = "M_DIM4_ID")
    private Long mDim4Id;
    //二级分类
    @JSONField(name = "M_DIM5_ID")
    private Long mDim5Id;
    //四级分类
    @JSONField(name = "M_DIM6_ID")
    private Long mDim6Id;
    //三级分类
    @JSONField(name = "M_DIM11_ID")
    private Long mDim11Id;

}
