package com.jackrain.nea.psext.api;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.Command;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;

/**
 * 组合商品提交接口
 * <AUTHOR>
 * @create 2019/03/04
 */
public interface SkuGroupSubmitCmd extends Command{

    @Override
    ValueHolder execute(QuerySession querySession) throws NDSException;

}
