package com.jackrain.nea.psext.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;

@TableName(value = "ps_c_pro")
@Data
@Document(index = "ps_c_pro", type = "ps_c_pro")
public class PsCPro extends BaseModel {
    private static final long serialVersionUID = 5728102069238886691L;
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "ID", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "PS_C_PRORULE_ID")
    @Field(type = FieldType.Long)
    private Long psCProruleId;

    @JSONField(name = "PS_C_SKURULE_ID")
    @Field(type = FieldType.Long)
    private Long psCSkuruleId;

    @JSONField(name = "ECODE")
    @Field(type = FieldType.Keyword)
    private String ecode;

    @JSONField(name = "ENAME")
    @Field(type = FieldType.Keyword)
    private String ename;

    @JSONField(name = "PS_C_BRAND_ID")
    @Field(type = FieldType.Long)
    private Long psCBrandId;

    @JSONField(name = "PS_C_SHAPEGROUP_ID")
    @Field(type = FieldType.Long)
    private Long psCShapegroupId;

    @JSONField(name = "GBCODE")
    @Field(type = FieldType.Keyword)
    private String gbcode;

    @JSONField(name = "REMARK")
    @Field(type = FieldType.Keyword)
    private String remark;


    @JSONField(name = "STATUS")
    @Field(type = FieldType.Long)
    private Long status;

    @JSONField(name = "PS_C_SPEC1GROUP_ID")
    @Field(type = FieldType.Long)
    private Long psCSpec1groupId;

    @JSONField(name = "PS_C_SPEC2GROUP_ID")
    @Field(type = FieldType.Long)
    private Long psCSpec2groupId;

    @JSONField(name = "PS_C_SPEC3GROUP_ID")
    @Field(type = FieldType.Long)
    private Long psCSpec3groupId;

    @JSONField(name = "PS_C_SPEC4GROUP_ID")
    @Field(type = FieldType.Long)
    private Long psCSpec4groupId;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "CLRS")
    @Field(type = FieldType.Keyword)
    private String clrs;

    @JSONField(name = "SIZES")
    @Field(type = FieldType.Keyword)
    private String sizes;

    @JSONField(name = "IS_GROUP")
    @Field(type = FieldType.Keyword)
    private String isGroup;

    @JSONField(name = "GROUP_TYPE")
    @Field(type = FieldType.Integer)
    private Integer groupType;

    @JSONField(name = "WARE_TYPE")
    @Field(type = FieldType.Integer)
    private Integer wareType;

    @JSONField(name = "WEIGHT")
    @Field(type = FieldType.Keyword)
    private String weight;

    @JSONField(name = "IS_GIFT")
    @Field(type = FieldType.Keyword)
    private String isGift;

    @JSONField(name = "IS_AIRFORBIDDEN")
    @Field(type = FieldType.Keyword)
    private String isAirforbidden;

    @JSONField(name = "MAIN_SUPPLIER")
    @Field(type = FieldType.Keyword)
    private String mainSupplier;

    @JSONField(name = "IMAGE")
    @Field(type = FieldType.Keyword)
    private String image;

    @JSONField(name = "PS_C_SPEC_IDS")
    @Field(type = FieldType.Keyword)
    private String psCSpecIds;

    @JSONField(name = "PS_C_SPECOBJ_IDS")
    @Field(type = FieldType.Keyword)
    private String psCSpecobjIds;


    @JSONField(name = "IMAGE_SKU")
    @Field(type = FieldType.Keyword)
    private String imageSku;

    @JSONField(name = "VIDEO")
    @Field(type = FieldType.Keyword)
    private String video;

    @JSONField(name = "M_DIM14_ID")
    private Integer mDim14Id;

    @JSONField(name = "M_DIM7_ID")
    private Integer mDim7Id;

    /**
     * 三级
     */
    @JSONField(name = "M_DIM11_ID")
    private Integer mDim11Id;

    @JSONField(name = "M_DIM2_ID")
    private Integer mDim2Id;

    @JSONField(name = "M_DIM3_ID")
    private Integer mDim3Id;

    @JSONField(name = "M_DIM4_ID")
    private Integer mDim4Id;

    @JSONField(name = "M_DIM5_ID")
    private Integer mDim5Id;

    /**
     * 四级
     */
    @JSONField(name = "M_DIM6_ID")
    private Integer mDim6Id;

    @JSONField(name = "M_DIM9_ID")
    private Integer mDim9Id;

    /**
     * 零级
     */
    @JSONField(name = "M_DIM12_ID")
    private Integer mDim12Id;


    @JSONField(name = "DELIVERY_CAPACITY")
    private BigDecimal deliveryCapacity;


    // 是否启用批次效期管理
    @JSONField(name = "IS_ENABLE_EXPIRY")
    private String isEnableExpiry;

    @JSONField(name = "PRICE_LIST")
    @TableField(value = "PRICE_LIST")
    private BigDecimal pricelist;

    @JSONField(name = "PRICE_COST_LIST")
    private BigDecimal priceCostList;

    @JSONField(name = "PRICE_SALE_LIST")
    private BigDecimal priceSaleList;

    @JSONField(name = "SPEC_MODEL")
    private String specModel;

    @JSONField(name = "TAX_RATE")
    private BigDecimal taxRate;

    @JSONField(name = "M_DIM10_ID")
    private Integer mDim10Id;

    @JSONField(name = "PRODUCT_ALIAS")
    private String productAlias;

    @JSONField(name = "PRODUCT_CLASSIFY")
    private String productClassify;

    @JSONField(name = "product_small_category")
    private String productSmallCategory;

    @JSONField(name = "product_coefficient")
    private BigDecimal productCoefficient;

    @JSONField(name = "VOLUME")
    private BigDecimal volume;

    @JSONField(name = "LENGTH")
    private BigDecimal length;

    @JSONField(name = "WIDTH")
    private BigDecimal width;

    @JSONField(name = "HEIGHT")
    private BigDecimal height;

    /**
     * 是否序列号管理
     */
    @JSONField(name = "IS_SERIAL_NUMBER")
    private String isSerialNumber;
}