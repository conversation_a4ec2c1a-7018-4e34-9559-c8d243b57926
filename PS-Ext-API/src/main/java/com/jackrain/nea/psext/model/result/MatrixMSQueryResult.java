package com.jackrain.nea.psext.model.result;
import com.jackrain.nea.psext.model.table.PsCMatchsizeItem;
import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @version :1.0
 * description ：矩阵相关
 * @date :2019/10/10 15:27
 */
@Data
public class MatrixMSQueryResult implements Serializable {

    /**
     * 配码信息
     */
    private HashMap<Long, List<PsCMatchsizeItem>> msMap;
    /**
     * 号型组
     */
    private HashMap<String, MatrixMsShapeResult> shapeList;
}
