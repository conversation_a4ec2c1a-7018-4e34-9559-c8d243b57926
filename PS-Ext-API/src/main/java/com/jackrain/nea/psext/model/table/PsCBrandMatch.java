package com.jackrain.nea.psext.model.table;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * @Author: jg.zhan
 * @Date:  2022/5/12 16:06
 * @Description:  
 */
/**
    * 品牌匹配表
    */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "ps_c_brand_match")
public class PsCBrandMatch implements Serializable {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 所属公司
     */
    @TableField(value = "AD_CLIENT_ID")
    private Long adClientId;

    /**
     * 所属组织
     */
    @TableField(value = "AD_ORG_ID")
    private Long adOrgId;

    /**
     * 解析字段
     */
    @TableField(value = "parse_fields")
    private String parseFields;

    /**
     * 解析位数
     */
    @TableField(value = "parse_number")
    private String parseNumber;

    /**
     * 解析规则
     */
    @TableField(value = "parse_rule")
    private String parseRule;

    /**
     * 启用
     */
    @TableField(value = "isactive")
    private String isactive;

    @TableField(value = "OWNERID")
    private Long ownerid;

    @TableField(value = "MODIFIERID")
    private Long modifierid;

    /**
     * 创建时间
     */
    @TableField(value = "CREATIONDATE")
    private Date creationdate;

    /**
     * 修改时间
     */
    @TableField(value = "MODIFIEDDATE")
    private Date modifieddate;

    @TableField(value = "MODIFIERNAME")
    private String modifiername;

    @TableField(value = "OWNERNAME")
    private String ownername;

    /**
     * 修改人
     */
    @TableField(value = "MODIFIERENAME")
    private String modifierename;

    /**
     * 创建人
     */
    @TableField(value = "OWNERENAME")
    private String ownerename;

    private static final long serialVersionUID = 1L;

    public static final String COL_ID = "id";

    public static final String COL_AD_CLIENT_ID = "AD_CLIENT_ID";

    public static final String COL_AD_ORG_ID = "AD_ORG_ID";

    public static final String COL_PARSE_FIELDS = "parse_fields";

    public static final String COL_PARSE_NUMBER = "parse_number";

    public static final String COL_PARSE_RULE = "parse_rule";

    public static final String COL_ISACTIVE = "isactive";

    public static final String COL_OWNERID = "OWNERID";

    public static final String COL_MODIFIERID = "MODIFIERID";

    public static final String COL_CREATIONDATE = "CREATIONDATE";

    public static final String COL_MODIFIEDDATE = "MODIFIEDDATE";

    public static final String COL_MODIFIERNAME = "MODIFIERNAME";

    public static final String COL_OWNERNAME = "OWNERNAME";

    public static final String COL_MODIFIERENAME = "MODIFIERENAME";

    public static final String COL_OWNERENAME = "OWNERENAME";
}