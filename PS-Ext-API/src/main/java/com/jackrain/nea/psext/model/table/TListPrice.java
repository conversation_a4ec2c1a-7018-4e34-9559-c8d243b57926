package com.jackrain.nea.psext.model.table;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * @Author: jg.zhan
 * @Date:  2022/5/20 15:37
 * @Description:  
 */
/**
    * ListPrice中间表
    */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "t_list_price")
public class TListPrice implements Serializable {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @TableField(value = "gln")
    private String gln;

    @TableField(value = "tp")
    private String tp;

    @TableField(value = "doctype")
    private String doctype;

    @TableField(value = "gui")
    private String gui;

    @TableField(value = "idoc")
    private String idoc;

    @TableField(value = "product_id")
    private String productId;

    @TableField(value = "list_price")
    private BigDecimal listPrice;

    @TableField(value = "unitof_measure")
    private String unitofMeasure;

    @TableField(value = "valid_from")
    private String validFrom;

    @TableField(value = "valid_to")
    private String validTo;

    @TableField(value = "sales_org")
    private String salesOrg;

    @TableField(value = "company_id")
    private String companyId;

    @TableField(value = "`action`")
    private String action;

    /**
     * 状态，0-新增
     */
    @TableField(value = "`status`")
    private Byte status;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 是否删除，0-否，1-是
     */
    @TableField(value = "deleted")
    private Byte deleted;

    /**
     * 转化时间
     */
    @TableField(value = "transdate")
    private Date transdate;

    /**
     * 转换备注
     */
    @TableField(value = "sysremark")
    private String sysremark;

    /**
     * 转换次数
     */
    @TableField(value = "trans_count")
    private Integer transCount;

    /**
     * 更新状态（0=初始 -1=异常  1=成功）
     */
    @TableField(value = "trans_status")
    private Integer transStatus;

    /**
     * 下次执行时间
     */
    @TableField(value = "next_trans_time")
    private Date nextTransTime;

    /**
     * 异常信息
     */
    @TableField(value = "err_msg")
    private String errMsg;

    private static final long serialVersionUID = 1L;

    public static final String COL_ID = "id";

    public static final String COL_GLN = "gln";

    public static final String COL_TP = "tp";

    public static final String COL_DOCTYPE = "doctype";

    public static final String COL_GUI = "gui";

    public static final String COL_IDOC = "idoc";

    public static final String COL_PRODUCT_ID = "product_id";

    public static final String COL_LIST_PRICE = "list_price";

    public static final String COL_UNITOF_MEASURE = "unitof_measure";

    public static final String COL_VALID_FROM = "valid_from";

    public static final String COL_VALID_TO = "valid_to";

    public static final String COL_SALES_ORG = "sales_org";

    public static final String COL_COMPANY_ID = "company_id";

    public static final String COL_ACTION = "action";

    public static final String COL_STATUS = "status";

    public static final String COL_CREATE_TIME = "create_time";

    public static final String COL_UPDATE_TIME = "update_time";

    public static final String COL_DELETED = "deleted";

    public static final String COL_TRANSDATE = "transdate";

    public static final String COL_SYSREMARK = "sysremark";

    public static final String COL_TRANS_COUNT = "trans_count";

    public static final String COL_TRANS_STATUS = "trans_status";

    public static final String COL_NEXT_TRANS_TIME = "next_trans_time";

    public static final String COL_ERR_MSG = "err_msg";
}