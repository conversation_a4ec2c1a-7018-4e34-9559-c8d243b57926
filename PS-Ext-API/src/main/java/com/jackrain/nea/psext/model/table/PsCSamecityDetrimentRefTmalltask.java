package com.jackrain.nea.psext.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

@TableName(value = "ps_c_samecity_detriment_ref_tmalltask")
@Data
public class PsCSamecityDetrimentRefTmalltask extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_ECODE")
    private String cpCShopEcode;

    @JSONField(name = "CP_C_SHOP_ENAME")
    private String cpCShopEname;

    @JSONField(name = "CP_C_SALESROOM_ID")
    private Long cpCSalesroomId;

    @JSONField(name = "CP_C_SALESROOM_ECODE")
    private String cpCSalesroomEcode;

    @JSONField(name = "CP_C_SALESROOM_ENAME")
    private String cpCSalesroomEname;

    @JSONField(name = "OPERATION_TYPE")
    private String operationType;

    @JSONField(name = "IS_NEED_TO_DEAL")
    private Long isNeedToDeal;

    @JSONField(name = "DEAL_TIMES")
    private Long dealTimes;

    @JSONField(name = "PS_C_PRO_ID")
    private Long psCProId;

    @JSONField(name = "PS_C_PRO_ECODE")
    private String psCProEcode;

    @JSONField(name = "PS_C_PRO_ENAME")
    private String psCProEname;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "CP_C_SHOP_NICK_NAME")
    private String cpCShopNickName;

    @JSONField(name = "PS_C_SAMECITY_PURCHASE_REF_ID")
    private Long psCSamecityPurchaseRefId;
    @JSONField(name = "NUMIID")
    private String numiid;
}