package com.jackrain.nea.psext.api;

import com.jackrain.nea.psext.model.table.PsCCollShopProMapping;
import com.jackrain.nea.sys.domain.ValueHolderV14;

import java.util.List;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/8/24
 */
public interface PsCCollShopProMappingQueryCmd {


    ValueHolderV14<PsCCollShopProMapping> queryByShopIdAndSku(Long shopId, Long skuId);

    ValueHolderV14<List<PsCCollShopProMapping>> queryByShopId(Long shopId);

    ValueHolderV14<List<PsCCollShopProMapping>> queryProMappingByShopIdAndSku(Long shopId,List<Long> skuId);

}
