package com.jackrain.nea.psext.common;

/**
 * <AUTHOR> lin yu
 * @since 2019/7/11
 * create at : 2019/7/11 16:07
 */
public interface PsExtConstantsIF {

    /**
     * 商品类型
     */
    int NORMAL_PRODUCT = 0;   //正常商品
    int GOODY_BAG_PRODUCT = 1;   //福袋商品
    int GROUP_PRODUCT = 2;   //组合商品
    int PRESELL_PRODUCT = 3;   //预售商品

    /**
     * 实际福袋前缀
     */
    String LUCKY_BAG_PRODUCT_KEY_PREFIX = "PS:LUCKYBAG:";
    String REAL_SKU = "COMBINED:SKUGROUP:";


    String SKUGROUP_TEMPLATE_UPLOAD_PATH = "ps.control.skugroup_template_upload_base";
    String ENDPOINT_KEY = "r3.oss.endpoint";
    String ACCESS_KEY = "r3.oss.accessKey";
    String SECRET_KEY = "r3.oss.secretKey";
    String BUCKET_NAME = "r3.oss.bucketName";
    String TIMEOUT = "r3.oss.timeout";


    String ERPDWPRODUCTSTANDPLAT="r3.ps.erpdwproductstandplat";

    /**
     * 未审核
     */
    int SETTLE_CODE_STATUS_UN_AUDIT = 1;

    /**
     * 已审核
     */
    int SETTLE_CODE_STATUS_AUDITED = 2;

    /**
     * 已结案
     */
    int SETTLE_CODE_STATUS_FINISH = 3;

    /**
     * 作废
     */
    int SETTLE_CODE_STATUS_void = 4;


    /**
     * 销售价策略单据状态-未作废
     */
    static int PSCPRICESTRATEGY_NOT_OBSOLETE = 0;

    /**
     * 基准价—吊牌价
     */
    int BASEPRICE_PRICETAG = 1;

    /**
     * 基准价—全国价
     */
    int BASEPRICE_NATIONALPRICE = 2;

    /**
     * 系数类型—资金折扣价
     */
    int COEFFICIENT_TYPE_DISCOUNT = 1;

    /**
     * 系数类型—销售价
     */
    int COEFFICIENT_TYPE_SALE = 2;

    /**
     * 销售价策略单据状态-已作废
     */
    int PSCPRICESTRATEGY_ABOLISHED = 1;

    /**
     * 销售价调整单据状态-未审核
     */
    int PSCPRICEADJUST_UNREVIEWED = 1;

    /**
     * 销售价调整单据状态-已审核
     */
    int PSCPRICEADJUST_AUDITED = 2;

    /**
     * 销售价调整单据状态-已结案
     */
    int PSCPRICEADJUST_CLOSED = 3;
    /**
     * 销售价调整单据状态-作废
     */

    int PSCPRICEADJUST_ABOLISHED = 4;
    String EFFECTIVE = "Y";
    String INVALID = "N";

    /**
     * 查询分页
     */
    int SELECT_PAGE_SIZE_FIFTY = 50;
    int SELECT_PAGE_SIZE_TWO_HUNDRED = 200;
    int SELECT_PAGE_SIZE_FIVE_HUNDRED = 500;
    int MAX_SELECT_PAGE_SIZE = 1000;


    /**
     * 单据状态（箱定义）
     * 1=未审核
     * 2=已审核
     * 3=已作废
     */
    int TEUS_STATUS_WAIT = 1;
    int TEUS_STATUS_AUDIT = 2;
    int TEUS_STATUS_VOID = 3;


    /**
     * 箱状态（箱定义）
     * 1=待入库
     * 2=已入库未拆箱
     * 3=已拆箱
     */
    int TEUS_STATUS_WAIT_IN = 1;
    int TEUS_STATUS_IN = 2;
    int TEUS_STATUS_SPLIT = 3;

    String TEUS_TYPE_PM = "1";//配码箱
    String TEUS_TYPE_ZY = "2";//自由箱

    /**
     * 传WMS状态
     * 0=未传wms
     * 1=传wms中
     * 2=传wms成功
     * 3=传wms失败
     */
    int TO_WMS_STATUS_WAIT = 0;
    int TO_WMS_STATUS_GOING = 1;
    int TO_WMS_STATUS_SUCCESS = 2;
    int TO_WMS_STATUS_FAIL = 3;

    /**
     * 新增门店商品绑定关系
     * 新增or修改
     */
    String SYNCSAMECITYPURCHASEREF_ACTIONTYPE_ADD = "ADD";
    String SYNCSAMECITYPURCHASEREF_ACTIONTYPE_DELETE = "DELETE";

    String IS_CURRENT_Y = "Y";//当前条码
    String IS_CURRENT_N = "N";//同款条码

    /**
     * 可用
     */
    String IS_ACTIVE_Y = "Y";
    String IS_ACTIVE_N = "N";
    /**
     * WMS商品数据推送表 状态
     */
    /*** 未传*/
    public static final Integer PS_C_SKU_PUSH_WMS_MID_NO_SEND = 0;
    /*** 传中*/
    public static final Integer PS_C_SKU_PUSH_WMS_MID_SENDING = 2;
    /*** 已传*/
    public static final Integer PS_C_SKU_PUSH_WMS_MID_SENDED = 1;

    /**
     * WMS商品映射缓存前缀，根据item查询
     */
    String ITEM_REDIS_KEY_PREFIX = "ps:sku:wms:mapping:item";
    /**
     * WMS商品映射缓存前缀，根据ECODE查询
     */
    String ECODE_REDIS_KEY_PREFIX = "ps:sku:wms:mapping:ecode";
}
