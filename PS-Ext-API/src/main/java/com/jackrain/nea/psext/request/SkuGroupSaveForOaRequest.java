package com.jackrain.nea.psext.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/3/20 14:29
 * @Description
 */
@Data
public class SkuGroupSaveForOaRequest implements Serializable {

    /**
     * 是否可拆分
     */
    private Integer canSplit;

    private List<SkuGroupSaveForOaItem> skuItemList;

    @Data
    public static class SkuGroupSaveForOaItem implements Serializable {

        private Integer lineNo;

        private String skuGroupName;

        /**
         * 组合商品编码-- 这个字段正常OA不会下发的。这次就为了肉业导入数据使用。后面删掉
         */
        @Deprecated
        private String skuGroupCode;

        private List<ProGroupSaveForOaItem> proItemList;

    }


    @Data
    public static class ProGroupSaveForOaItem implements Serializable {

        private String proCode;

        private Integer num;

        private Integer isGift;
    }
}
