package com.jackrain.nea.psext.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: 汪聿森
 * @Date: Created in 2019-07-18 10:11
 * @Description : 实际条码信息
 */
@Data
public class VirtualSkuRealInfoRequest implements Serializable {
    //实际条码ID
    @JSONField(name = "ID")
    private Long id;
    //实际条码ECODE
    @JSONField(name = "ECODE")
    private String eCode;
    //组合数量
    @JSONField(name = "NUM")
    private BigDecimal num;
    //分组编号
    @JSONField(name = "GROUPNUM")
    private Integer groupNum;
}
