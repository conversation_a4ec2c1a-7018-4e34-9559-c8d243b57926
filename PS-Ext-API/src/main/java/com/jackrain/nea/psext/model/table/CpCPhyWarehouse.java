package com.jackrain.nea.psext.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@TableName(value = "cp_c_phy_warehouse")
@Data
@Document(index = "cp_c_phy_warehouse",type = "cp_c_phy_warehouse")
public class CpCPhyWarehouse extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "ECODE")
    @Field(type = FieldType.Keyword)
    private String ecode;

    @JSONField(name = "ENAME")
    @Field(type = FieldType.Keyword)
    private String ename;

    @JSONField(name = "WMS_WAREHOUSE_CODE")
    @Field(type = FieldType.Keyword)
    private String wmsWarehouseCode;

    @JSONField(name = "OWNER_CODE")
    @Field(type = FieldType.Keyword)
    private String ownerCode;

    @JSONField(name = "WMS_ACCOUNT")
    @Field(type = FieldType.Keyword)
    private String wmsAccount;

    @JSONField(name = "WMS_URL")
    @Field(type = FieldType.Keyword)
    private String wmsUrl;

    @JSONField(name = "IS_SAP")
    @Field(type = FieldType.Integer)
    private Integer isSap;

    @JSONField(name = "REMARK")
    @Field(type = FieldType.Keyword)
    private String remark;

    @JSONField(name = "CONTACT_NAME")
    @Field(type = FieldType.Keyword)
    private String contactName;

    @JSONField(name = "MOBILEPHONE_NUM")
    @Field(type = FieldType.Keyword)
    private String mobilephoneNum;

    @JSONField(name = "PHONE_NUM")
    @Field(type = FieldType.Keyword)
    private String phoneNum;

    @JSONField(name = "SELLER_PROVINCE_ID")
    @Field(type = FieldType.Long)
    private Long sellerProvinceId;

    @JSONField(name = "SELLER_CITY_ID")
    @Field(type = FieldType.Long)
    private Long sellerCityId;

    @JSONField(name = "SELLER_AREA_ID")
    @Field(type = FieldType.Long)
    private Long sellerAreaId;

    @JSONField(name = "SELLER_ZIP")
    @Field(type = FieldType.Keyword)
    private String sellerZip;

    @JSONField(name = "SEND_ADDRESS")
    @Field(type = FieldType.Keyword)
    private String sendAddress;

    @JSONField(name = "CP_C_CUSTOMER_ID")
    @Field(type = FieldType.Long)
    private Long cpCCustomerId;

    @JSONField(name = "CP_C_CUSTOMER_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCCustomerEcode;

    @JSONField(name = "CP_C_CUSTOMER_ENAME")
    @Field(type = FieldType.Keyword)
    private String cpCCustomerEname;

    @JSONField(name = "CP_C_STORE_ID")
    @Field(type = FieldType.Long)
    private Long cpCStoreId;

    @JSONField(name = "CP_C_STORE_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCStoreEcode;

    @JSONField(name = "CP_C_STORE_ENAME")
    @Field(type = FieldType.Keyword)
    private String cpCStoreEname;

    @JSONField(name = "QTY_PKG_MAX")
    @Field(type = FieldType.Double)
    private BigDecimal qtyPkgMax;

    @JSONField(name = "WMS_CONTROL_WAREHOUSE")
    @Field(type = FieldType.Integer)
    private Integer wmsControlWarehouse;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "CUT_TIME")
    @Field(type = FieldType.Date)
    private Date cutTime;

    @JSONField(name = "JD_CUT_TIME")
    @Field(type = FieldType.Date)
    private Date jdCutTime;

    @JSONField(name = "SELF_MEMTION_POINT")
    private String selfMemtionPoint;

    @JSONField(name = "WMS_TYPE")
    @TableField(fill = FieldFill.UPDATE)
    private String wmsType;
}