package com.jackrain.nea.psext.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;

@TableName(value = "ps_c_fair_sample")
@Data
public class PsCFairSample extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "B_FAIR_ID")
    private Long bFairId;

    @JSONField(name = "FR_END_MA")
    private Long frEndMa;

    @JSONField(name = "STATUS")
    private String status;

    @JSONField(name = "M_SAMPLE_ID")
    private Long mSampleId;

    @JSONField(name = "DIVISION2")
    private String division2;

    @JSONField(name = "IS_KEY_DIVISION")
    private String isKeyDivision;

    @JSONField(name = "QUARTER")
    private Integer quarter;

    @JSONField(name = "SHOE_CHANNEL")
    private Integer shoeChannel;

    @JSONField(name = "COMEDATEE_MONTH")
    private Integer comedateeMonth;

    @JSONField(name = "COMEDATES_MONTH")
    private Integer comedatesMonth;

    @JSONField(name = "COMEDATEW_MONTH")
    private Integer comedatewMonth;

    @JSONField(name = "COMEDATEEN_MONTH")
    private Integer comedateenMonth;

    @JSONField(name = "COMEDATEN_MONTH")
    private Integer comedatenMonth;

    @JSONField(name = "MARKETDATE")
    private Integer marketdate;

    @JSONField(name = "COMEDATEE")
    private Integer comedatee;

    @JSONField(name = "COMEDATES")
    private Integer comedates;

    @JSONField(name = "COMEDATEW")
    private Integer comedatew;

    @JSONField(name = "COMEDATEEN")
    private Integer comedateen;

    @JSONField(name = "COMEDATEN")
    private Integer comedaten;

    @JSONField(name = "COMEDATEEN2")
    private Integer comedateen2;

    @JSONField(name = "FLOWNO")
    private String flowno;

    @JSONField(name = "PRODUCT_INFORMATION")
    private String productInformation;

    @JSONField(name = "M_SIZE_ID")
    private Integer mSizeId;

    @JSONField(name = "PRICELIST")
    private BigDecimal pricelist;

    @JSONField(name = "CERTAINLY")
    private Integer certainly;

    @JSONField(name = "POSTER")
    private Integer poster;

    @JSONField(name = "SELF_SELECTED")
    private Integer selfSelected;

    @JSONField(name = "NC_TOP")
    private Integer ncTop;

    @JSONField(name = "EC_TOP")
    private Integer ecTop;

    @JSONField(name = "SC_TOP")
    private Integer scTop;

    @JSONField(name = "TEST_PRODUCT")
    private Integer testProduct;

    @JSONField(name = "FLAGSHIP_PRODUCT")
    private Integer flagshipProduct;

    @JSONField(name = "CITY_PRODUCT")
    private Integer cityProduct;

    @JSONField(name = "OTO")
    private Integer oto;

    @JSONField(name = "ANALYSIS2")
    private String analysis2;

    @JSONField(name = "ANALYSIS3")
    private String analysis3;

    @JSONField(name = "IS_6_MULTIPLE")
    private Integer is6Multiple;

    @JSONField(name = "IS_BAS")
    private Integer isBas;

    @JSONField(name = "ANALYSIS4")
    private String analysis4;

    @JSONField(name = "MODEL_PICTURE")
    private String modelPicture;

    @JSONField(name = "MATCH_CODE")
    private String matchCode;

    @JSONField(name = "IS_DELETE")
    private String isDelete;

    @JSONField(name = "REMAKE")
    private String remake;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "CHANNEL")
    private String channel;
}
