package com.jackrain.nea.psext.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;

@TableName(value = "ps_c_price_strategy_discount_type")
@Data
public class PsCPriceStrategyDiscountType extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "PS_C_PRICE_STRATEGY_ID")
    private Long psCPriceStrategyId;

    @JSONField(name = "PS_C_PRODIM_DISCOUNT_ID")
    private Long psCProdimDiscountId;

    @JSONField(name = "PRICE_STANDARD")
    private Integer priceStandard;

    @JSONField(name = "COEFFICIENT_TYPE")
    private String coefficientType;

    @JSONField(name = "COEFFICIENT")
    private BigDecimal coefficient;

    @JSONField(name = "REMARK")
    private String remark;

    @JSONField(name = "VERSION")
    private Long version;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}