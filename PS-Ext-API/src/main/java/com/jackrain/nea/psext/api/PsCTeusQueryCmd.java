package com.jackrain.nea.psext.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.psext.model.result.PsCTeusWmsResult;
import com.jackrain.nea.psext.model.table.PsCTeus;
import com.jackrain.nea.psext.model.table.PsCTeusItem;
import com.jackrain.nea.psext.request.PsCTeusRequest;
import com.jackrain.nea.psext.request.PsTeusItemsQueryRequest;
import com.jackrain.nea.psext.request.PsTeusQueryRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;

import java.util.List;
import java.util.Map;

/**
 * @author: 李杰
 * @since: 2019/10/18
 * create at : 2019/10/18 16:34
 */
public interface PsCTeusQueryCmd {

    ValueHolderV14<List<PsCTeusRequest>> queryTeusByEcode(List<String> ecodes) throws NDSException;

    ValueHolderV14<List<PsCTeus>> queryTeusInfoByEcode(List<String> ecodes) throws NDSException;

    /**
     * 根据箱Id查询箱信息
     *
     * @param teusIds
     * @return
     * @throws NDSException
     */
    ValueHolderV14<List<PsCTeus>> queryTeusInfoById(List<Long> teusIds) throws NDSException;

    /**
     * 根据箱查询箱明细（批量）
     *
     * @param request 箱明细查询请求体
     * @return 箱明细信息
     * @throws NDSException 框架异常
     */
    ValueHolderV14<List<PsCTeusItem>> queryTeusItemsByTeus(PsTeusItemsQueryRequest request) throws NDSException;

    /**
     * 根据箱号查询箱明细（批量）
     *
     * @param boxIds    箱id集合
     * @param boxECodes 箱号集合
     * @return 箱明细信息
     * @throws NDSException 框架异常
     */
    @Deprecated
    ValueHolderV14<List<PsCTeusItem>> queryBoxItemsByBoxInfo(List<Long> boxIds, List<String> boxECodes) throws NDSException;

    /**
     * 根据指定条件查询箱信息
     *
     * @param list
     * @return
     */
    ValueHolderV14<Map<String, PsCTeus>> queryTeus(List<PsTeusQueryRequest> list);


    /**
     * 批量查询箱定义信息
     *
     * @param whereKeys in(条件)
     * @return List<PsCTeus>
     */
    ValueHolderV14<List<PsCTeus>> queryTeuListByArrayCondition(JSONObject whereKeys);

    /**
     * 查询 待传 wms 箱码 - 2020.01.02 cs 添加
     *
     * @param request
     * @return
     * @throws NDSException
     */
    ValueHolderV14<List<PsCTeusWmsResult>> queryTeusForWms(PsCTeusRequest request) throws NDSException;

}
