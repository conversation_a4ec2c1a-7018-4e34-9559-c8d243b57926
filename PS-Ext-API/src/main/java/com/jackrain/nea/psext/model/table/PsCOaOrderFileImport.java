package com.jackrain.nea.psext.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.util.Date;

/**
 * 文件导入记录表
 * 
 * <AUTHOR> Generated
 * @since 2024
 */
@TableName(value = "ps_c_oa_order_file_import")
@Data
@Document(index = "ps_c_oa_order_file_import", type = "ps_c_oa_order_file_import")
public class PsCOaOrderFileImport extends BaseModel {
    
    /**
     * 主键ID
     */
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    /**
     * 导入文件路径或名称
     */
    @JSONField(name = "IMPORT_FILE")
    @Field(type = FieldType.Keyword)
    private String importFile;

    /**
     * 导出文件路径或名称
     */
    @JSONField(name = "EXPORT_FILE")
    @Field(type = FieldType.Keyword)
    private String exportFile;

    /**
     * 解析结果
     */
    @JSONField(name = "PARSE_RESULT")
    @Field(type = FieldType.Text)
    private String parseResult;

    /**
     * 导入人ID
     */
    @JSONField(name = "IMPORT_USER_ID")
    @Field(type = FieldType.Long)
    private Long importUserId;

    /**
     * 导入人姓名
     */
    @JSONField(name = "IMPORT_USER_NAME")
    @Field(type = FieldType.Keyword)
    private String importUserName;

    /**
     * 导入时间
     */
    @JSONField(name = "IMPORT_TIME")
    @Field(type = FieldType.Date)
    private Date importTime;

    /**
     * 文件大小（字节）
     */
    @JSONField(name = "FILE_SIZE")
    @Field(type = FieldType.Long)
    private Long fileSize;

    /**
     * 文件类型
     */
    @JSONField(name = "FILE_TYPE")
    @Field(type = FieldType.Keyword)
    private String fileType;

    /**
     * 处理状态：0-待处理，1-处理中，2-处理成功，3-处理失败
     */
    @JSONField(name = "PROCESS_STATUS")
    @Field(type = FieldType.Integer)
    private Integer processStatus;

    /**
     * 错误信息
     */
    @JSONField(name = "ERROR_MESSAGE")
    @Field(type = FieldType.Text)
    private String errorMessage;

    /**
     * 备注
     */
    @JSONField(name = "REMARK")
    @Field(type = FieldType.Text)
    private String remark;
}
