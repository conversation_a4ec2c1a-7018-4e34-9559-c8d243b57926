package com.jackrain.nea.psext.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: 汪聿森
 * @Date: Created in 2019-07-26 17:12
 * @Description : 组合商品导入临时表
 */
@Data
public class SkuGroupTempImportRequest implements Serializable {
    /**
     * 组合商品编码
     */
    @JSONField(name = "SKU_GROUP_CODE")
    private String skuGroupCode;

    /**
     * 组合商品名称
     */
    @JSONField(name = "SKU_GROUP_NAME")
    private String skuGroupName;

    /**
     * 是否可拆单
     */
    @JSONField(name = "IS_SPLIT")
    private String isSplit;

    /**
     * 商品编码
     */
    @JSONField(name = "SKU_ECODE")
    private String skuEcode;

    /**
     * 商品数量
     */
    @JSONField(name = "NUM")
    private BigDecimal num;

    /**
     * 是否赠品
     */
    @JSONField(name = "IS_GIFT")
    private String isGift;
}