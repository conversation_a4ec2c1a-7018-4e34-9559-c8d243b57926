package com.jackrain.nea.psext.api;

import com.jackrain.nea.psext.model.request.ValidityDefinitionQueryRequest;
import com.jackrain.nea.psext.model.table.PsCValidityDefinition;
import com.jackrain.nea.sys.domain.ValueHolderV14;

import java.util.List;
import java.util.Map;

/**
 * 标准效期定义查询服务
 *
 * <AUTHOR>
 * @date 2022/06/17
 */
public interface ValidityDefinitionQueryCmd {

    Map<Long, PsCValidityDefinition> queryValidityDefinitionByPsCSkuId(ValidityDefinitionQueryRequest request);

    /**
     * 查询标准效期定义为潜在大效期的集合
     * @param id
     * @return
     */
    ValueHolderV14<List<PsCValidityDefinition>> queryValidityDefinitionBymDim14Id(Long id);



    ValueHolderV14<List<PsCValidityDefinition>> selectPsCValidityDefinitionByType(Long psCProdimItemId,String type, Long skuId);

}
