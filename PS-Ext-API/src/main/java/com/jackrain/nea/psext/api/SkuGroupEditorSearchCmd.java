package com.jackrain.nea.psext.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.sys.Command;
import com.jackrain.nea.sys.domain.ValueHolderV14;

import java.util.HashMap;

/**
 * 组合商品编辑初始化查询接口【主表，子表，子表第一条数据对应的子子表所有数据】
 *
 * <AUTHOR>
 * @create 2019/03/11
 */
public interface SkuGroupEditorSearchCmd extends Command {

    ValueHolderV14<HashMap<String, String>> groupSkuEditorSearch(JSONObject json);

}
