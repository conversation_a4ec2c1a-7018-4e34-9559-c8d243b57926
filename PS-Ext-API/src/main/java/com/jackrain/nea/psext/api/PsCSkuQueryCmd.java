package com.jackrain.nea.psext.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.psext.model.table.PsCSku;
import com.jackrain.nea.psext.result.PdaQuerySkuResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;

import java.util.Collection;
import java.util.List;

/**
 * @ClassName : PsCSkuQueryCmd
 * @Description :
 * <AUTHOR> CD
 * @Date: 2021-08-13 10:25
 */
public interface PsCSkuQueryCmd {

    /**
     *  提供给PDA的根据条件查询条码信息接口
     * @param param
     * @return
     */
    ValueHolderV14<List<PdaQuerySkuResult>> pdaGetSkuList(JSONObject param);

    ValueHolderV14<List<PsCSku>> querySKUByEcodeList(List<String> ecodeList);

    ValueHolderV14<PsCSku> querySkuById(Long id);

    ValueHolderV14<List<PsCSku>> querySkuByIds(Collection ids);
}
