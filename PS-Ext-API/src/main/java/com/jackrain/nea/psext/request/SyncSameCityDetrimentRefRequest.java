package com.jackrain.nea.psext.request;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
@Data
public class SyncSameCityDetrimentRefRequest implements Serializable {
    @NotNull
    @JSONField(name="action_type")
    private  String actionType;
    @NotNull
    @JSONField(name="shop_code")
    private  String shopCode;
    @NotNull
    @JSONField(name="store_code")
    private List<String> storeCode;
    @NotNull
    @JSONField(name="item_code")
    private  String itemCode;
    @NotNull
    @JSONField(name="customerid")
    private String customerId;

}

