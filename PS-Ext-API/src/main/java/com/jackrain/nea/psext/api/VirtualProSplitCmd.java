package com.jackrain.nea.psext.api;

import com.jackrain.nea.psext.model.table.ExtractLuckyBag;
import com.jackrain.nea.psext.request.VirtualProSplitRequest;
import com.jackrain.nea.sys.Command;
import com.jackrain.nea.sys.domain.ValueHolderV14;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR> lin yu
 * @since 2019/7/11
 * create at : 2019/7/11 11:48
 */

public interface VirtualProSplitCmd extends Command {
    /**
     * 组合商品拆分
     * @param virtualProSplitRequest 虚拟条码集合
     * @return
     */
    ValueHolderV14<Map<String, List<ExtractLuckyBag>>> splitVirtualPro(VirtualProSplitRequest virtualProSplitRequest);

    /**
     * 库存不足时随机生成福袋
     *
     * @param virtualProSplitRequest 虚拟条码集合
     * @return ValueHolderV14<Map<String, List<ExtractLuckyBag>>> 福袋
     */
    ValueHolderV14<Map<String, List<ExtractLuckyBag>>> randomlyGenerateLuckyBags(VirtualProSplitRequest virtualProSplitRequest);

    /**
     *
     *
     * @param psCSkuIds 条码集合
     * @return ValueHolderV14<Map<String, Boolean>>
     */
     ValueHolderV14<Map<Long, Boolean>> querySkuIsSplit(List<Long> psCSkuIds);
}
