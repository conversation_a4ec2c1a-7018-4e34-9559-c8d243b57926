package com.jackrain.nea.psext.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

@TableName(value = "ps_c_brand")
@Data
public class PsCBrand extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "ID", type = IdType.INPUT)

    private Long id;

    @JSONField(name = "ECODE")
    private String ecode;

    @J<PERSON><PERSON>ield(name = "ENAME")
    private String ename;

    @JSONField(name = "REMARK")
    private String remark;

    @J<PERSON>NField(name = "MODIFIERENAME")
    private String modifierename;

    @J<PERSON>NField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "PS_C_BRAND_ID")
    private Long psCBrandId;
}