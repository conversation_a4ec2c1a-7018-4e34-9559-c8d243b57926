package com.jackrain.nea.psext.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2019/11/5 15:18
 * desc:
 */
@Data
public class PsMatchSizeItemResult implements Serializable {

    /**
     * 尺寸信息
     */
    @JSONField(name = "PS_C_SPEC2_ID")
    private Long psCSpec2Id;
    @J<PERSON><PERSON>ield(name = "PS_C_SPEC2_ECODE")
    private String psCSpec2Ecode;
    @JSONField(name = "PS_C_SPEC2_ENAME")
    private String psCSpec2Ename;

    /**
     * 数量
     */
    @JSONField(name = "QTY")
    private BigDecimal qty;
}
