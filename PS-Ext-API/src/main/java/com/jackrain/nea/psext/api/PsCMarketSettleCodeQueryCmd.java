package com.jackrain.nea.psext.api;

import com.jackrain.nea.psext.model.request.PsCMarketSettleCodeQueryRequest;
import com.jackrain.nea.psext.model.table.PsCMarketSettleCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;

import java.util.List;

/**
 * <AUTHOR>
 * @version :1.0
 * description ：
 * @date :2019/12/7 14:21
 */
public interface PsCMarketSettleCodeQueryCmd {

    /**
     * 查询 商场结算码
     *
     * @param request request
     * @return list
     */
    ValueHolderV14<List<PsCMarketSettleCode>> query(PsCMarketSettleCodeQueryRequest request);
}
