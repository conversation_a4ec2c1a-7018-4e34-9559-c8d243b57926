package com.jackrain.nea.psext.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@TableName(value = "m_product_category")
@Data
public class MProductCategory {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "AD_CLIENT_ID")
    private Long adClientId;
    @JSONField(name = "AD_ORG_ID")
    private Long adOrgId;
    @JSONField(name = "OWNERID")
    private Long ownerid;
    @JSONField(name = "MODIFIERID")
    private Long modifierid;
    @J<PERSON><PERSON>ield(name = "CREATIONDATE")
    private Date creationdate;
    @JSONField(name = "MODIFIEDDATE")
    private Date modifieddate;

    @JSONField(name = "ISDEFAULT")
    private String isdefault;

    @JSONField(name = "ISSUMMARY")
    private String issummary;

    @JSONField(name = "PARENT_ID")
    private Long parentId;

    @JSONField(name = "VALUE")
    private String value;

    @JSONField(name = "NAME")
    private String name;

    @JSONField(name = "DESCRIPTION")
    private String description;

    @JSONField(name = "EDESCRIPTION")
    private String edescription;

    @JSONField(name = "ATTRIB_ENAME")
    private String attribEname;

    @JSONField(name = "CODE")
    private String code;
}