package com.jackrain.nea.psext.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: 汪聿森
 * @Date: Created in 2019-07-18 10:11
 * @Description : ${description}
 */
@Data
public class SkuVituralInfoRequest implements Serializable {

    //组合数量
    @JSONField(name = "NUM")
    private BigDecimal num;
    //分组编号
    @JSONField(name = "GROUPNUM")
    private Integer groupNum;
    //虚拟条码ID
    @JSONField(name = "ID")
    private Long id;
    //虚拟条码ECODE
    @JSONField(name = "ECODE")
    private String eCode;
    //每组抽取的数量
    @JSONField(name = "GROUP_EXTRACT_NUM")
    private Integer groupExtractNum;
    //组合商品ID
    @JSONField(name = "PS_C_PRO_ID")
    private Long psCProId;

}
