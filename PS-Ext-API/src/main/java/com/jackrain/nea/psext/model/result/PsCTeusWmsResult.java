package com.jackrain.nea.psext.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.psext.model.table.PsCTeusItem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2019/01/02 11:10
 * desc: 传 wms箱码 结果集
 */
@Data
public class PsCTeusWmsResult implements Serializable {

    /**
     * 箱码信息
     */
    @J<PERSON><PERSON>ield(name = "ID")
    private Long id;
    @JSONField(name = "ECODE")
    private String ecode;
    @JSONField(name = "ENAME")
    private String ename;

    @J<PERSON><PERSON>ield(name = "CP_C_CUSTOMER_ORIG_ECODE")
    private String cpCCustomerOrigEcode;

    @JSONField(name = "OC_B_PURCHASE_ORDER_ID")
    private Long ocBPurchaseOrderId;

    @JSONField(name = "OC_B_SEND_OUT_ID")
    private Long ocBSendOutId;

    @JSONField(name = "PS_C_MATCHSIZE_ECODE")
    private String psCMatchsizeEcode;

    @J<PERSON><PERSON>ield(name = "PS_C_PRO_ECODE")
    private String psCProEcode;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_NOW_ID")
    private Long cpCPhyWareHouseNowId;

    /**
     * 备注
     */
    @JSONField(name = "REMARK")
    private String remark;

    /**
     * 明细信息
     */
    List<PsCTeusItem> itemResults;
}
